package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.fake.FakeExecutionLogs;
import pt.jumia.services.brad.domain.entities.filter.executionlog.ExecutionLogFilters;
import pt.jumia.services.brad.domain.entities.filter.executionlog.ExecutionLogSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlExecutionLogRepositoryTest extends BaseRepositoryTest {


    static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");


    private static final ExecutionLog AN_EXECUTION_LOG = FakeExecutionLogs.getFakeExecutionLogs(21).get(20)
            .toBuilder()
            .id(null)
            .executionStartTime(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .executionEndTime(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();
    private static final ExecutionLog ANOTHER_EXECUTION_LOG = FakeExecutionLogs.getFakeExecutionLogs(22).get(21)
            .toBuilder()
            .id(null)
            .executionStartTime(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .executionEndTime(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();

    @SneakyThrows
    @Test
    public void upsertAndFindByIdTest() {
        ExecutionLog executionLog = insertExecutionLog(AN_EXECUTION_LOG);
        Optional<ExecutionLog> foundExecutionLog = executionLogRepository.findById(executionLog.getId());
        assertThat(foundExecutionLog).isPresent();
        assertThat(foundExecutionLog.get()).isEqualTo(executionLog);
    }

    @SneakyThrows
    @Test
    public void findAll_shouldReturnAllExecutionLogs() {
        insertExecutionLog(AN_EXECUTION_LOG);
        insertExecutionLog(ANOTHER_EXECUTION_LOG);

        List<ExecutionLog> executionLogs = executionLogRepository.findAll(null, null, null);
        assertThat(executionLogs).hasSize(2);
    }

    
    @SneakyThrows
    @Test
    public void findAll_withRecordsAmountFilter_shouldReturnExecutionLogs() {
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .recordsAmount(10)
                .build());
        insertExecutionLog(ANOTHER_EXECUTION_LOG.toBuilder()
                .recordsAmount(50)
                .build());

        ExecutionLogFilters filters = ExecutionLogFilters.builder()
                .recordsAmount(50)
                .build();
        List<ExecutionLog> executionLogs = executionLogRepository.findAll(filters, null, null);
        assertThat(executionLogs).hasSize(1);
    }

    @SneakyThrows
    @Test
    public void findAll_withExecutionStartTimeFilter_shouldReturnExecutionLogs() {
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionStartTime(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(1))
                .build());
        insertExecutionLog(ANOTHER_EXECUTION_LOG.toBuilder()
                .executionStartTime(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(5))
                .build());

        ExecutionLogFilters filters = ExecutionLogFilters.builder()
                .executionStartTime(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(1))
                .build();

        List<ExecutionLog> executionLogs = executionLogRepository.findAll(filters, null, null);
        assertThat(executionLogs).hasSize(1);
    }

    @SneakyThrows
    @Test
    public void findAll_withExecutionEndTimeFilter_shouldReturnExecutionLogs() {
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(1))
                .build());
        insertExecutionLog(ANOTHER_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(5))
                .build());

        ExecutionLogFilters filters = ExecutionLogFilters.builder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(1))
                .build();
        List<ExecutionLog> executionLogs = executionLogRepository.findAll(filters, null, null);
        assertThat(executionLogs).hasSize(1);
    }

    @SneakyThrows
    @Test
    public void findAll_withAscSort_returnsExecutionLogsInAscOrder() {
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .recordsAmount(10)
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .recordsAmount(60)
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .recordsAmount(50)
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .recordsAmount(5)
                .build());

        ExecutionLogSortFilters sortFilters = ExecutionLogSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(ExecutionLog.SortingFields.RECORDS_AMOUNT)
                .build();
        List<ExecutionLog> executionLogs = executionLogRepository.findAll(null, sortFilters, null);
        assertThat(executionLogs).hasSize(4);
        assertThat(executionLogs.get(0).getRecordsAmount()).isEqualTo(5);
        assertThat(executionLogs.get(1).getRecordsAmount()).isEqualTo(10);
        assertThat(executionLogs.get(2).getRecordsAmount()).isEqualTo(50);
        assertThat(executionLogs.get(3).getRecordsAmount()).isEqualTo(60);
    }

    @SneakyThrows
    @Test
    public void findAll_withDescSortRecordsAmount_returnsExecutionLogsInDescOrder() {
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .recordsAmount(10)
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .recordsAmount(60)
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .recordsAmount(50)
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .recordsAmount(5)
                .build());

        ExecutionLogSortFilters sortFilters = ExecutionLogSortFilters.builder()
                .direction(OrderDirection.DESC)
                .field(ExecutionLog.SortingFields.RECORDS_AMOUNT)
                .build();
        List<ExecutionLog> executionLogs = executionLogRepository.findAll(null, sortFilters, null);
        assertThat(executionLogs).hasSize(4);
        assertThat(executionLogs.get(0).getRecordsAmount()).isEqualTo(60);
        assertThat(executionLogs.get(1).getRecordsAmount()).isEqualTo(50);
        assertThat(executionLogs.get(2).getRecordsAmount()).isEqualTo(10);
        assertThat(executionLogs.get(3).getRecordsAmount()).isEqualTo(5);
    }

    @SneakyThrows
    @Test
    public void findAll_withAscSortStartDate_returnsExecutionLogsInAscOrder() {
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionStartTime(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(1))
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionStartTime(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(5))
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionStartTime(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(3))
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionStartTime(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(2))
                .build());

        ExecutionLogSortFilters sortFilters = ExecutionLogSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(ExecutionLog.SortingFields.EXECUTION_START_TIME)
                .build();
        List<ExecutionLog> executionLogs = executionLogRepository.findAll(null, sortFilters, null);
        assertThat(executionLogs).hasSize(4);
        assertThat(executionLogs.get(0).getExecutionStartTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(5));
        assertThat(executionLogs.get(1).getExecutionStartTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(3));
        assertThat(executionLogs.get(2).getExecutionStartTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(2));
        assertThat(executionLogs.get(3).getExecutionStartTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(1));

    }

    @SneakyThrows
    @Test
    public void findAll_withDescSortStartDate_returnsExecutionLogsInDescOrder() {
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionStartTime(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(1))
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionStartTime(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(5))
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionStartTime(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(3))
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionStartTime(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(2))
                .build());

        ExecutionLogSortFilters sortFilters = ExecutionLogSortFilters.builder()
                .direction(OrderDirection.DESC)
                .field(ExecutionLog.SortingFields.EXECUTION_START_TIME)
                .build();
        List<ExecutionLog> executionLogs = executionLogRepository.findAll(null, sortFilters, null);
        assertThat(executionLogs).hasSize(4);
        assertThat(executionLogs.get(0).getExecutionStartTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(1));
        assertThat(executionLogs.get(1).getExecutionStartTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(2));
        assertThat(executionLogs.get(2).getExecutionStartTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(3));
        assertThat(executionLogs.get(3).getExecutionStartTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionStartTime().minusDays(5));
    }

    @SneakyThrows
    @Test
    public void findAll_withAscSortEndDate_returnsExecutionLogsInAscOrder() {
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(1))
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(5))
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(3))
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(2))
                .build());

        ExecutionLogSortFilters sortFilters = ExecutionLogSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(ExecutionLog.SortingFields.EXECUTION_END_TIME)
                .build();
        List<ExecutionLog> executionLogs = executionLogRepository.findAll(null, sortFilters, null);
        assertThat(executionLogs).hasSize(4);
        assertThat(executionLogs.get(0).getExecutionEndTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(5));
        assertThat(executionLogs.get(1).getExecutionEndTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(3));
        assertThat(executionLogs.get(2).getExecutionEndTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(2));
        assertThat(executionLogs.get(3).getExecutionEndTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(1));

    }

    @SneakyThrows
    @Test
    public void findAll_withDescSortEndDate_returnsExecutionLogsInDescOrder() {
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(1))
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(5))
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(3))
                .build());
        insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(2))
                .build());

        ExecutionLogSortFilters sortFilters = ExecutionLogSortFilters.builder()
                .direction(OrderDirection.DESC)
                .field(ExecutionLog.SortingFields.EXECUTION_END_TIME)
                .build();
        List<ExecutionLog> executionLogs = executionLogRepository.findAll(null, sortFilters, null);
        assertThat(executionLogs).hasSize(4);
        assertThat(executionLogs.get(0).getExecutionEndTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(1));
        assertThat(executionLogs.get(1).getExecutionEndTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(2));
        assertThat(executionLogs.get(2).getExecutionEndTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(3));
        assertThat(executionLogs.get(3).getExecutionEndTime()).isEqualTo(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(5));
    }

    @SneakyThrows
    @Test
    public void findAll_withSortFieldButNotDirection_returnsExecutionLogsInDefaultDescOrder() {
        ExecutionLog executionLog1 = insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(1))
                .build());
        ExecutionLog executionLog2 = insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(5))
                .build());
        ExecutionLog executionLog3 = insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(3))
                .build());
        ExecutionLog executionLog4 = insertExecutionLog(AN_EXECUTION_LOG.toBuilder()
                .executionEndTime(AN_EXECUTION_LOG.getExecutionEndTime().minusDays(2))
                .build());

        ExecutionLogSortFilters sortFilters = ExecutionLogSortFilters.builder()
                .field(ExecutionLog.SortingFields.EXECUTION_END_TIME)
                .build();

        List<ExecutionLog> executionLogs = executionLogRepository.findAll(null, sortFilters, null);
        assertThat(executionLogs).hasSize(4);

        assertEquals(executionLog1, executionLogs.get(0));
        assertEquals(executionLog4, executionLogs.get(1));
        assertEquals(executionLog3, executionLogs.get(2));
        assertEquals(executionLog2, executionLogs.get(3));

    }

    @SneakyThrows
    @Test
    void deleteTest() {
       ExecutionLog executionLog = executionLogRepository.upsert(AN_EXECUTION_LOG);
        executionLogRepository.deleteById(executionLog.getId());
        Optional<ExecutionLog> foundExecutionLog = executionLogRepository.findById(executionLog.getId());
        assertThat(foundExecutionLog).isEmpty();
    }


}
