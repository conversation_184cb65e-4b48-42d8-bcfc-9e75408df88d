package pt.jumia.services.brad.data.finrecstatements.repository.mssql;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.repository.FinrecStatementRepository;

import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles({"offlineDB", "fakeClients"})
public class MssqlOfflineFinrecStatementsRepositoryTest {

    @Autowired
    protected FinrecStatementRepository finrecStatementRepository;

    @MockBean
    private SchedulerFactoryBean schedulerFactoryBean;

    @Test
    public void testFindAll() {
        assertThrows(DatabaseErrorsException.class, () -> finrecStatementRepository.findAll(null, ExecutionLog.builder().build()));
    }
}
