package pt.jumia.services.brad.data.fxrate.repository.mssql;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.repository.FxRateRepository;

import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles({"offlineDB", "fakeClients"})
public class MssqlOfflineFxRateRepositoryTest {

    @Autowired
    protected FxRateRepository fxRateRepository;

    @MockBean
    private SchedulerFactoryBean schedulerFactoryBean;

    @Test
    public void testFindAll() {
        assertThrows(DatabaseErrorsException.class, () -> fxRateRepository.findAll(null,
                FakeViewEntity.FAKE_VIEW_ENTITY.toBuilder().entityType(ViewEntity.EntityType.FX_RATE).build(),
                false,
                ExecutionLog.builder().build()));
    }

    @Test
    public void testFindByCurrency() {
        assertThrows(DatabaseErrorsException.class, () -> fxRateRepository.findByCurrency(null, null,
                FakeViewEntity.FAKE_VIEW_ENTITY.toBuilder().entityType(ViewEntity.EntityType.FX_RATE).build(),
                false,
                ExecutionLog.builder().build()));
    }
}
