package pt.jumia.services.brad.data.finrecstatements.repository.mssql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.data.brad.repository.psql.BaseRepositoryTest;
import pt.jumia.services.brad.domain.entities.ExecutionLog;

import static org.junit.jupiter.api.Assertions.assertFalse;

@Disabled //TODO Investigate out why it's failing on <PERSON> but passing locally
public class MssqlFinrecStatementsRepositoryTest extends BaseRepositoryTest {
    @SneakyThrows
    @Test
    public void testFindAll() {
        assertFalse(finrecStatementRepository.findAll(null, ExecutionLog.builder().build()).isEmpty());
    }
}
