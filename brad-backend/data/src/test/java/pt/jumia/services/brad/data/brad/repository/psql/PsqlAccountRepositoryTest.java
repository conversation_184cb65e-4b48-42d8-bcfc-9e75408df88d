package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountDailySummaries;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlAccountRepositoryTest extends BaseRepositoryTest {

    static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");
    private static final Account AN_ACCOUNT = FakeAccounts.getFakeAccounts(21, null).get(20)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();
    private static final Account ANOTHER_ACCOUNT = FakeAccounts.getFakeAccounts(22, null).get(21)
            .toBuilder()
            .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
            .build();

    private static final Country A_COUNTRY = FakeCountries.NIGERIA;
    private static final Currency A_CURRENCY = FakeCurrencies.NGN;

    @SneakyThrows
    @Test
    public void insertAndFindTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        Account anotherAccount = insertAccount(ANOTHER_ACCOUNT.toBuilder().country(country).currency(currency).build());

        AccountSortFilters accountSortFilters = AccountSortFilters.builder()
                .direction(OrderDirection.ASC)
                .build();
        List<Account> accounts = accountRepository.findAll(null,
                accountSortFilters, null).stream().map(Account::withoutDbFields).toList();

        assertThat(accounts).containsExactlyInAnyOrder(aAccount.withoutDbFields(), anotherAccount.withoutDbFields());
    }

    @SneakyThrows
    @Test
    public void testFindAll() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        insertAccount(ANOTHER_ACCOUNT.toBuilder().country(country).currency(currency).build());

        AccountSortFilters accountSortFilters = AccountSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(Account.SortingFields.ID)
                .build();
        AccountFilters accountFilters = AccountFilters.builder()
                .companyID(aAccount.getCompanyID())
                .countryCodes(List.of(country.getId()))
                .navReference(aAccount.getNavReference())
                .beneficiaryName(aAccount.getBeneficiaryName())
                .beneficiaryAddress(aAccount.getBeneficiaryAddress())
                .accountNumber(aAccount.getAccountNumber())
                .accountName(aAccount.getAccountName())
                .swiftCode(aAccount.getSwiftCode())
                .bankRoutingCode(aAccount.getBankRoutingCode())
                .sortCode(aAccount.getSortCode())
                .branchCode(aAccount.getBranchCode())
                .rib(aAccount.getRib())
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Account> accounts = accountRepository.findAll(accountFilters,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();

        assertThat(accounts).containsExactly(aAccount.withoutDbFields());

    }

    @SneakyThrows
    @Test
    public void testFindAll2() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account aAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        Account anotherAccount = insertAccount(ANOTHER_ACCOUNT.toBuilder().country(country).currency(currency).build());

        AccountSortFilters accountSortFilters = AccountSortFilters.builder()
                .direction(OrderDirection.DESC)
                .field(Account.SortingFields.ID)
                .build();
        AccountFilters accountFilters = AccountFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Account> accounts = accountRepository.findAll(accountFilters,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();

        assertThat(accounts).containsExactly(anotherAccount.withoutDbFields(), aAccount.withoutDbFields());

    }

    @SneakyThrows
    @Test
    public void findByAccountNumber() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account insertedAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Optional<Account> optionalAccount = accountRepository.findByAccountNumber(
                insertedAccount.getAccountNumber()
        ).map(Account::withoutDbFields);

        assertThat(optionalAccount).contains(insertedAccount.withoutDbFields());
    }

    @SneakyThrows
    @Test
    public void findByIdTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account insertedAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        assert insertedAccount.getId() != null;
        Optional<Account> optionalAccount = accountRepository.findById(insertedAccount.getId())
                .map(Account::withoutDbFields);

        assertThat(optionalAccount).contains(insertedAccount.withoutDbFields());
    }

    @SneakyThrows
    @Test
    public void findByFilterTextTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        insertAccount(ANOTHER_ACCOUNT.toBuilder().country(country).currency(currency).build());

        AccountSortFilters accountSortFilters = AccountSortFilters.builder()
                .direction(OrderDirection.DESC)
                .field(Account.SortingFields.ID)
                .build();
        AccountFilters accountFilters = AccountFilters.builder().filterText("fakeBankName21")
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Account> accounts = accountRepository.findAll(accountFilters,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();

        assertThat(accounts).size().isEqualTo(1);

    }


    @SneakyThrows
    @Test
    public void findByFilterTextWithNoDataTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        insertAccount(ANOTHER_ACCOUNT.toBuilder().country(country).currency(currency).build());

        AccountSortFilters accountSortFilters = AccountSortFilters.builder()
                .direction(OrderDirection.DESC)
                .field(Account.SortingFields.ID)
                .build();
        AccountFilters accountFilters = AccountFilters.builder().filterText("fakeBankName1")
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Account> accounts = accountRepository.findAll(accountFilters,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();

        assertThat(accounts).size().isEqualTo(0);

    }

    @SneakyThrows
    @Test
    public void findByinvestmentIdTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account accountWithIsin = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .isin("ISIN123")
                .contractId("CONTRACT_OTHER")
                .build());
        Account accountWithContractId = insertAccount(ANOTHER_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .isin("ISIN_OTHER")
                .contractId("CONTRACT456")
                .build());

        AccountSortFilters accountSortFilters = AccountSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(Account.SortingFields.ID)
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        // Test filtering by ISIN
        AccountFilters accountFiltersIsin = AccountFilters.builder().investmentId("ISIN123").build();
        List<Account> accountsIsin = accountRepository.findAll(accountFiltersIsin,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();
        assertThat(accountsIsin).containsExactly(accountWithIsin.withoutDbFields());

        // Test filtering by ContractId
        AccountFilters accountFiltersContractId = AccountFilters.builder().investmentId("CONTRACT456").build();
        List<Account> accountsContractId = accountRepository.findAll(accountFiltersContractId,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();
        assertThat(accountsContractId).containsExactly(accountWithContractId.withoutDbFields());

        // Test filtering with no match
        AccountFilters accountFiltersNoMatch = AccountFilters.builder().investmentId("NOMATCH").build();
        List<Account> accountsNoMatch = accountRepository.findAll(accountFiltersNoMatch,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();
        assertThat(accountsNoMatch).isEmpty();
    }


    @SneakyThrows
    @Test
    public void updateTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account insertedAccount = insertAccount(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());

        Account toUpdate = insertedAccount.toBuilder()
                .accountName("New Bank")
                .build();

        Account updatedAccount = accountRepository.upsert(toUpdate);
        assert insertedAccount.getId() != null;
        Optional<Account> optionalAccount = accountRepository.findById(insertedAccount.getId())
                .map(Account::withoutDbFields);

        assertThat(optionalAccount).contains(updatedAccount.withoutDbFields());
        assertEquals(toUpdate.withoutDbFields(), updatedAccount.withoutDbFields());
    }

    @SneakyThrows
    @Test
    public void deleteTest() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        Account insertedAccount = accountRepository.upsert(AN_ACCOUNT.toBuilder().country(country).currency(currency).build());
        assert insertedAccount.getId() != null;
        assertThat(accountRepository.findById(insertedAccount.getId()).map(Account::withoutDbFields))
                .contains(insertedAccount.withoutDbFields());

        accountRepository.deleteById(insertedAccount.getId());
        assertThat(accountRepository.findById(insertedAccount.getId())).isEmpty();
    }

    @SneakyThrows
    @Test
    public void testFindAllWithNegativeBalanceTrue() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());

        Account accountWithNegativeBalance = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG123")
                .build());

        Account accountWithPositiveBalance = insertAccount(ANOTHER_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("POS456")
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(accountWithNegativeBalance)
                .finalBalanceUsd(new BigDecimal("-100.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(accountWithPositiveBalance)
                .finalBalanceUsd(new BigDecimal("500.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        AccountSortFilters accountSortFilters = AccountSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(Account.SortingFields.ID)
                .build();
        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Account> accounts = accountRepository.findAll(accountFilters,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();

        assertThat(accounts).hasSize(1);
        assertThat(accounts.get(0).getAccountNumber()).isEqualTo("NEG123");
    }

    @SneakyThrows
    @Test
    public void testFindAllWithNegativeBalanceFalse() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());

        Account accountWithNegativeBalance = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG123")
                .build());

        Account accountWithPositiveBalance = insertAccount(ANOTHER_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("POS456")
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(accountWithNegativeBalance)
                .finalBalanceUsd(new BigDecimal("-100.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(accountWithPositiveBalance)
                .finalBalanceUsd(new BigDecimal("500.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        AccountSortFilters accountSortFilters = AccountSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(Account.SortingFields.ID)
                .build();
        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(false)
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Account> accounts = accountRepository.findAll(accountFilters,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();

        assertThat(accounts).hasSize(2);
        assertThat(accounts.stream().map(Account::getAccountNumber))
                .containsExactlyInAnyOrder("NEG123", "POS456");
    }

    @SneakyThrows
    @Test
    public void testFindAllWithZeroBalance() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());

        Account accountWithZeroBalance = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("ZERO123")
                .build());

        Account accountWithNegativeBalance = insertAccount(ANOTHER_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG456")
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(accountWithZeroBalance)
                .finalBalanceUsd(BigDecimal.ZERO)
                .fxRate(null)
                .currency(currency)
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(accountWithNegativeBalance)
                .finalBalanceUsd(new BigDecimal("-50.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        AccountSortFilters accountSortFilters = AccountSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(Account.SortingFields.ID)
                .build();

        AccountFilters accountFiltersNegativeTrue = AccountFilters.builder()
                .isNegativeBalance(true)
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Account> negativeAccounts = accountRepository.findAll(accountFiltersNegativeTrue,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();

        assertThat(negativeAccounts).hasSize(1);
        assertThat(negativeAccounts.get(0).getAccountNumber()).isEqualTo("NEG456");

        AccountFilters accountFiltersNegativeFalse = AccountFilters.builder()
                .isNegativeBalance(false)
                .build();

        List<Account> allAccounts = accountRepository.findAll(accountFiltersNegativeFalse,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();

        assertThat(allAccounts).hasSize(2);
        assertThat(allAccounts.stream().map(Account::getAccountNumber))
                .containsExactlyInAnyOrder("ZERO123", "NEG456");
    }

    @SneakyThrows
    @Test
    public void testFindAllWithMultipleNegativeBalanceAccounts() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());

        Account firstNegativeAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG001")
                .build());

        Account secondNegativeAccount = insertAccount(ANOTHER_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG002")
                .navReference("NAV002")
                .build());

        Account positiveAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("POS001")
                .navReference("NAVPOS")
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(firstNegativeAccount)
                .finalBalanceUsd(new BigDecimal("-100.50"))
                .fxRate(null)
                .currency(currency)
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(secondNegativeAccount)
                .finalBalanceUsd(new BigDecimal("-250.75"))
                .fxRate(null)
                .currency(currency)
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(positiveAccount)
                .finalBalanceUsd(new BigDecimal("1000.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        AccountSortFilters accountSortFilters = AccountSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(Account.SortingFields.ID)
                .build();
        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Account> accounts = accountRepository.findAll(accountFilters,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();

        assertThat(accounts).hasSize(2);
        assertThat(accounts.stream().map(Account::getAccountNumber))
                .containsExactlyInAnyOrder("NEG001", "NEG002");
    }    @SneakyThrows
    @Test
    public void testFindAllWithNoAccountDailySummaryData() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        
        insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NOSUMMARY123")
                .build());

        AccountSortFilters accountSortFilters = AccountSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(Account.SortingFields.ID)
                .build();
        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Account> accounts = accountRepository.findAll(accountFilters,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();

        assertThat(accounts).isEmpty();
    }

    @SneakyThrows
    @Test
    public void testFindAllNegativeBalanceWithOtherFilters() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());

        Account negativeAccountMatchingFilters = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG123")
                .companyID("COMPANY1")
                .build());

        Account negativeAccountNotMatchingFilters = insertAccount(ANOTHER_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG456")
                .companyID("COMPANY2")
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(negativeAccountMatchingFilters)
                .finalBalanceUsd(new BigDecimal("-100.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(negativeAccountNotMatchingFilters)
                .finalBalanceUsd(new BigDecimal("-200.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        AccountSortFilters accountSortFilters = AccountSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(Account.SortingFields.ID)
                .build();
        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .companyID("COMPANY1")
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Account> accounts = accountRepository.findAll(accountFilters,
                accountSortFilters, pageFilters).stream().map(Account::withoutDbFields).toList();

        assertThat(accounts).hasSize(1);
        assertThat(accounts.get(0).getAccountNumber()).isEqualTo("NEG123");
        assertThat(accounts.get(0).getCompanyID()).isEqualTo("COMPANY1");
    }

    @SneakyThrows
    @Test
    public void testCountWithNegativeBalanceTrue() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());

        Account accountWithNegativeBalance = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG123")
                .build());

        Account accountWithPositiveBalance = insertAccount(ANOTHER_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("POS456")
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(accountWithNegativeBalance)
                .finalBalanceUsd(new BigDecimal("-100.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(accountWithPositiveBalance)
                .finalBalanceUsd(new BigDecimal("500.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .build();

        Integer count = accountRepository.count(accountFilters);

        assertThat(count).isEqualTo(1);
    }

    @SneakyThrows
    @Test
    public void testCountWithNegativeBalanceFalse() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());

        Account accountWithNegativeBalance = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG123")
                .build());

        Account accountWithPositiveBalance = insertAccount(ANOTHER_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("POS456")
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(accountWithNegativeBalance)
                .finalBalanceUsd(new BigDecimal("-100.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(accountWithPositiveBalance)
                .finalBalanceUsd(new BigDecimal("500.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(false)
                .build();

        Integer count = accountRepository.count(accountFilters);

        assertThat(count).isEqualTo(2);
    }

    @SneakyThrows
    @Test
    public void testCountWithMultipleNegativeBalanceAccounts() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());

        Account firstNegativeAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG001")
                .build());

        Account secondNegativeAccount = insertAccount(ANOTHER_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG002")
                .navReference("NAV002")
                .build());

        Account positiveAccount = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("POS001")
                .navReference("NAVPOS")
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(firstNegativeAccount)
                .finalBalanceUsd(new BigDecimal("-100.50"))
                .fxRate(null)
                .currency(currency)
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(secondNegativeAccount)
                .finalBalanceUsd(new BigDecimal("-250.75"))
                .fxRate(null)
                .currency(currency)
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(positiveAccount)
                .finalBalanceUsd(new BigDecimal("1000.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .build();

        Integer count = accountRepository.count(accountFilters);

        assertThat(count).isEqualTo(2);
    }

    @SneakyThrows
    @Test
    public void testCountWithNegativeBalanceAndOtherFilters() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());

        Account negativeAccountMatchingFilters = insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG123")
                .companyID("COMPANY1")
                .build());

        Account negativeAccountNotMatchingFilters = insertAccount(ANOTHER_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NEG456")
                .companyID("COMPANY2")
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(negativeAccountMatchingFilters)
                .finalBalanceUsd(new BigDecimal("-100.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        insert(FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now())
                .account(negativeAccountNotMatchingFilters)
                .finalBalanceUsd(new BigDecimal("-200.00"))
                .fxRate(null)
                .currency(currency)
                .build());

        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .companyID("COMPANY1")
                .build();

        Integer count = accountRepository.count(accountFilters);

        assertThat(count).isEqualTo(1);
    }

    @SneakyThrows
    @Test
    public void testCountWithNoAccountDailySummaryData() {
        Currency currency = insertCurrency(A_CURRENCY);
        Country country = insertCountry(A_COUNTRY.toBuilder().currency(currency).build());
        
        insertAccount(AN_ACCOUNT.toBuilder()
                .country(country)
                .currency(currency)
                .accountNumber("NOSUMMARY123")
                .build());

        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .build();

        Integer count = accountRepository.count(accountFilters);

        assertThat(count).isEqualTo(0);
    }
}
