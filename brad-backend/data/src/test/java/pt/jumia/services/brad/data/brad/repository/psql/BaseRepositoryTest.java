package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import pt.jumia.services.brad.data.brad.entities.AccountPsql;
import pt.jumia.services.brad.data.brad.entities.keys.AccountDailySummaryId;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaAccountStatementFileRepository;
import pt.jumia.services.brad.domain.Profiles;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.ApiLog;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.Setting;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.AccountDailySummary;
import pt.jumia.services.brad.domain.entities.account.Contact;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.account.Document;
import pt.jumia.services.brad.domain.entities.account.User;
// TODO: BatchProcessingConfig removed - now handled by Spring Batch
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.repository.FinrecStatementRepository;
import pt.jumia.services.brad.domain.repository.FxRateRepository;
import pt.jumia.services.brad.domain.repository.brad.AccountDailySummaryRepository;
import pt.jumia.services.brad.domain.repository.brad.AccountRepository;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementFileRepository;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementRepository;
import pt.jumia.services.brad.domain.repository.brad.ApiLogRepository;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.repository.brad.BradFxRateRepository;
import pt.jumia.services.brad.domain.repository.brad.ContactRepository;
import pt.jumia.services.brad.domain.repository.brad.CountryRepository;
import pt.jumia.services.brad.domain.repository.brad.CurrencyRepository;
import pt.jumia.services.brad.domain.repository.brad.DocumentRepository;
import pt.jumia.services.brad.domain.repository.brad.ExecutionLogRepository;
import pt.jumia.services.brad.domain.repository.brad.JobsRepository;
import pt.jumia.services.brad.domain.repository.brad.ReconciliationRepository;
import pt.jumia.services.brad.domain.repository.brad.SettingRepository;
import pt.jumia.services.brad.domain.repository.brad.TransactionRepository;
import pt.jumia.services.brad.domain.repository.brad.UserRepository;
import pt.jumia.services.brad.domain.repository.brad.ViewEntityRepository;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Collections;
import java.util.List;
import java.util.Stack;
import java.util.concurrent.CompletableFuture;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles({Profiles.FAKE_CLIENTS, Profiles.TEST})
public class BaseRepositoryTest {

    private final Stack<Long> accountsToDelete = new Stack<>();
    private final Stack<Long> contactsToDelete = new Stack<>();
    private final Stack<Long> usersToDelete = new Stack<>();
    private final Stack<Long> documentsToDelete = new Stack<>();
    private final Stack<Long> balesToDelete = new Stack<>();
    private final Stack<Integer> reconciliationsToDelete = new Stack<>();
    private final Stack<Long> countriesToDelete = new Stack<>();
    private final Stack<Long> accountStatementsAccountIDsToDelete = new Stack<>();
    private final Stack<Long> transactionsToDelete = new Stack<>();
    private final Stack<Long> apiLogsToDelete = new Stack<>();
    private final Stack<Long> currenciesToDelete = new Stack<>();
    private final Stack<Long> executionLogsToDelete = new Stack<>();
    private final Stack<Long> baleViewEntityToDelete = new Stack<>();
    private final Stack<Integer> fxRatesToDelete = new Stack<>();
    private final Stack<Long> accountStatementFilesToDelete = new Stack<>();
    private final Stack<Long> settingsToDelete = new Stack<>();
    private final Stack<AccountDailySummaryId> accountDailySummariesToDelete = new Stack<>();


    @Autowired
    protected AccountRepository accountRepository;

    @Autowired
    protected ContactRepository contactRepository;

    @Autowired
    protected UserRepository userRepository;

    @Autowired
    protected DocumentRepository documentRepository;

    @Autowired
    protected BradBaleRepository bradBaleRepository;

    @Autowired
    protected ReconciliationRepository reconciliationRepository;

    @Autowired
    protected BaleRepository baleRepository;

    @Autowired
    protected FinrecStatementRepository finrecStatementRepository;

    @Autowired
    protected FxRateRepository fxRateRepository;

    @Autowired
    protected JobsRepository jobsRepository;

    @Autowired
    protected CountryRepository countryRepository;

    @Autowired
    protected AccountStatementRepository accountStatementRepository;

    @Autowired
    protected TransactionRepository transactionRepository;

    @Autowired
    protected ApiLogRepository apiLogRepository;

    @Autowired
    protected CurrencyRepository currencyRepository;

    @Autowired
    protected ExecutionLogRepository executionLogRepository;

    @Autowired
    protected BradFxRateRepository bradFxRateRepository;

    @Autowired
    protected ViewEntityRepository baleViewEntityRepository;

    @Autowired
    protected AccountStatementFileRepository accountStatementFileRepository;

    @Autowired
    protected JpaAccountStatementFileRepository jpaAccountStatementFileRepository;

    @Autowired
    protected SettingRepository settingRepository;

    @Autowired
    protected AccountDailySummaryRepository accountDailySummaryRepository;

    @MockBean
    SchedulerFactoryBean schedulerFactoryBean;

    // TODO: BatchProcessingConfig MockBean removed - now handled by Spring Batch


    @BeforeEach
    public void superSetup() {
        executionLogRepository.deleteAll();
        apiLogRepository.deleteAll();
    }

    @AfterEach
    public void baseTearDown() throws ParseException {
        while (!accountDailySummariesToDelete.empty()) {
            final AccountDailySummaryId top = accountDailySummariesToDelete.pop();
            accountDailySummaryRepository.deleteById(top.getTransactionDate(), top.getAccount().toEntity());
        }
        while(!baleViewEntityToDelete.empty()){
            baleViewEntityRepository.deleteById(baleViewEntityToDelete.pop());
        }
        while (!reconciliationsToDelete.empty()) {
            reconciliationRepository.delete(reconciliationsToDelete.pop());
        }
        while (!contactsToDelete.empty()) {
            contactRepository.deleteById(contactsToDelete.pop());
        }
        while (!usersToDelete.empty()) {
            userRepository.deleteById(usersToDelete.pop());
        }
        while (!documentsToDelete.empty()) {
            documentRepository.deleteById(documentsToDelete.pop());
        }
        while (!transactionsToDelete.empty()) {
            transactionRepository.deleteById(transactionsToDelete.pop());
        }
        while (!accountStatementsAccountIDsToDelete.empty()) {
            Long accountId = accountStatementsAccountIDsToDelete.pop();
            this.deleteAccountStatementsByAccountID(accountId);
        }
        while (!balesToDelete.empty()) {
            bradBaleRepository.deleteById(balesToDelete.pop());
        }
        while (!accountStatementFilesToDelete.empty()) {
            jpaAccountStatementFileRepository.deleteById(accountStatementFilesToDelete.pop());
        }
        while (!accountsToDelete.empty()) {
            accountRepository.deleteById(accountsToDelete.pop());
        }
        while (!countriesToDelete.empty()) {
            countryRepository.deleteById(countriesToDelete.pop());
        }
        while (!apiLogsToDelete.empty()) {
            apiLogRepository.deleteById(apiLogsToDelete.pop());
        }
        while (!fxRatesToDelete.empty()) {
            bradFxRateRepository.deleteById(fxRatesToDelete.pop());
        }
        while (!currenciesToDelete.empty()) {
            currencyRepository.deleteById(currenciesToDelete.pop());
        }
        while (!executionLogsToDelete.empty()) {
            executionLogRepository.deleteById(executionLogsToDelete.pop());
        }
        while (!settingsToDelete.empty()) {
            settingRepository.deleteById(settingsToDelete.pop());
        }

    }


    protected User insertUser(User user) {
        User createdUser = userRepository.upsert(user);
        usersToDelete.push(createdUser.getId());
        return createdUser;
    }

    protected Document insertDocument(Document document) {
        Document createdDocument = documentRepository.upsert(document);
        documentsToDelete.push(createdDocument.getId());
        return createdDocument;
    }

    @SneakyThrows
    protected Account insertAccount(Account account) {
        Account createdAccount = accountRepository.upsert(account);
        accountsToDelete.push(createdAccount.getId());
        accountStatementsAccountIDsToDelete.push(createdAccount.getId());
        return createdAccount;
    }

    protected AccountStatement insertAccountStatement(AccountStatement accountStatement) {
        return accountStatementRepository.upsert(accountStatement);
    }

    protected Transaction insertTransaction(Transaction transaction) {

        assert transaction.getAccountStatement().getAccount().getId() != null;

        Transaction createdTransaction = CompletableFuture.supplyAsync(() ->
            transactionRepository.insert(transaction, "test").join()).join();

        transactionsToDelete.push(createdTransaction.getId());
        return createdTransaction;
    }


    protected Contact insertContact(Contact contact) {
        Contact createdContact = contactRepository.upsert(contact);
        contactsToDelete.push(createdContact.getId());
        return createdContact;
    }

    protected List<Bale> insertBales(List<Bale> baleList) throws DatabaseErrorsException {
        List<Bale> createdBales = bradBaleRepository.sync(baleList);
        for (Bale bale : createdBales) {
            balesToDelete.push(bale.getId());
        }
        return createdBales;
    }

    protected ViewEntity insertBaleViewEntity(ViewEntity baleViewEntity) {
        ViewEntity createdBaleViewEntity = baleViewEntityRepository.upsert(baleViewEntity);
        baleViewEntityToDelete.push(createdBaleViewEntity.getId());
        return createdBaleViewEntity;
    }

    protected Reconciliation insertReconciliations(Reconciliation reconciliation,
                                                   BigDecimal amountTransaction,
                                                   BigDecimal amountBale,
                                                   BigDecimal amountThreshold,
                                                   Account account) {
        Reconciliation createdReconciliation = reconciliationRepository.insert(
                reconciliation,
                amountTransaction,
                amountBale,
                amountThreshold,
            account
        );
        reconciliationsToDelete.push(createdReconciliation.getId());
        return createdReconciliation;
    }

    protected Country insertCountry(Country country) {
        Country createdCountry = countryRepository.upsert(country);
        countriesToDelete.push(createdCountry.getId());
        return createdCountry;
    }

    protected ApiLog insertApiLog(ApiLog apiLog) {
        ApiLog createdApiLog = apiLogRepository.upsert(apiLog);
        apiLogsToDelete.push(createdApiLog.getId());
        return createdApiLog;
    }

    protected List<FxRate> insertFxRate(List<FxRate> fxRate) throws DatabaseErrorsException {
        List<FxRate> createdFxRate = bradFxRateRepository.sync(fxRate);
        for (FxRate fxRate1 : createdFxRate) {
            fxRatesToDelete.push(fxRate1.getId());
        }
        return createdFxRate;
    }

    protected Currency insertCurrency(Currency currency) {
        Currency createdCurrency = currencyRepository.upsert(currency);
        currenciesToDelete.push(createdCurrency.getId());
        return createdCurrency;
    }
    protected ExecutionLog insertExecutionLog(ExecutionLog executionLog) {
        ExecutionLog createdExecutionLog = executionLogRepository.upsert(executionLog);
        executionLogsToDelete.push(createdExecutionLog.getId());
        return createdExecutionLog;
    }
    protected AccountStatementFile insertAccountStatementFile(AccountStatementFile accountStatementFile) {
        AccountStatementFile insertedAccountStatementFile = accountStatementFileRepository.upsert(accountStatementFile);
        accountStatementFilesToDelete.push(insertedAccountStatementFile.getId());
        return insertedAccountStatementFile;
    }


    protected List<AccountStatement> getOrderedListOfAccountStatements(AccountStatementFilters accountStatementFilters) {
        return accountStatementRepository.findAllStatementsOrdered(accountStatementFilters);
    }

    private void deleteAccountStatementsByAccountID(Long accountId) {
        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .status(AccountStatementStatus.getValues())
                .accountID(accountId).build();
        List<AccountStatement> accountStatements = getOrderedListOfAccountStatements(accountStatementFilters);

        Collections.reverse(accountStatements);
        for (AccountStatement accountStatement : accountStatements) {
            accountStatementRepository.deleteById(accountStatement.getId());
        }
    }

    protected Setting insert(Setting setting) {

        Setting createdSetting = settingRepository.upsert(setting);
        settingsToDelete.push(createdSetting.getId());
        return createdSetting;
    }

    protected AccountDailySummary insert(AccountDailySummary accountDailySummary) {

        AccountDailySummary createdSummary = accountDailySummaryRepository.upsert(accountDailySummary);
        AccountDailySummaryId id = new AccountDailySummaryId(createdSummary.getTransactionDate(),
            new AccountPsql(createdSummary.getAccount()));
        accountDailySummariesToDelete.push(id);
        return createdSummary;
    }

}
