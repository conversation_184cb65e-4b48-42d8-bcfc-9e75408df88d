package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.AccountDailySummary;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.dtos.GroupedAccountDailySummaryDto;
import pt.jumia.services.brad.domain.entities.dtos.StackedCashPositionDto;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountDailySummaries;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashEvolutionFilters;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashEvolutionFilters.AggregateBy;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashPositionFilters;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CommonFilters;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CommonFilters.GroupBy;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PsqlAccountDailySummaryRepositoryTest extends BaseRepositoryTest {

    static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");

    private static final Account A_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0)
        .toBuilder()
        .createdAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
        .updatedAt(LocalDateTime.parse("2023-04-19T17:58:03.687298", formatter))
        .build();

    private static final Country NIGERIA = FakeCountries.NIGERIA;
    private static final Currency NGN = FakeCurrencies.NGN;
    private static final Currency USD = FakeCurrencies.USD;

    @SneakyThrows
    @Test
    public void whenSummaryIsFetched_givenSummaryIsInserted_thenSummaryIsFoundInDb() {
        // GIVEN
        Currency ngn = insertCurrency(NGN);
        Country country = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(ngn).build());

        AccountDailySummary dailySummaryToInsert = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .account(aAccount)
            .fxRate(null)
            .currency(ngn)
            .build();

        //WHEN
        AccountDailySummary savedSummary = insert(dailySummaryToInsert);
        Optional<AccountDailySummary> fetchedSummary = accountDailySummaryRepository.findById(dailySummaryToInsert.getTransactionDate(),
            dailySummaryToInsert.getAccount());

        //THEN
        assertNotNull(savedSummary);
        assertTrue(fetchedSummary.isPresent());
        assertEquals(savedSummary.getTransactionDate(), fetchedSummary.get().getTransactionDate());
        assertEquals(savedSummary.getAccount().getId(), fetchedSummary.get().getAccount().getId());
    }

    @SneakyThrows
    @Test
    public void whenFindLatestByAccountId_givenMultipleSummariesExist_thenReturnsLatestSummaryAndDate() {
        // GIVEN
        Currency ngn = insertCurrency(NGN);
        Country country = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(ngn).build());

        AccountDailySummary olderSummary = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.now().minusDays(2))
            .account(aAccount)
            .fxRate(null)
            .currency(ngn)
            .build();

        AccountDailySummary newerSummary = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.now().minusDays(1))
            .account(aAccount)
            .fxRate(null)
            .currency(ngn)
            .build();

        // WHEN
        AccountDailySummary savedOlderSummary = insert(olderSummary);
        AccountDailySummary savedNewerSummary = insert(newerSummary);
        Optional<AccountDailySummary> latestSummary = accountDailySummaryRepository.findLatestByAccountIdAndDate(aAccount.getId(), LocalDate.now());

        // THEN
        assertTrue(latestSummary.isPresent());
        assertEquals(newerSummary.getTransactionDate(), latestSummary.get().getTransactionDate());
    }

    @SneakyThrows
    @Test
    public void whenFindOldestMissingFxRate_givenSummariesWithoutFxRateExist_thenReturnsOldestSummary() {
        // GIVEN
        Currency ngn = insertCurrency(NGN);
        Country country = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(ngn).build());

        AccountDailySummary olderSummary = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.now().minusDays(2))
            .account(aAccount)
            .fxRate(null)
            .currency(ngn)
            .build();

        AccountDailySummary newerSummary = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.now().minusDays(1))
            .account(aAccount)
            .fxRate(null)
            .currency(ngn)
            .build();

        // WHEN
        AccountDailySummary savedOlderSummary = insert(olderSummary);
        AccountDailySummary savedNewerSummary = insert(newerSummary);
        Optional<AccountDailySummary> oldestMissingFxRate = accountDailySummaryRepository.findOldestMissingFxRate(aAccount.getId());

        // THEN
        assertTrue(oldestMissingFxRate.isPresent());
        assertEquals(olderSummary.getTransactionDate(), oldestMissingFxRate.get().getTransactionDate());
    }

    @SneakyThrows
    @Test
    public void whenDeleteByDates_givenSummariesExist_thenDeletesAllRelatedSummaries() {
        // GIVEN
        Currency ngn = insertCurrency(NGN);
        Country country = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(ngn).build());

        AccountDailySummary summary1 = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.now().minusDays(1))
            .account(aAccount)
            .fxRate(null)
            .currency(ngn)
            .build();

        AccountDailySummary summary2 = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.now().minusDays(2))
            .account(aAccount)
            .fxRate(null)
            .currency(ngn)
            .build();

        // WHEN
        AccountDailySummary savedSummary1 = insert(summary1);
        AccountDailySummary savedSummary2 = insert(summary2);
        accountDailySummaryRepository.deleteByDates(aAccount.getId(), savedSummary2.getTransactionDate(), savedSummary1.getTransactionDate());
        Optional<AccountDailySummary> fetchedSummary1 = accountDailySummaryRepository.findById(summary1.getTransactionDate(),
            summary1.getAccount());
        Optional<AccountDailySummary> fetchedSummary2 = accountDailySummaryRepository.findById(summary2.getTransactionDate(),
            summary2.getAccount());

        // THEN
        assertFalse(fetchedSummary1.isPresent());
        assertFalse(fetchedSummary2.isPresent());
    }

    @SneakyThrows
    @Test
    public void whenSummaryisFetched_givenSummaryIsInserted_thenSummaryIsFoundInDb() {
        // GIVEN
        Currency ngn = insertCurrency(NGN);
        Country country = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(ngn).build());

        AccountDailySummary dailySummaryToInsert = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .account(aAccount)
            .fxRate(null)
            .currency(ngn)
            .build();

        //WHEN
        AccountDailySummary savedSummary = insert(dailySummaryToInsert);
        Optional<AccountDailySummary> fetchedSummary = accountDailySummaryRepository.findById(dailySummaryToInsert.getTransactionDate(),
            dailySummaryToInsert.getAccount());

        //THEN
        assertNotNull(savedSummary);
        assertTrue(fetchedSummary.isPresent());

    }

    @SneakyThrows
    @Test
    public void whenSummaryisDeleted_givenSummaryIsInserted_thenSummaryIsDeletedFromDb() {
        // GIVEN
        Currency ngn = insertCurrency(NGN);
        Country country = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(ngn).build());

        AccountDailySummary dailySummaryToInsert = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .account(aAccount)
            .fxRate(null)
            .currency(ngn)
            .build();

        //WHEN
        AccountDailySummary savedSummary = insert(dailySummaryToInsert);
        accountDailySummaryRepository.deleteById(dailySummaryToInsert.getTransactionDate(), dailySummaryToInsert.getAccount());
        Optional<AccountDailySummary> fetchedSummary = accountDailySummaryRepository.findById(dailySummaryToInsert.getTransactionDate(),
            dailySummaryToInsert.getAccount());

        //THEN
        assertNotNull(savedSummary);
        assertFalse(fetchedSummary.isPresent());

    }

    @SneakyThrows
    @Test
    public void whenCashEvolutionIsRequested_givenSummaryIsInserted_thenSummaryIsReturnedAppropriately() {
        // GIVEN
        Currency ngn = insertCurrency(NGN);
        Country country = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(ngn).build());

        AccountDailySummary dailySummaryToInsert = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .account(aAccount)
            .initialBalanceUsd(BigDecimal.TEN)
            .finalBalanceUsd(BigDecimal.TEN)
            .fxRate(null)
            .currency(ngn)
            .build();
        CashEvolutionFilters cashEvolutionFilters = CashEvolutionFilters.builder()
            .evolutionFromDate(dailySummaryToInsert.getTransactionDate().minusDays(1))
            .evolutionToDate(dailySummaryToInsert.getTransactionDate().plusDays(1))
            .isAggregatedByPeriod(true)
            .build();

        //WHEN
        AccountDailySummary savedSummary = insert(dailySummaryToInsert);

        final List<GroupedAccountDailySummaryDto> result = accountDailySummaryRepository.findCashEvolution(cashEvolutionFilters);

        //THEN
        assertNotNull(savedSummary);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(savedSummary.getFinalBalanceUsd().setScale(2, RoundingMode.HALF_UP), result.get(0).getFinalBalanceUsd());
        assertEquals(savedSummary.getTransactionDate().toString(), result.get(0).getGroupLabel());

    }

    @SneakyThrows
    @Test
    public void whenCashEvolutionIsRequested_givenMultipleSummariesAreInserted_thenSummaryIsReturnedAppropriately() {
        // GIVEN
        Currency ngn = insertCurrency(NGN);
        Country country = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(ngn).build());

        AccountDailySummary dailySummaryToInsert0 = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.of(2023, 1, 1))
            .account(aAccount)
            .fxRate(null)
            .currency(ngn)
            .build();

        AccountDailySummary dailySummaryToInsert1 = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.of(2024, 1, 12))
            .account(aAccount)
            .initialBalanceUsd(BigDecimal.TEN)
            .finalBalanceUsd(BigDecimal.TEN)
            .fxRate(null)
            .currency(ngn)
            .build();

        AccountDailySummary dailySummaryToInsert2 = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.of(2024, 12, 12))
            .account(aAccount)
            .initialBalanceUsd(BigDecimal.TEN)
            .finalBalanceUsd(BigDecimal.TEN)
            .fxRate(null)
            .currency(ngn)
            .build();

        CashEvolutionFilters cashEvolutionFilters = CashEvolutionFilters.builder()
            .evolutionAggregateBy(AggregateBy.YEARLY)
            .evolutionFromDate(LocalDate.of(2024, 1, 1))
            .evolutionToDate(LocalDate.of(2024, 12, 12))
            .isAggregatedByPeriod(true)
            .build();

        //WHEN
        AccountDailySummary savedSummary0 = insert(dailySummaryToInsert0);
        AccountDailySummary savedSummary1 = insert(dailySummaryToInsert1);
        AccountDailySummary savedSummary2 = insert(dailySummaryToInsert2);

        final List<GroupedAccountDailySummaryDto> result = accountDailySummaryRepository.findCashEvolution(cashEvolutionFilters);

        //THEN
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(savedSummary2.getFinalBalanceUsd().setScale(2, RoundingMode.HALF_UP), result.get(0).getFinalBalanceUsd());

        assertEquals(savedSummary0.getFinalBalanceUsd().setScale(2, RoundingMode.HALF_UP), result.get(0).getFinalBalanceUsd());

        assertEquals("2024", result.get(0).getGroupLabel());

    }

    @SneakyThrows
    @Test
    public void whenFindCashPosition_givenValidFilters_thenReturnsGroupedSummaries() {
//         GIVEN
        Currency ngn = insertCurrency(NGN);
        Country country = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(ngn).build());

        AccountDailySummary dailySummaryToInsert0 = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.of(2022, 1, 2))
            .account(aAccount)
            .finalBalanceUsd(BigDecimal.TEN)
            .fxRate(null)
            .currency(ngn)
            .build();

        AccountDailySummary dailySummaryToInsert1 = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.of(2023, 1, 2))
            .account(aAccount)
            .finalBalanceUsd(BigDecimal.valueOf(20))
            .fxRate(null)
            .currency(ngn)
            .build();

        CashPositionFilters cashPositionFilters = CashPositionFilters.builder()
            .date(LocalDate.of(2023, 1, 2))
            .groupBy(CommonFilters.GroupBy.CURRENCY)
            .build();

        // WHEN
        insert(dailySummaryToInsert0);
        insert(dailySummaryToInsert1);

        final List<GroupedAccountDailySummaryDto> result = accountDailySummaryRepository.findCashPosition(cashPositionFilters);

        // THEN
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("NGN", result.get(0).getGroupLabel());
        assertEquals(BigDecimal.valueOf(20).setScale(2, RoundingMode.HALF_UP), result.get(0).getFinalBalanceUsd());
    }

    @SneakyThrows
    @Test
    public void whenCashEvolutionIsRequested_groupedByAccount_thenSummaryIsReturnedGroupedByAccount() {
        // GIVEN
        Currency ngn = insertCurrency(NGN);
        Country country = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(ngn).build());

        AccountDailySummary dailySummaryToInsert = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .account(aAccount)
            .initialBalanceUsd(BigDecimal.TEN)
            .finalBalanceUsd(BigDecimal.TEN)
            .fxRate(null)
            .currency(ngn)
            .build();
        CashEvolutionFilters cashEvolutionFilters = CashEvolutionFilters.builder()
            .evolutionFromDate(dailySummaryToInsert.getTransactionDate().minusDays(1))
            .evolutionToDate(dailySummaryToInsert.getTransactionDate().plusDays(1))
            .isAggregatedByPeriod(false)
            .groupBy(GroupBy.ACCOUNT)
            .build();

        //WHEN
        AccountDailySummary savedSummary = insert(dailySummaryToInsert);

        final List<GroupedAccountDailySummaryDto> result = accountDailySummaryRepository.findCashEvolution(cashEvolutionFilters);

        //THEN
        assertNotNull(savedSummary);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNotNull(result.get(0).getAccount());
        assertEquals(savedSummary.getFinalBalanceUsd().setScale(2, RoundingMode.HALF_UP), result.get(0).getFinalBalanceUsd());
        assertEquals(savedSummary.getAccount().getNavReference(), result.get(0).getGroupLabel());

    }


    @SneakyThrows
    @Test
    public void whenCashEvolutionIsRequested_groupedByCountry_thenSummaryIsReturnedGroupedByCountry() {
        // GIVEN
        Currency ngn = insertCurrency(NGN);
        Country country = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(country).currency(ngn).build());

        AccountDailySummary dailySummaryToInsert = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .account(aAccount)
            .initialBalanceUsd(BigDecimal.TEN)
            .finalBalanceUsd(BigDecimal.TEN)
            .fxRate(null)
            .currency(ngn)
            .build();
        CashEvolutionFilters cashEvolutionFilters = CashEvolutionFilters.builder()
            .evolutionFromDate(dailySummaryToInsert.getTransactionDate().minusDays(1))
            .evolutionToDate(dailySummaryToInsert.getTransactionDate().plusDays(1))
            .isAggregatedByPeriod(false)
            .groupBy(GroupBy.COUNTRY)
            .build();

        //WHEN
        AccountDailySummary savedSummary = insert(dailySummaryToInsert);

        final List<GroupedAccountDailySummaryDto> result = accountDailySummaryRepository.findCashEvolution(cashEvolutionFilters);

        //THEN
        assertNotNull(savedSummary);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNotNull(result.get(0).getCountry());
        assertEquals(savedSummary.getFinalBalanceUsd().setScale(2, RoundingMode.HALF_UP), result.get(0).getFinalBalanceUsd());
        assertEquals(savedSummary.getAccount().getCountry().getName(), result.get(0).getGroupLabel());

    }

    @SneakyThrows
    @Test
    public void whenFindCashPositionStacked_givenValidFilters_thenReturnsStackedSummaries() {
        // GIVEN
        Currency ngn = insertCurrency(NGN);
        Currency kes = insertCurrency(FakeCurrencies.KES);
        Country nigeria = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Country kenya = insertCountry(FakeCountries.KENYA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().country(nigeria).currency(ngn).build());
        Account anotherAccount = insertAccount(A_ACCOUNT.toBuilder().accountNumber("**********").companyID("COMPANY_ID_2")
            .country(kenya).currency(ngn).build());

        AccountDailySummary dailySummaryToInsert0 = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.of(2022, 1, 2))
            .account(aAccount)
            .finalBalanceUsd(BigDecimal.TEN)
            .fxRate(null)
            .currency(ngn)
            .build();

        AccountDailySummary dailySummaryToInsert1 = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.of(2023, 1, 2))
            .account(anotherAccount)
            .finalBalanceUsd(BigDecimal.valueOf(20))
            .fxRate(null)
            .currency(kes)
            .build();

        CashPositionFilters cashPositionFilters = CashPositionFilters.builder()
            .date(LocalDate.of(2025, 1, 2))
            .countries(List.of(nigeria.getId()))
            .groupBy(GroupBy.COUNTRY)
            .secondaryGroupBy(GroupBy.CURRENCY)
            .build();

        // WHEN
        insert(dailySummaryToInsert0);
        insert(dailySummaryToInsert1);

        final List<StackedCashPositionDto> result = accountDailySummaryRepository.findCashPositionStacked(cashPositionFilters);

        // THEN
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(nigeria.getName(), result.get(0).getParentGroupLabel());
        assertEquals(1, result.get(0).getStack().size());
        assertEquals(ngn.getCode(), result.get(0).getStack().get(0).getGroupLabel());
        assertEquals(BigDecimal.valueOf(10).setScale(2, RoundingMode.HALF_UP),
            result.get(0).getStack().get(0).getFinalBalanceUsd());
    }

    @SneakyThrows
    @Test
    public void whenFindCashPositionStacked_given2StackedValues_thenReturnsStackedSummaries() {
        // GIVEN
        Currency ngn = insertCurrency(NGN);
        Country country = insertCountry(NIGERIA.toBuilder().currency(ngn).build());
        Account aAccount = insertAccount(A_ACCOUNT.toBuilder().companyID("COMPANY_ID_1").country(country).currency(ngn).build());
        Account anotherAccount = insertAccount(
            A_ACCOUNT.toBuilder().accountNumber("**********").companyID("COMPANY_ID_2").country(country).currency(ngn).build());

        AccountDailySummary dailySummaryToInsert = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.of(2022, 1, 1))
            .account(anotherAccount)
                .finalBalanceUsd(BigDecimal.valueOf(55))
            .fxRate(null)
            .currency(ngn)
            .build();

        AccountDailySummary dailySummaryToInsert0 = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.of(2022, 1, 2))
            .account(anotherAccount)
            .finalBalanceUsd(BigDecimal.TEN)
            .fxRate(null)
            .currency(ngn)
            .build();

        AccountDailySummary dailySummaryToInsert1 = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .transactionDate(LocalDate.of(2023, 1, 2))
            .account(aAccount)
            .finalBalanceUsd(BigDecimal.valueOf(20))
            .fxRate(null)
            .currency(ngn)
            .build();

        CashPositionFilters cashPositionFilters = CashPositionFilters.builder()
            .date(LocalDate.of(2023, 1, 2))
            .groupBy(GroupBy.COUNTRY)
            .secondaryGroupBy(GroupBy.LEGAL_ENTITY)
            .build();

        // WHEN
        insert(dailySummaryToInsert0);
        insert(dailySummaryToInsert1);

        final List<StackedCashPositionDto> result = accountDailySummaryRepository.findCashPositionStacked(cashPositionFilters);

        // THEN
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(country.getName(), result.get(0).getParentGroupLabel());
        assertEquals(2, result.get(0).getStack().size());

        assertEquals(aAccount.getCompanyID(), result.get(0).getStack().get(0).getGroupLabel());
        assertEquals(BigDecimal.valueOf(20).setScale(2, RoundingMode.HALF_UP),
            result.get(0).getStack().get(0).getFinalBalanceUsd());

        assertEquals(anotherAccount.getCompanyID(), result.get(0).getStack().get(1).getGroupLabel());
        assertEquals(BigDecimal.valueOf(10).setScale(2, RoundingMode.HALF_UP),
            result.get(0).getStack().get(1).getFinalBalanceUsd());
    }
}
