package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.SubQueryExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.StringTemplate;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.AccountDailySummaryPsql;
import pt.jumia.services.brad.data.brad.entities.AccountPsql;
import pt.jumia.services.brad.data.brad.entities.QAccountDailySummaryPsql;
import pt.jumia.services.brad.data.brad.entities.QAccountPsql;
import pt.jumia.services.brad.data.brad.entities.QCountryPsql;
import pt.jumia.services.brad.data.brad.entities.QCurrencyPsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.FxRatePsql;
import pt.jumia.services.brad.data.brad.entities.keys.AccountDailySummaryId;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaAccountDailySummaryRepository;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaFxRateRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.AccountDailySummary;
import pt.jumia.services.brad.domain.entities.dtos.GroupedAccountDailySummaryDto;
import pt.jumia.services.brad.domain.entities.dtos.StackedCashPositionDto;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashEvolutionFilters;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashEvolutionFilters.AggregateBy;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashPositionFilters;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CommonFilters;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CommonFilters.GroupBy;
import pt.jumia.services.brad.domain.repository.brad.AccountDailySummaryRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlAccountDailySummaryRepository extends BaseRepository implements AccountDailySummaryRepository {

    public static final String GROUP_LABEL = "groupLabel";
    public static final String SECONDARY_GROUP_LABEL = "secondaryGroupLabel";
    private final QAccountDailySummaryPsql root = new QAccountDailySummaryPsql("root");
    private final QAccountPsql account = new QAccountPsql("account");
    private final QCountryPsql country = new QCountryPsql("country");
    private final QCurrencyPsql currency = new QCurrencyPsql("currency");
    private final JpaAccountDailySummaryRepository jpaAccountDailySummaryRepository;
    private final JpaFxRateRepository jpaFxRateRepository;
    private final EntityManager entityManager;
    private static final String FINAL_USD_BALANCE = "finalBalanceUsd";
    private static final String EMPTY_STRING_TEMPLATE = "{0}";


    @Override
    @Transactional
    public AccountDailySummary upsert(AccountDailySummary summary) {

        final AccountDailySummaryPsql entity = addFxRate(summary);

        return jpaAccountDailySummaryRepository.save(entity).toEntity();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AccountDailySummary> findById(LocalDate accountDate, Account account) {

        return jpaAccountDailySummaryRepository.findById(new AccountDailySummaryId(accountDate, new AccountPsql(account)))
                .map(AccountDailySummaryPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AccountDailySummary> findLatestByAccountIdAndDate(Long accountId, LocalDate toDate) {

        JPAQuery<AccountDailySummaryPsql> query = new JPAQueryFactory(entityManager)
            .selectFrom(root)
            .where(root.account.id.eq(accountId).and(root.transactionDate.loe(toDate)))
            .orderBy(root.transactionDate.desc())
            .limit(1);

        return Optional.ofNullable(query.fetchOne()).map(AccountDailySummaryPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AccountDailySummary> findOldestMissingFxRate(Long accountId) {

        JPAQuery<AccountDailySummaryPsql> query = new JPAQueryFactory(entityManager)
            .selectFrom(root)
            .where(root.account.id.eq(accountId).and(root.fxRate.isNull()))
            .orderBy(root.transactionDate.asc())
            .limit(1);

        return Optional.ofNullable(query.fetchOne()).map(AccountDailySummaryPsql::toEntity);
    }

    @Override
    @Transactional
    public void deleteById(LocalDate accountDate, Account account) {

        jpaAccountDailySummaryRepository.deleteById(new AccountDailySummaryId(accountDate, new AccountPsql(account)));
    }

    @Override
    @Transactional
    public void deleteByDates(Long accountId, LocalDate startDate, LocalDate endDate) {

        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        queryFactory.delete(root)
            .where(root.account.id.eq(accountId).and(root.transactionDate.between(startDate, endDate)))
            .execute();
    }

    @Override
    @Transactional(readOnly = true)
    public List<GroupedAccountDailySummaryDto> findCashEvolution(CashEvolutionFilters evolutionFilters) {

        JPAQuery<AccountDailySummaryPsql> query = new JPAQueryFactory(entityManager).selectFrom(root);

        StringTemplate aggregate = determineAggregate(evolutionFilters);
        buildWhereClauses(evolutionFilters, query);
        addEvolutionDateRangeFilters(evolutionFilters, query);
        Expression<?>[] selectedColumns = determineSelectedColumns(evolutionFilters, aggregate);

        final JPAQuery<Tuple> tupleJPAQuery = query
            .select(selectedColumns)
            .distinct()
            .from(root)
            .join(root.account, account)
            .join(root.account.country, country)
            .groupBy(determineGroupBy(evolutionFilters, aggregate))
            .orderBy(root.transactionDate.max().asc());

        List<Tuple> tuples = tupleJPAQuery.fetch();

        final List<GroupedAccountDailySummaryDto> summaries = tuples.stream()
            .map(tuple -> GroupedAccountDailySummaryDto.builder()
                .groupLabel(getGroupLabel(tuple))
                .account(Objects.isNull(tuple.get(account)) ? null : tuple.get(account).toEntity())
                .country(Objects.isNull(tuple.get(country)) ? null : tuple.get(country).toEntity())
                .creditAmountUsd(tuple.get(Expressions.numberPath(BigDecimal.class, "creditAmountUsd")))
                .debitAmountUsd(tuple.get(Expressions.numberPath(BigDecimal.class, "debitAmountUsd")))
                .build()
            )
            .collect(Collectors.toList());

        final List<BigDecimal> initialBalance = findFinalBalAtADate(evolutionFilters, evolutionFilters.getEvolutionFromDate().minusDays(1));
        final List<BigDecimal> finalBalance = findFinalBalAtADate(evolutionFilters, evolutionFilters.getEvolutionToDate());

        if (!summaries.isEmpty()) {
            summaries.get(0).setInitialBalanceUsd(initialBalance.get(0));

            summaries.get(summaries.size() - 1).setFinalBalanceUsd(finalBalance.get(0));
        }
        return summaries;

    }

    private List<BigDecimal> findFinalBalAtADate(CashEvolutionFilters filters, LocalDate endDate) {

        QAccountDailySummaryPsql subRoot = new QAccountDailySummaryPsql("subRoot");

        JPAQuery<AccountDailySummaryPsql> query = new JPAQueryFactory(entityManager).selectFrom(root);

        buildWhereClauses(filters, query);

        return query
            .select(
                root.finalBalanceUsd.coalesce(BigDecimal.valueOf(0)).sum().as("finalBalanceUsd")
            )
            .from(root)
            .join(subRoot).on(subRoot.transactionDate.eq(root.transactionDate).and(subRoot.account.id.eq(root.account.id)))
            .where(root.transactionDate.in(
                JPAExpressions
                    .select(subRoot.transactionDate.max())
                    .from(subRoot)
                    .where(subRoot.transactionDate.loe(endDate).and(root.account.id.eq(subRoot.account.id)))
                    .groupBy(subRoot.account.id)
            ).and(root.account.id.eq(subRoot.account.id)))
            .fetch();

    }

    @Override
    @Transactional(readOnly = true)
    public List<GroupedAccountDailySummaryDto> findCashPosition(CashPositionFilters positionFilters) {

        JPAQuery<AccountDailySummaryPsql> query = new JPAQueryFactory(entityManager).selectFrom(root);
        StringTemplate groupBy = determineGroup(positionFilters.getGroupBy());

        buildWhereClauses(positionFilters, query);
        List<Tuple> tuples = query
            .select(
                groupBy.as(GROUP_LABEL),
                root.finalBalanceUsd.sum().as(FINAL_USD_BALANCE)
            )
            .from(root)
            .where(root.transactionDate.eq(buildCashPositionSubquery(positionFilters.getDate())))
            .groupBy(groupBy)
            .fetch();

        return tuples.stream()
            .map(tuple -> GroupedAccountDailySummaryDto.builder()
                .groupLabel(tuple.get(Expressions.stringPath(GROUP_LABEL)))
                .finalBalanceUsd(tuple.get(Expressions.numberPath(BigDecimal.class, FINAL_USD_BALANCE)))
                .build()
            ).collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<StackedCashPositionDto> findCashPositionStacked(CashPositionFilters positionFilters) {

        JPAQuery<AccountDailySummaryPsql> query = new JPAQueryFactory(entityManager).selectFrom(root);

        StringTemplate groupBy = determineGroup(positionFilters.getGroupBy());
        StringTemplate secondaryGroupBy = determineGroup(positionFilters.getSecondaryGroupBy());

        buildWhereClauses(positionFilters, query);

        List<Tuple> tuples = query
            .select(
                groupBy.as(GROUP_LABEL),
                secondaryGroupBy.as(SECONDARY_GROUP_LABEL),
                root.finalBalanceUsd.sum().as(FINAL_USD_BALANCE)
            )
            .from(root)
            .where(root.transactionDate.eq(buildCashPositionSubquery(positionFilters.getDate())))
            .groupBy(groupBy)
            .groupBy(secondaryGroupBy)
            .orderBy(groupBy.asc(), secondaryGroupBy.asc())
            .fetch();

        Map<String, List<GroupedAccountDailySummaryDto>> groupedSummaries = tuples.stream()
            .collect(Collectors.groupingBy(
                tuple -> tuple.get(Expressions.stringPath(GROUP_LABEL)),
                Collectors.mapping(tuple -> GroupedAccountDailySummaryDto.builder()
                        .groupLabel(tuple.get(Expressions.stringPath(SECONDARY_GROUP_LABEL)))
                        .finalBalanceUsd(tuple.get(Expressions.numberPath(BigDecimal.class, FINAL_USD_BALANCE)))
                        .build(),
                    Collectors.toList())
            ));

        return groupedSummaries.entrySet().stream()
            .map(entry -> StackedCashPositionDto.builder()
                .parentGroupLabel(entry.getKey())
                .stack(entry.getValue())
                .build())
            .collect(Collectors.toList());
    }

    private void buildWhereClauses(CommonFilters filters, JPAQuery<?> query) {

        if (Objects.isNull(filters)) {
            return;
        }
        filterByCurrency(filters, query);
        filterByCountries(filters, query);
        filterByLegalEntity(filters, query);
        filterByAccountType(filters, query);

        if (filters instanceof CashPositionFilters) {

            CashPositionFilters cashPositionFilters = (CashPositionFilters) filters;
            filterByDate(cashPositionFilters, query);
        }

    }

    private void addEvolutionDateRangeFilters(final CashEvolutionFilters filters, final JPAQuery<?> query) {

        if (filters != null) {
            filterByEvolutionDateRange(filters, query);
        }
    }

    public void filterByCountries(CommonFilters filters, JPAQuery<?> query) {

        if (Objects.nonNull(filters.getCountries())) {
            query.where(root.account.country.id.in(filters.getCountries()));
        }
    }

    public void filterByCurrency(CommonFilters filters, JPAQuery<?> query) {

        if (Objects.nonNull(filters.getCurrency())) {
            query.where(root.currency.code.eq(filters.getCurrency()));
        }
    }

    public void filterByLegalEntity(CommonFilters filters, JPAQuery<?> query) {

        if (Objects.nonNull(filters.getLegalEntity())) {
            query.where(root.account.companyID.equalsIgnoreCase(filters.getLegalEntity()));
        }
    }

    public void filterByAccountType(CommonFilters filters, JPAQuery<?> query) {

        if (Objects.nonNull(filters.getAccountType())) {
            query.where(root.account.type.eq(filters.getAccountType().name()));
        }
    }

    public void filterByEvolutionDateRange(CashEvolutionFilters filters, JPAQuery<?> query) {

        if (Objects.nonNull(filters.getEvolutionFromDate()) && !Objects.isNull(filters.getEvolutionToDate())) {
            query.where(root.transactionDate.between(filters.getEvolutionFromDate(), filters.getEvolutionToDate()));
        }
    }

    public void filterByDate(CashPositionFilters filters, JPAQuery<?> query) {

        if (Objects.nonNull(filters.getDate())) {
            query.where(root.transactionDate.loe(filters.getDate()));
        }
    }

    public StringTemplate determineGroup(CommonFilters.GroupBy groupBy) {

        if (Objects.isNull(groupBy)) {
            return Expressions.stringTemplate(EMPTY_STRING_TEMPLATE, root.account.country.name);
        }

        return switch (groupBy) {

            case ACCOUNT_TYPE -> Expressions.stringTemplate(EMPTY_STRING_TEMPLATE, root.account.type);

            case LEGAL_ENTITY -> Expressions.stringTemplate(EMPTY_STRING_TEMPLATE, root.account.companyID);

            case CURRENCY -> Expressions.stringTemplate(EMPTY_STRING_TEMPLATE, root.currency.code);

            case COUNTRY -> Expressions.stringTemplate(EMPTY_STRING_TEMPLATE, root.account.country.name);

            case ACCOUNT -> Expressions.stringTemplate(EMPTY_STRING_TEMPLATE, root.account.accountNumber);
        };
    }

    public SubQueryExpression<LocalDate> buildCashPositionSubquery(LocalDate date) {

        QAccountDailySummaryPsql subRoot = new QAccountDailySummaryPsql("subRoot");

        BooleanBuilder predicate = new BooleanBuilder()
                .and(subRoot.account.id.eq(root.account.id));

        if (date != null) {
            predicate.and(subRoot.transactionDate.loe(date));
        }

        return JPAExpressions
                .select(subRoot.transactionDate.max())
                .from(subRoot)
                .where(predicate);
    }

    private StringTemplate determineAggregate(final CashEvolutionFilters filters) {

        if (!filters.getIsAggregatedByPeriod()) {
            GroupBy groupBy = filters.getGroupBy();
            if (Objects.isNull(groupBy)) {
                return Expressions.stringTemplate(EMPTY_STRING_TEMPLATE, root.account.country.name);
            }

            return switch (groupBy) {

                case ACCOUNT_TYPE -> Expressions.stringTemplate(EMPTY_STRING_TEMPLATE, root.account.type);

                case LEGAL_ENTITY -> Expressions.stringTemplate(EMPTY_STRING_TEMPLATE, root.account.companyID);

                case CURRENCY -> Expressions.stringTemplate(EMPTY_STRING_TEMPLATE, root.currency.id);

                case COUNTRY -> Expressions.stringTemplate(EMPTY_STRING_TEMPLATE, root.account.country.name);

                case ACCOUNT -> Expressions.stringTemplate(EMPTY_STRING_TEMPLATE, root.account.id);
            };
        }

        AggregateBy evolutionAggregateBy = filters.getEvolutionAggregateBy();

        if (Objects.isNull(evolutionAggregateBy)) {
            return Expressions.stringTemplate(
                "TO_CHAR({0}, 'YYYY-MM-DD')",
                root.transactionDate
            );
        }

        return switch (evolutionAggregateBy) {

            case DAILY -> Expressions.stringTemplate(
                "TO_CHAR({0}, 'YYYY-MM-DD')",
                root.transactionDate
            );

            case MONTHLY -> Expressions.stringTemplate(
                "TO_CHAR({0}, 'YYYY') || ' ' || TO_CHAR({0}, 'Month')",
                root.transactionDate
            );

            case QUARTERLY -> Expressions.stringTemplate(
                "EXTRACT(YEAR FROM {0}) || ' Q' || FLOOR((EXTRACT(MONTH FROM {0}) - 1) / 3 + 1)",
                root.transactionDate
            );

            case YEARLY -> Expressions.stringTemplate(
                "TO_CHAR({0}, 'YYYY')",
                root.transactionDate
            );

        };
    }

    private Expression<?>[] determineSelectedColumns(final CashEvolutionFilters evolutionFilters, final StringTemplate aggregate) {

        List<Expression<?>> expressions = new ArrayList<>();
        expressions.add(aggregate.as(GROUP_LABEL));

        expressions.add(root.totalCreditAmountUsd.sum().as("creditAmountUsd"));
        expressions.add(root.totalDebitAmountUsd.sum().as("debitAmountUsd"));
        expressions.add(root.transactionDate.max()); //for ordering purposes only

        if (GroupBy.ACCOUNT.equals(evolutionFilters.getGroupBy()) && !evolutionFilters.getIsAggregatedByPeriod()) {
            expressions.add(account);
        }
        if (GroupBy.COUNTRY.equals(evolutionFilters.getGroupBy()) && !evolutionFilters.getIsAggregatedByPeriod()) {
            expressions.add(country);
        }
        if (GroupBy.CURRENCY.equals(evolutionFilters.getGroupBy()) && !evolutionFilters.getIsAggregatedByPeriod()) {
            expressions.add(currency);
        }
        return expressions.toArray(new Expression[0]);
    }

    private Expression<?>[] determineGroupBy(final CashEvolutionFilters evolutionFilters, final StringTemplate aggregate) {

        List<Expression<?>> expressions = new ArrayList<>();
        expressions.add(aggregate);

        if (GroupBy.ACCOUNT.equals(evolutionFilters.getGroupBy()) && !evolutionFilters.getIsAggregatedByPeriod()) {
            expressions.add(account);
        }
        if (GroupBy.COUNTRY.equals(evolutionFilters.getGroupBy()) && !evolutionFilters.getIsAggregatedByPeriod()) {
            expressions.add(country);
        }
        if (GroupBy.CURRENCY.equals(evolutionFilters.getGroupBy()) && !evolutionFilters.getIsAggregatedByPeriod()) {
            expressions.add(currency);
        }
        return expressions.toArray(new Expression[0]);
    }

    private String getGroupLabel(final Tuple tuple) {

        return Objects.nonNull(tuple.get(account))
            ? tuple.get(account).toEntity().getNavReference()
            : Objects.nonNull(tuple.get(country))
                ? tuple.get(country).toEntity().getName()
                : Objects.nonNull(tuple.get(currency))
                    ? tuple.get(currency).toEntity().getCode()
                    : String.valueOf(tuple.get(Expressions.stringPath(GROUP_LABEL)));
    }

    private AccountDailySummaryPsql addFxRate(final AccountDailySummary summary) {

        if (Objects.isNull(summary.getFxRate())) {
            return new AccountDailySummaryPsql(summary);
        }
        FxRatePsql fxRateFromDb = jpaFxRateRepository.findById(summary.getFxRate().getId()).orElse(null);

        final AccountDailySummaryPsql entity = new AccountDailySummaryPsql(summary);
        entity.setFxRate(fxRateFromDb);
        return entity;
    }

}
