package pt.jumia.services.brad.data.bale.configuration;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.dialect.SQLServer2016Dialect;
import org.hibernate.jpa.HibernatePersistenceProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.Database;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import pt.jumia.services.brad.data.shared.Datasource;
import pt.jumia.services.brad.data.shared.EntityManagerConfiguration;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.properties.DataProperties;
import pt.jumia.services.brad.domain.repository.brad.ViewEntityRepository;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

@SuppressFBWarnings("EI_EXPOSE_REP")
@Slf4j
@Configuration
@RequiredArgsConstructor
public class BaleEntityManagerConfiguration implements EntityManagerConfiguration {

    private final ViewEntityRepository viewEntityRepository;
    private final DataProperties dataProperties;
    private Datasource datasource;


    @Bean(name = "baleTransactionManager")
    public JpaTransactionManager transactionManager(@Qualifier("baleEntityManager") EntityManagerFactory emf) {
        return new JpaTransactionManager(emf);
    }

    @Bean(name = "baleEntityManager")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory() {

        LocalContainerEntityManagerFactoryBean fact = new LocalContainerEntityManagerFactoryBean();
        fact.setPackagesToScan("pt.jumia.services.brad.data.bale.entities");
        fact.setDataSource(getDatasource());

        HibernateJpaVendorAdapter vendor = new HibernateJpaVendorAdapter();
        vendor.setDatabase(Database.SQL_SERVER);

        fact.setJpaVendorAdapter(vendor);
        fact.setPersistenceProvider(new HibernatePersistenceProvider());

        final Properties properties = new Properties();
        additionalProperties(properties);
        fact.setJpaProperties(properties);

        return fact;
    }

    @Bean(name = "baleDataSource")
    public DataSource getDatasource() {
        this.datasource = new Datasource(buildDataSources());
        return this.datasource;
    }

    private void additionalProperties(Properties properties) {
        properties.put("hibernate.dialect", SQLServer2016Dialect.class.getName());
        properties.put("hibernate.default_schema", dataProperties.getBale().getApplicationSchema());
        properties.put("hibernate.show_sql", "false");
        properties.put("hibernate.ejb.interceptor", "pt.jumia.services.brad.data.bale.configuration.BaleInterceptor");
    }

    public void refresh() {
        try {
            this.datasource.update(buildDataSources());
        } catch (Exception ex) {
            log.error("Error refreshing bale datasources", ex);
        }
    }

    private Map<String, HikariDataSource> buildDataSources() {
        try {
            List<ViewEntity> baleViewEntityList = viewEntityRepository.findAll(ViewEntity.EntityType.BALE);
            Map<String, HikariDataSource> datasourceMap = new HashMap<>();

            for (ViewEntity entity : baleViewEntityList) {
                try {
                    datasourceMap.put(Datasource.getLookupKey(entity), buildDatasource(entity));
                } catch (Exception ex) {
                    log.error("Error building bale datasource", ex);
                }
            }
            return datasourceMap;
        } catch (Exception ex) {
            log.error("Error building bale datasources", ex);
        }
        return new HashMap<>();
    }

    private HikariDataSource buildDatasource(ViewEntity entity) {
        HikariConfig config = new HikariConfig();
        config.setDriverClassName(entity.getDriver());
        String jdbcUrl = String.format("%s;databaseName=%s",
                entity.getJdbcConnectionUrl(), entity.getDatabaseName());
        config.setJdbcUrl(jdbcUrl);
        String username = dataProperties.getBale().getUsername();
        config.setUsername(username);
        config.setPassword(dataProperties.getBale().getPassword());
        config.setMaximumPoolSize(dataProperties.getBale().getMaxPoolSize());
        config.setConnectionTestQuery("SELECT 1");
        log.debug("Attempting to build Bale datasource with JDBC URL: {} and Username: {}", jdbcUrl, username);
        return new HikariDataSource(config);
    }
}
