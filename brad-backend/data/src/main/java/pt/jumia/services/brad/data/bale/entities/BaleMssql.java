package pt.jumia.services.brad.data.bale.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import pt.jumia.services.brad.data.shared.BaseBale;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Account;

@Slf4j
@Entity
@Table(name = "Bale")
@Getter
@Setter
@NoArgsConstructor
public class BaleMssql extends BaseBale {

    @Id
    @Column(name = "`Entry No_`")
    protected Integer entryNo;

    @Column(name = "No_", length = 50)
    protected String accountNumber;

    @Column(name = "Name", length = 255)
    protected String accountName;

    @Column(name = "`Account Type`", length = 50)
    protected String accountType;

    @Column(name = "`Currency Code`", length = 5)
    protected String currencyCode;

    @Column(name = "`Kyriba Ref_`", length = 50)
    protected String kyribaReference;

    @Column(name = "`Bank Account No_`", length = 50)
    protected String navReference;

    @Column(name = "`Currency`", length = 50)
    protected String transactionCurrency;

    public BaleMssql(Bale bale) {
        super(bale);
    }

    @Override
    public Bale toEntity() {
        log.debug("Bale sync: Converting BaleMssql to Bale entity - ALL PROPERTIES: " +
                "entryNo: {}, accountNumber: {}, accountName: {}, accountType: {}, " +
                "currencyCode: {}, kyribaReference: {}, navReference: {}, transactionCurrency: {}",
                this.entryNo, this.accountNumber, this.accountName, this.accountType,
                this.currencyCode, this.kyribaReference, this.navReference, this.transactionCurrency);
        
        try {
            Bale bale = super.toEntity();
            log.debug("Bale sync: Base entity conversion successful for entryNo: {}", this.entryNo);
            
            Currency currency = Currency.builder().code(this.currencyCode).build();
            log.debug("Bale sync: Created Currency object with code: {}", this.currencyCode);
            
            Account account = Account.builder().navReference(this.navReference).build();
            log.debug("Bale sync: Created Account object with navReference: {}", this.navReference);
            
            Bale result = bale.toBuilder()
                    .entryNo(this.entryNo)
                    .transactionCurrency(currency)
                    .account(account)
                    .build();
            
            log.debug("Bale sync: Successfully converted BaleMssql to Bale - entryNo: {}, transactionCurrency: {}, navReference: {}",
                    result.getEntryNo(), result.getTransactionCurrency().getCode(), result.getAccount().getNavReference());
            
            return result;
        } catch (Exception e) {
            log.error("Bale sync: Error converting BaleMssql to Bale entity - entryNo: {}, currencyCode: {}, navReference: {}, error: {}",
                    this.entryNo, this.currencyCode, this.navReference, e.getMessage(), e);
            throw e;
        }
    }
}
