package pt.jumia.services.brad.data.brad.repository.psql.audit;

import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import pt.jumia.services.brad.data.brad.audit.RevisionTypeConverter;
import pt.jumia.services.brad.data.brad.entities.QRevisionPsql;
import pt.jumia.services.brad.data.brad.entities.RevisionPsql;
import pt.jumia.services.brad.data.brad.entities.audit.AccountAudPsql;
import pt.jumia.services.brad.data.brad.entities.audit.QAccountAudPsql;
import pt.jumia.services.brad.domain.entities.AuditedEntity;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.filter.audit.AccountAuditFilters;
import pt.jumia.services.brad.domain.enumerations.AuditedEntities;
import pt.jumia.services.brad.domain.repository.audit.AccountAuditRepository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Repository
@RequiredArgsConstructor
public class PsqlAccountAuditRepository implements AccountAuditRepository {

    private final QAccountAudPsql qAccountAudPsql = QAccountAudPsql.accountAudPsql;
    private final QRevisionPsql qRevisionPsql = QRevisionPsql.revisionPsql;

    private final EntityManager entityManager;



    @Override
    public List<AuditedEntity<Account>> getAuditLogById(AccountAuditFilters filters) {
        JPAQuery<Tuple> query = baseQuery(filters)
                .select(qAccountAudPsql, qRevisionPsql);

        return query.fetch().stream().map(tuple -> {
            RevisionPsql revisionPsql = tuple.get(qRevisionPsql);
            AccountAudPsql accountAudPsql = tuple.get(qAccountAudPsql);

            AuditedEntity.OperationType operationType = RevisionTypeConverter.toOperationType(
                    tuple.get(qAccountAudPsql).getRevType());

            Account auditedAccount = accountAudPsql.toEntity();

            return AuditedEntity.<Account>builder()
                    .auditedEntity(getAuditedEntity(auditedAccount))
                    .operationType(operationType)
                    .revisionInfo(revisionPsql.toEntity())
                    .entity(auditedAccount)
                    .build();
        }).collect(Collectors.toList());

    }

    @Override
    public long getAuditLogCountById(AccountAuditFilters filters) {
        return baseQuery(filters).fetchCount();
    }


    private JPAQuery<?> baseQuery(AccountAuditFilters filters) {
        JPAQuery<?> query = new JPAQueryFactory(entityManager)
                .selectFrom(qAccountAudPsql)
                .innerJoin(qRevisionPsql).on(qAccountAudPsql.id.rev.eq(qRevisionPsql.id));

        if (!Objects.isNull(filters.getId())) {
            query.where(qAccountAudPsql.id.id.eq(filters.getId()));
        }

        AuditRepositoryHelper.applyGenericFiltersAndSorting(query, qRevisionPsql, filters);

        return query;
    }

    private AuditedEntities getAuditedEntity(Account auditedAccount) {
        return switch (auditedAccount.getType()) {
            case BANK_ACCOUNT -> AuditedEntities.BANK_ACCOUNT;
            case PSP -> AuditedEntities.PSP;
            case MOBILE_MONEY -> AuditedEntities.MOBILE_MONEY;
            case WALLET -> AuditedEntities.WALLET;
            case INVESTMENTS -> AuditedEntities.INVESTMENTS;

        };
    }

}
