package pt.jumia.services.brad.data.brad.configuration;

import lombok.RequiredArgsConstructor;
import org.quartz.JobDetail;
import org.quartz.Trigger;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.quartz.CronTriggerFactoryBean;
import org.springframework.scheduling.quartz.JobDetailFactoryBean;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import pt.jumia.services.brad.data.brad.job.BaleSyncJob;
import pt.jumia.services.brad.data.brad.job.FinrecStatementSyncJob;
import pt.jumia.services.brad.data.brad.job.FxRateSyncJob;
import pt.jumia.services.brad.data.brad.job.ScanSftpFolderJob;
import pt.jumia.services.brad.data.brad.job.SendTroubleShootingAccountsJob;
import pt.jumia.services.brad.domain.properties.DataProperties;

import java.io.IOException;
import java.util.Properties;

@Configuration
@RequiredArgsConstructor
public class JobSchedulerConfig implements ApplicationContextAware {

    private static final String SYNC_BALE_JOB_NAME = "BaleJob";
    private static final String BALE_DEFAULT_REPEAT_INTERVALE = "0 40 13 ? * *";

    private static final String FINREC_STATEMENTS_JOB_NAME = "FinrecStatementsJob";
    private static final String FINREC_STATEMENTS_REPEAT_INTERVAL = "0 0 0 ? * *";

    private static final String FX_RATE_JOB_NAME = "FxRateJob";
    private static final String DEFAULT_FX_RATE_REPEAT_INTERVAL = "0 0 1 ? * *";

    private static final String BANK_STATEMENTS_FILE_SCAN = "BankStatementFileScan";
    private static final String BANK_STATEMENTS_FILE_SCAN_REPEAT_INTERVAL = "0 0 0 ? * *";

    private static final String TROUBLESHOOTING_ACCOUNTS_EMAIL = "SendTroubleshootingAccountsEmail";
    private static final String TROUBLESHOOTING_ACCOUNTS_EMAIL_REPEAT_INTERVAL = "0 0 4 ? * *";

    private ApplicationContext applicationContext;

    private final DataProperties dataProperties;

    @Bean
    public JobDetailFactoryBean baleJobDetail() {
        JobDetailFactoryBean factory = new JobDetailFactoryBean();
        factory.setJobClass(BaleSyncJob.class);
        factory.setDurability(true);
        factory.setName(SYNC_BALE_JOB_NAME);
        factory.setApplicationContext(applicationContext);
        return factory;
    }

    @Bean
    public CronTriggerFactoryBean baleJobTriggerA(JobDetail baleJobDetail) {
        CronTriggerFactoryBean factory = new CronTriggerFactoryBean();
        factory.setJobDetail(baleJobDetail);
        factory.setCronExpression(BALE_DEFAULT_REPEAT_INTERVALE);

        return factory;
    }

    @Bean
    public JobDetailFactoryBean finrecStatementsSyncJobDetail() {
        JobDetailFactoryBean factory = new JobDetailFactoryBean();
        factory.setJobClass(FinrecStatementSyncJob.class);
        factory.setDurability(true);
        factory.setName(FINREC_STATEMENTS_JOB_NAME);
        factory.setApplicationContext(applicationContext);
        return factory;
    }

    @Bean
    public CronTriggerFactoryBean finrecStatementsSyncJobTrigger(JobDetail finrecStatementsSyncJobDetail) {
        CronTriggerFactoryBean factory = new CronTriggerFactoryBean();
        factory.setJobDetail(finrecStatementsSyncJobDetail);
        factory.setCronExpression(FINREC_STATEMENTS_REPEAT_INTERVAL);

        return factory;
    }

    @Bean
    public JobDetailFactoryBean fxRateJobDetail() {
        JobDetailFactoryBean factory = new JobDetailFactoryBean();
        factory.setJobClass(FxRateSyncJob.class);
        factory.setDurability(true);
        factory.setName(FX_RATE_JOB_NAME);
        factory.setApplicationContext(applicationContext);
        return factory;
    }

    @Bean
    public CronTriggerFactoryBean  fxRateJobTriggerA(JobDetail fxRateJobDetail) {
        CronTriggerFactoryBean factory = new CronTriggerFactoryBean();
        factory.setJobDetail(fxRateJobDetail);
        factory.setCronExpression(DEFAULT_FX_RATE_REPEAT_INTERVAL);

        return factory;
    }

    @Bean
    public JobDetailFactoryBean scanSftpFolderJobDetail() {
        JobDetailFactoryBean factory = new JobDetailFactoryBean();
        factory.setJobClass(ScanSftpFolderJob.class);
        factory.setDurability(true);
        factory.setName(BANK_STATEMENTS_FILE_SCAN);
        factory.setApplicationContext(applicationContext);
        return factory;
    }

    @Bean
    public CronTriggerFactoryBean scanSftpFolderJobTriggerA(JobDetail scanSftpFolderJobDetail) {
        CronTriggerFactoryBean factory = new CronTriggerFactoryBean();
        factory.setJobDetail(scanSftpFolderJobDetail);
        factory.setCronExpression(BANK_STATEMENTS_FILE_SCAN_REPEAT_INTERVAL);
        return factory;
    }

    @Bean
    public JobDetailFactoryBean sendTroubleShootingAccountsEmailJobDetail() {
        JobDetailFactoryBean factory = new JobDetailFactoryBean();
        factory.setJobClass(SendTroubleShootingAccountsJob.class);
        factory.setDurability(true);
        factory.setName(TROUBLESHOOTING_ACCOUNTS_EMAIL);
        factory.setApplicationContext(applicationContext);
        return factory;
    }

    @Bean
    public CronTriggerFactoryBean sendTroubleShootingAccountsEmailJobTrigger(JobDetail sendTroubleShootingAccountsEmailJobDetail) {
        CronTriggerFactoryBean factory = new CronTriggerFactoryBean();
        factory.setJobDetail(sendTroubleShootingAccountsEmailJobDetail);
        factory.setCronExpression(TROUBLESHOOTING_ACCOUNTS_EMAIL_REPEAT_INTERVAL);
        return factory;
    }

    @Bean
    public Properties quartzProperties() throws IOException {
        PropertiesFactoryBean propertiesFactoryBean = new PropertiesFactoryBean();
        propertiesFactoryBean.setLocation(new ClassPathResource("quartz.properties"));
        propertiesFactoryBean.afterPropertiesSet();

        Properties quartzProperties = propertiesFactoryBean.getObject();

        quartzProperties.put("org.quartz.dataSource.quartzDataSource.driver", dataProperties.getDb().getDriver());
        quartzProperties.put("org.quartz.dataSource.quartzDataSource.URL", dataProperties.getDb().getUrl());
        quartzProperties.put("org.quartz.dataSource.quartzDataSource.user", dataProperties.getDb().getUsername());
        quartzProperties.put("org.quartz.dataSource.quartzDataSource.password", dataProperties.getDb().getPassword());



        return quartzProperties;
    }

    @Bean
    public SchedulerFactoryBean scheduler(Trigger... triggers) throws IOException {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setConfigLocation(new ClassPathResource("quartz.properties"));
        factory.setQuartzProperties(quartzProperties());
        factory.setAutoStartup(true);
        factory.setTriggers(triggers);
        factory.setApplicationContextSchedulerContextKey("applicationContext");
        return factory;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

}
