package pt.jumia.services.brad.data.brad.job;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jetbrains.annotations.NotNull;
import org.quartz.JobExecutionContext;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.ScanS3BucketForStatementFileUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.CreateExecutionLogsUseCase;


@Slf4j
public class ScanSftpFolderJob extends QuartzJobBean {

    @Override
    protected void executeInternal(@NotNull JobExecutionContext jobExecutionContext) {

        try {
            log.info("Starting to scan sftp folder");
            ApplicationContext applicationContext = (ApplicationContext)
                jobExecutionContext.getScheduler().getContext().get("applicationContext");

            ScanS3BucketForStatementFileUseCase scanS3BucketForStatementFileUseCase
                = applicationContext.getBean(ScanS3BucketForStatementFileUseCase.class);

            ExecutionLog createdExecutionLog = createExecutionLog(applicationContext);

            log.info("SFTP folder scan. Execution log created: {}", createdExecutionLog.getId());

            scanS3BucketForStatementFileUseCase.execute(createdExecutionLog);

            log.info("SFTP folder scan completed: {}", createdExecutionLog.getId());

        } catch (Exception e) {
            log.error("Error while scanning sftp folder {}", ExceptionUtils.getStackTrace(e));
        }

    }

    private static ExecutionLog createExecutionLog(ApplicationContext applicationContext) {

        CreateExecutionLogsUseCase createExecutionLogsUseCase = applicationContext.getBean(CreateExecutionLogsUseCase.class);
        ExecutionLog newExecutionLog = ExecutionLog.builder()
            .logType(ExecutionLog.ExecutionLogType.BANK_STATEMENTS_FILE_SCAN)
            .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
            .executionStartTime(LocalDateTime.now(ZoneOffset.UTC))
            .build();
        ExecutionLog createdExecutionLog = createExecutionLogsUseCase.execute(newExecutionLog);
        return createdExecutionLog;
    }

}
