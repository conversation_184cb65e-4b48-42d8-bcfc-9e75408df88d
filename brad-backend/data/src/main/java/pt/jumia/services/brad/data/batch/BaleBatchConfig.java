package pt.jumia.services.brad.data.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.transaction.PlatformTransactionManager;
import pt.jumia.services.brad.domain.entities.Bale;

/**
 * Spring Batch configuration for Bale synchronization.
 * 
 * This replaces the over-engineered custom batch processing with proven Spring Batch patterns.
 * Benefits:
 * - Built-in chunk processing, error handling, and retry mechanisms
 * - Automatic job execution tracking and restart capability
 * - Standard patterns that any Spring developer can understand
 * - Robust transaction management and rollback handling
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class BaleBatchConfig {

    /**
     * Main Bale synchronization job.
     * 
     * Uses Spring Batch's JobBuilder for clean, declarative configuration.
     * Includes automatic run ID incrementing for job restart capability.
     */
    @Bean
    public Job baleSyncJob(JobRepository jobRepository, Step baleProcessingStep) {
        return new JobBuilder("baleSyncJob", jobRepository)
                .incrementer(new RunIdIncrementer())
                .start(baleProcessingStep)
                .build();
    }

    /**
     * Main processing step for Bale synchronization.
     * 
     * Configured with:
     * - Chunk size of 1000 (configurable via properties)
     * - Fault tolerance with retry on transient database errors
     * - Skip policy for handling individual item failures
     */
    @Bean
    public Step baleProcessingStep(JobRepository jobRepository,
                                   PlatformTransactionManager transactionManager,
                                   ItemReader<Bale> baleItemReader,
                                   ItemProcessor<Bale, Bale> baleItemProcessor,
                                   ItemWriter<Bale> baleItemWriter) {
        return new StepBuilder("baleProcessingStep", jobRepository)
                .<Bale, Bale>chunk(1000, transactionManager)
                .reader(baleItemReader)
                .processor(baleItemProcessor)
                .writer(baleItemWriter)
                .faultTolerant()
                .retryLimit(3)
                .retry(TransientDataAccessException.class)
                .skipLimit(100)
                .skip(Exception.class)
                .build();
    }
}
