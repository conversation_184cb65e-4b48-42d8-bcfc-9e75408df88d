package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import pt.jumia.services.brad.data.brad.entities.AccountPsql;

import java.util.List;
import java.util.Optional;

public interface JpaAccountRepository extends JpaRepository<AccountPsql, Long>, JpaSpecificationExecutor<AccountPsql> {

    @Query("SELECT acc FROM AccountPsql acc WHERE acc.accountNumber = :accountNumber AND acc.accountNumber IS NOT NULL")
    Optional<AccountPsql> findByAccountNumber(@Param("accountNumber") String accountNumber);

    Optional<AccountPsql> findByNavReference(String navReference);

    Optional<AccountPsql> findByNavReferenceAndCompanyID(String navReference, String companyID);

    Integer countByIsin(String isin);

    Integer countByContractId(String contractId);

    List<AccountPsql> findByNavReferenceIn(List<String> navReferences);
}
