package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.AccountStatementPsql;
import pt.jumia.services.brad.data.brad.entities.QAccountStatementPsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.FxRatePsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.statement.FxRateStatementPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaAccountStatementRepository;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaFxRateAccountStatementRepository;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaFxRateRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementSortFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementRepository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlAccountStatementRepository extends BaseRepository implements AccountStatementRepository {


    private final QAccountStatementPsql root = new QAccountStatementPsql("root");
    private final JpaAccountStatementRepository jpaAccountStatementRepository;
    private final EntityManager entityManager;

    private final JpaFxRateAccountStatementRepository jpaFxRateAccountStatementRepository;
    private final JpaFxRateRepository jpaFxRateRepository;

    @Override
    @Transactional
    public AccountStatement upsert(AccountStatement accountStatement) {
        AccountStatementPsql accountStatementPsql = jpaAccountStatementRepository.save(new AccountStatementPsql(accountStatement));
        this.addFxRates(accountStatement, accountStatementPsql);

        return fetch(accountStatementPsql);
    }

    @Override
    @Transactional
    public void addFxRates(AccountStatement accountStatement) {
        AccountStatementPsql accountStatementPsql = jpaAccountStatementRepository.findById(accountStatement.getId())
                .orElseThrow(() -> new RuntimeException("AccountStatement not found while adding fx rates"));

        this.addFxRates(accountStatement, accountStatementPsql);
    }

    private void addFxRates(AccountStatement accountStatement, AccountStatementPsql accountStatementPsql) {
        accountStatement.getFxRates().forEach(fxRate -> {
            FxRatePsql existingFxRate = jpaFxRateRepository.findById(fxRate.getId())
                    .orElseThrow(() -> new RuntimeException("FxRate not found while saving statement"));
            jpaFxRateAccountStatementRepository.save(new FxRateStatementPsql(existingFxRate, accountStatementPsql));
        });
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AccountStatement> findById(Long id) {
        return jpaAccountStatementRepository.findById(id)
                .map(this::fetch);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AccountStatement> findAll(AccountStatementFilters accountStatementFilters,
        AccountStatementSortFilters accountStatementSortFilters, PageFilters pageFilters) throws EntityErrorsException {

        JPAQuery<AccountStatementPsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root);

        if (Objects.nonNull(accountStatementFilters)) {
            applyProjectionSelect(accountStatementFilters.getSelectedFields(), query, root, AccountStatementPsql.class);
        }
        buildWhereClauses(accountStatementFilters, query);
        applySort(accountStatementSortFilters, query, root, AccountStatement.SortingFields.class);
        applyPagination(pageFilters, query);

        return query.fetch().stream()
                .map(this::fetch)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AccountStatement> findLastStatementInList(Long accountId) {
        return jpaAccountStatementRepository.findLastStatementInList(String.valueOf(accountId))
                .map(this::fetch);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AccountStatement> findLastImportedStatementInList(Long accountId) {
        Query query = entityManager.createNativeQuery(
                "SELECT * FROM bank_statement bs " +
                        "WHERE bs.partition_key = :accountId " +
                        "AND bs.status = 'IMPORTED' " +
                        "ORDER BY bs.final_date DESC " +
                        "LIMIT 1",
                AccountStatementPsql.class);
        query.setParameter("accountId", String.valueOf(accountId));

        Optional<AccountStatementPsql> result = query.getResultList().stream().findFirst();

        return result.map(this::fetch);
    }


    @Override
    @Transactional
    public void deleteById(final Long id) {
        jpaFxRateAccountStatementRepository.deleteAllByStatementId(id);
        jpaAccountStatementRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AccountStatement> findByPreviousStatementId(AccountStatement accountStatement) {

        return jpaAccountStatementRepository.findByPreviousStatement(new AccountStatementPsql(accountStatement))
                .map(this::fetch);
    }

    @Override
    public Optional<AccountStatement> findLastUpdatedStatementByUser(String username) {
        return jpaAccountStatementRepository.findFirstByUpdatedByOrderByUpdatedAt(username)
                .map(AccountStatementPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AccountStatement> findAllStatementsOrdered(AccountStatementFilters accountStatementFilters) {

        if (Objects.isNull(accountStatementFilters.getStatus())) {
            accountStatementFilters = accountStatementFilters.toBuilder()
                    .status(AccountStatementStatus.getValues())
                    .build();
        }

        Query query = entityManager.createNativeQuery("WITH RECURSIVE ordered_bank_statements AS (" +
                "select bs.*, 1 as level " +
                "from bank_statement bs " +
                "LEFT JOIN bank_statement bs2 ON " +
                "bs.id = bs2.previous_statement_id " +
                "WHERE bs2.id IS NULL " +
                "AND bs.partition_key = :partitionId " +
                "AND bs.status in :status " +
                "UNION ALL " +
                "select bs.*, os.level + 1 AS level " +
                "from bank_statement bs " +
                "JOIN ordered_bank_statements os ON bs.id = os.previous_statement_id " +
                "WHERE bs.partition_key = :partitionId AND bs.status in :status " +
                ") SELECT * FROM ordered_bank_statements ORDER BY level DESC", AccountStatementPsql.class);
        query.setParameter("partitionId", String.valueOf(accountStatementFilters.getAccountID()));
        query.setParameter("status", accountStatementFilters.getStatus().stream().map(Enum::name).collect(Collectors.toList()));

        List<AccountStatementPsql> resultList = query.getResultList();

        return resultList.stream()
                .map(this::fetch)
                .collect(Collectors.toList());
    }

    private AccountStatement fetch(AccountStatementPsql accountStatementPsql) {
        Set<FxRate> fxRates = jpaFxRateAccountStatementRepository.findByStatement(accountStatementPsql).stream()
                .map(FxRateStatementPsql::getFxRate)
                .map(FxRatePsql::toEntity)
                .collect(Collectors.toSet());
        return accountStatementPsql.toEntity().toBuilder().fxRates(fxRates).build();
    }

    @Override
    public Integer count(AccountStatementFilters filters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(filters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    private void buildWhereClauses(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (Objects.isNull(accountStatementFilters)) {
            return;
        }

        filterByPartition(accountStatementFilters, query);

        filterByFilterText(accountStatementFilters, query);
        filterByCurrency(accountStatementFilters, query);
        filterByStatementId(accountStatementFilters, query);
        filterByPreviousStatement(accountStatementFilters, query);
        filterByInitialDate(accountStatementFilters, query);
        filterByFinalDate(accountStatementFilters, query);
        filterByInitialDirection(accountStatementFilters, query);
        filterByFinalDirection(accountStatementFilters, query);
        filterByInitialAmount(accountStatementFilters, query);
        filterByFinalAmount(accountStatementFilters, query);
        filterByStatus(accountStatementFilters, query);
        filterByStatusDescription(accountStatementFilters, query);
        filterByAccount(accountStatementFilters, query);
        filterByCreatedAt(accountStatementFilters, query);


    }

    private void filterByPartition(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getPartitionKey())) {
            query.where(root.partitionKey.eq(accountStatementFilters.getPartitionKey()));
        }
    }

    private void filterByFilterText(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getFilterText())) {
            query.where(root.statementId.likeIgnoreCase("%" + (accountStatementFilters.getFilterText()) + "%"));
        }
    }

    private void filterByCurrency(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getCurrencyCodes())) {
            query.where(root.currency.code.in(accountStatementFilters.getCurrencyCodes()));
        }
    }

    private void filterByStatementId(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getStatementId())) {
            query.where(root.statementId.likeIgnoreCase("%" + (accountStatementFilters.getStatementId()) + "%"));
        }

    }

    private void filterByPreviousStatement(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getPreviousStatementID())) {
            query.where(root.previousStatement.id.in(accountStatementFilters.getPreviousStatementID()));
        }

    }

    private void filterByInitialDate(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getInitialDateStart())) {
            LocalDate startDate = accountStatementFilters.getInitialDateStart();
            LocalDate endDate = Objects.isNull(accountStatementFilters.getInitialDateEnd()) ? startDate : accountStatementFilters.getInitialDateEnd();
            query.where(root.initialDate.between(startDate, endDate));
        }
    }

    private void filterByFinalDate(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getFinalDateStart())) {
            LocalDate startDate = accountStatementFilters.getFinalDateStart();
            LocalDate endDate = Objects.isNull(accountStatementFilters.getFinalDateEnd()) ? startDate : accountStatementFilters.getFinalDateEnd();
            query.where(root.finalDate.between(startDate, endDate));
        }
    }

    private void filterByInitialDirection(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getInitialDirection())) {

            List<String> directions = accountStatementFilters.getInitialDirection().stream()
                    .map(Direction::valueOf)
                    .map(Direction::name)
                    .collect(Collectors.toList());

            query.where(root.initialDirection.in(directions));


        }
    }

    private void filterByFinalDirection(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getFinalDirection())) {
            List<String> directions = accountStatementFilters.getFinalDirection().stream()
                    .map(Direction::valueOf)
                    .map(Direction::name)
                    .collect(Collectors.toList());

            query.where(root.finalDirection.in(directions));
        }
    }

    private void filterByInitialAmount(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getInitialAmount())) {
            query.where(root.initialAmount.in(accountStatementFilters.getInitialAmount()));
        }

    }

    private void filterByFinalAmount(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getFinalAmount())) {
            query.where(root.finalAmount.in(accountStatementFilters.getFinalAmount()));
        }

    }

    private void filterByStatus(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getStatus())) {
            query.where(root.status.in(accountStatementFilters.getStatus()));
        }
        query.where(root.status.ne(AccountStatementStatus.DISCARDED));
    }

    private void filterByStatusDescription(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getStatusDescription())) {
            query.where(root.statusDescription.in(accountStatementFilters.getStatusDescription()));
        }
    }

    private void filterByAccount(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getAccountID())) {
            query.where(root.account.id.eq(accountStatementFilters.getAccountID()));
        }

    }

    public void filterByCreatedAt(AccountStatementFilters accountStatementFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountStatementFilters.getCreatedAtStart())) {
            LocalDateTime startDate = accountStatementFilters.getCreatedAtStart().with(LocalTime.MIN);
            LocalDateTime endDate = Objects.isNull(accountStatementFilters.getCreatedAtEnd())
                    ? startDate.with(LocalTime.MAX) : accountStatementFilters.getCreatedAtEnd().with(LocalTime.MAX);
            query.where(root.createdAt.between(startDate, endDate));
        }

    }

    //for testing purposes

    @Transactional
    @Override
    public void deleteAllFxRatesAccountStatement() {
        jpaFxRateAccountStatementRepository.deleteAll();
    }

    @Override
    public void save(AccountStatement accountStatement) {
        jpaAccountStatementRepository.save(new AccountStatementPsql(accountStatement));
    }

}
