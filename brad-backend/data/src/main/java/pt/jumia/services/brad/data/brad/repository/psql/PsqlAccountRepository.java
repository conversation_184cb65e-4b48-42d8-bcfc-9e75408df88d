package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.core.Tuple;
import com.querydsl.core.types.SubQueryExpression;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.core.types.dsl.DateExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.AccountPsql;
import pt.jumia.services.brad.data.brad.entities.QAccountDailySummaryPsql;
import pt.jumia.services.brad.data.brad.entities.QAccountPsql;
import pt.jumia.services.brad.data.brad.entities.QAccountStatementPsql;
import pt.jumia.services.brad.data.brad.entities.QApiLogPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaAccountRepository;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaCountryRepository;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaCurrencyRepository;
import pt.jumia.services.brad.data.brad.utils.PartitionCreator;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Account.TroubleShooting;
import pt.jumia.services.brad.domain.entities.dtos.AccountTroubleshootingDto;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountSortFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.StatementSource;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.AccountRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlAccountRepository extends BaseRepository implements AccountRepository {

    private final QAccountPsql root = new QAccountPsql("root");
    private final QAccountStatementPsql statement = new QAccountStatementPsql("statement");
    private final QApiLogPsql apiLog = new QApiLogPsql("apiLog");
    private final QAccountDailySummaryPsql accountDailySummary = new QAccountDailySummaryPsql("accountDailySummary");


    private final JpaAccountRepository jpaAccountRepository;
    private final JpaCountryRepository jpaCountryRepository;
    private final EntityManager entityManager;
    private final PartitionCreator partitionCreator;
    private final JpaCurrencyRepository jpaCurrencyRepository;


    @Override
    @Transactional
    public Account upsert(Account account) throws DatabaseErrorsException {
        Account createdAccount = jpaAccountRepository.save(new AccountPsql(account)).toEntity();
        partitionCreator.createListPartition(String.valueOf(createdAccount.getId()), PartitionCreator.TableNames.BANK_STATEMENT);
        partitionCreator.createListPartition(String.valueOf(createdAccount.getId()), PartitionCreator.TableNames.TRANSACTIONS);
        return createdAccount;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Account> findById(long id) {

        return jpaAccountRepository.findById(id)
                .map(AccountPsql::toEntity);
    }

    @Override
    public Optional<Account> findByAccountNumber(String accountNumber) {
        return jpaAccountRepository.findByAccountNumber(accountNumber)
                .map(AccountPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Account> findAll(AccountFilters accountFilters, AccountSortFilters accountSortFilters,
                                 PageFilters pageFilters) throws EntityErrorsException {
        JPAQuery<AccountPsql> query;

        if (accountFilters != null && accountFilters.isNegativeBalance()) {
            query = new JPAQueryFactory(entityManager)
                    .selectFrom(root)
                    .leftJoin(accountDailySummary).on(
                            root.id.eq(accountDailySummary.account.id)
                                    .and(accountDailySummary.transactionDate.eq(buildLatestSummaryDateSubquery()))
                    )
                    .where(accountDailySummary.finalBalanceUsd.lt(BigDecimal.ZERO));
        } else {
            query = new JPAQueryFactory(entityManager).selectFrom(root);
        }

        if (Objects.nonNull(accountFilters)) {
            applyProjectionSelect(accountFilters.getSelectedFields(), query, root, AccountPsql.class);
        }
        buildWhereClauses(accountFilters, query);
        applySort(accountSortFilters, query, root, Account.SortingFields.class);
        applyPagination(pageFilters, query);


        return query.fetch().stream()
                .map(AccountPsql::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<AccountTroubleshootingDto> findAllAccountsInTroubleShooting(AccountFilters accountFilters,
                                                                            AccountSortFilters accountSortFilters,
                                                                            PageFilters pageFilters) throws EntityErrorsException {

        final QAccountStatementPsql statementToGetLatest = new QAccountStatementPsql("statementToGetLatest");
        final QApiLogPsql apiLogToGetTheLatest = new QApiLogPsql("apiLogToGetLatest");
        JPAQuery<AccountPsql> query = new JPAQueryFactory(entityManager).selectFrom(root);

        buildWhereClauses(accountFilters, query);
        addTroubleShootingFilters(query, buildTroubleshootingPredicate(accountFilters));
        applySort(accountSortFilters, query, root, Account.SortingFields.class);
        applyPagination(pageFilters, query);

        List<Tuple> tuples = query
                .select(
                        root,
                        // CASE for is_out_of_sync
                        new CaseBuilder()
                                .when(isAccountOutOfSync())
                                .then(1)
                                .otherwise(0).max().as("isOutOfSync"),
                        // CASE for manual_upload_missing
                        new CaseBuilder()
                                .when(isAccountMissingManualUpload())
                                .then(1)
                                .otherwise(0).max().as("manualUploadMissing"),
                        // CASE for failed_statement_validation
                        new CaseBuilder()
                                .when(statement.status.eq(AccountStatementStatus.REVIEW))
                                .then(1)
                                .otherwise(0).max().as("failedStatementValidation")
                )
                .from(root)
                .leftJoin(apiLogToGetTheLatest).on(hasFailedApiLog(apiLogToGetTheLatest))

                .leftJoin(apiLog).on(hasFailedApiLog(apiLog)
                        .and(apiLog.createdAt.gt(apiLogToGetTheLatest.createdAt)))

                .leftJoin(statementToGetLatest)
                .on(root.id.eq(statementToGetLatest.account.id))

                .leftJoin(statement)
                .on(root.id.eq(statement.account.id)
                        .and((statement.finalDate.gt(statementToGetLatest.finalDate).or(statement.createdAt.goe(statementToGetLatest.createdAt)))))
                .distinct()
                .groupBy(root)
                .fetch();

        return tuples.stream()
                .map(tuple -> new AccountTroubleshootingDto(
                        Objects.requireNonNull(tuple.get(root)).toEntity(),
                        Objects.equals(tuple.get(Expressions.numberPath(Integer.class, "isOutOfSync")), 1),
                        Objects.equals(tuple.get(Expressions.numberPath(Integer.class, "manualUploadMissing")), 1),
                        Objects.equals(tuple.get(Expressions.numberPath(Integer.class, "failedStatementValidation")), 1)
                ))
                .collect(Collectors.toList());
    }


    @Override
    @Transactional(readOnly = true)
    public Integer count(AccountFilters accountFilters) {

        JPAQuery<Long> query;

        if (accountFilters != null && accountFilters.isNegativeBalance()) {
            query = new JPAQueryFactory(entityManager)
                    .select(root.id.count())
                    .from(root)
                    .leftJoin(accountDailySummary).on(
                            root.id.eq(accountDailySummary.account.id)
                                    .and(accountDailySummary.transactionDate.eq(buildLatestSummaryDateSubquery()))
                    )
                    .where(accountDailySummary.finalBalanceUsd.lt(BigDecimal.ZERO));
        } else {
            query = new JPAQueryFactory(entityManager)
                    .select(root.id.count())
                    .from(root);
        }

        buildWhereClauses(accountFilters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    @Override
    @Transactional(readOnly = true)
    public Integer countTroubleShooting(AccountFilters accountFilters) {

        final QAccountStatementPsql statementToGetLatest = new QAccountStatementPsql("statementToGetLatest");
        final QApiLogPsql apiLogToGetTheLatest = new QApiLogPsql("apiLogToGetLatest");

        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.countDistinct())
                .from(root)
                .leftJoin(apiLogToGetTheLatest).on(hasFailedApiLog(apiLogToGetTheLatest))

                .leftJoin(apiLog).on(hasFailedApiLog(apiLog)
                        .and(apiLog.createdAt.gt(apiLogToGetTheLatest.createdAt)))

                .leftJoin(statementToGetLatest)
                .on(root.id.eq(statementToGetLatest.account.id))

                .leftJoin(statement)
                .on(root.id.eq(statement.account.id)
                        .and((statement.finalDate.gt(statementToGetLatest.finalDate).or(statement.createdAt.goe(statementToGetLatest.createdAt)))))
                .distinct();

        buildWhereClauses(accountFilters, query);
        addTroubleShootingFilters(query, buildTroubleshootingPredicate(accountFilters));
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }


    @Override
    public Optional<Account> findAccountNavReference(String navReference) {
        return jpaAccountRepository.findByNavReference(navReference)
                .map(AccountPsql::toEntity);
    }

    @Override
    public Optional<Account> findAccountNavReferenceAndCompanyID(String navReference, String companyID) {
        return jpaAccountRepository.findByNavReferenceAndCompanyID(navReference, companyID)
                .map(AccountPsql::toEntity);
    }

    @Override
    public List<Account> findAccountNavReferences(AccountFilters filters) {
        JPAQuery<AccountPsql> query = new JPAQueryFactory(entityManager).selectFrom(root);

        filterByCountry(filters, query);
        return query.fetch().stream()
                .map(AccountPsql::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    public Integer countByIsin(String isin) {
        return jpaAccountRepository.countByIsin(isin);
    }

    @Override
    public Integer countByContractId(String contractId) {
        return jpaAccountRepository.countByContractId(contractId);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Account> findByNavReferences(List<String> navReferences) {
        if (navReferences == null || navReferences.isEmpty()) {
            return Map.of();
        }
        
        List<AccountPsql> accounts = jpaAccountRepository.findByNavReferenceIn(navReferences);
        
        return accounts.stream()
                .map(AccountPsql::toEntity)
                .collect(Collectors.toMap(
                        Account::getNavReference,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
    }

    @Override
    @Transactional
    public void deleteById(long id) {
        jpaAccountRepository.deleteById(id);
    }

    private void buildWhereClauses(AccountFilters accountFilters, JPAQuery<?> query) {
        if (Objects.isNull(accountFilters)) {
            return;
        }
        filterByFilterText(accountFilters, query);
        filterByCompanyID(accountFilters, query);
        filterByCountry(accountFilters, query);
        filterByPartner(accountFilters, query);
        filterByNavReference(accountFilters, query);
        filterByBeneficiaryName(accountFilters, query);
        filterByBeneficiaryAddress(accountFilters, query);
        filterByIban(accountFilters, query);
        filterByAccountNumber(accountFilters, query);
        filterByAccountName(accountFilters, query);
        filterByPhoneNumber(accountFilters, query);
        filterBySwiftCode(accountFilters, query);
        filterByBankRoutingCode(accountFilters, query);
        filterBySortCode(accountFilters, query);
        filterByBranchCode(accountFilters, query);
        filterByRib(accountFilters, query);
        filterByType(accountFilters, query);
        filterBySubType(accountFilters, query);
        filterByStatus(accountFilters, query);
        filterByStatementSource(accountFilters, query);
        filterByStatementPeriodicity(accountFilters, query);
        filterByLastProcessedStatementDate(accountFilters, query);
        filterByCurrencyCode(accountFilters, query);
        filterByCreatedAt(accountFilters, query);
        filterByinvestmentId(accountFilters, query);
    }

    public void filterByFilterText(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getFilterText())) {
            query.where(root.accountName.likeIgnoreCase("%" + accountFilters.getFilterText() + "%").
                    or(root.navReference.likeIgnoreCase("%" + accountFilters.getFilterText() + "%")));
        }
    }

    public void filterByCompanyID(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getCompanyID())) {
            query.where(root.companyID.likeIgnoreCase("%" + accountFilters.getCompanyID() + "%"));
        }
    }

    public void filterByCountry(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getCountryCodes())) {
            query.where(root.country.id.in(accountFilters.getCountryCodes()));
        }
    }

    public void filterByPartner(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getPartner())) {
            query.where(root.partner.likeIgnoreCase("%" + accountFilters.getPartner() + "%"));
        }
    }

    public void filterByNavReference(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getNavReference())) {
            query.where(root.navReference.likeIgnoreCase("%" + accountFilters.getNavReference() + "%"));
        }
    }

    public void filterByBeneficiaryName(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getBeneficiaryName())) {
            query.where(root.beneficiaryName.likeIgnoreCase("%" + accountFilters.getBeneficiaryName() + "%"));
        }
    }

    public void filterByBeneficiaryAddress(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getBeneficiaryAddress())) {
            query.where(root.beneficiaryAddress.likeIgnoreCase("%" + accountFilters.getBeneficiaryAddress() + "%"));
        }
    }

    public void filterByIban(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getIban())) {
            query.where(root.iban.likeIgnoreCase("%" + accountFilters.getIban() + "%"));
        }
    }

    public void filterByAccountNumber(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getAccountNumber())) {
            query.where(root.accountNumber.likeIgnoreCase("%" + accountFilters.getAccountNumber() + "%"));
        }
    }

    public void filterByAccountName(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getAccountName())) {
            query.where(root.accountName.likeIgnoreCase("%" + accountFilters.getAccountName() + "%"));
        }
    }

    public void filterByPhoneNumber(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getPhoneNumber())) {
            query.where(root.phoneNumber.likeIgnoreCase("%" + accountFilters.getPhoneNumber() + "%"));
        }
    }

    public void filterBySwiftCode(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getSwiftCode())) {
            query.where(root.swiftCode.likeIgnoreCase("%" + accountFilters.getSwiftCode() + "%"));
        }
    }

    public void filterByBankRoutingCode(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getBankRoutingCode())) {
            query.where(root.bankRoutingCode.likeIgnoreCase("%" + accountFilters.getBankRoutingCode() + "%"));
        }
    }

    public void filterBySortCode(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getSortCode())) {
            query.where(root.sortCode.likeIgnoreCase("%" + accountFilters.getSortCode() + "%"));
        }
    }

    public void filterByBranchCode(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getBranchCode())) {
            query.where(root.branchCode.likeIgnoreCase("%" + accountFilters.getBranchCode() + "%"));
        }
    }

    public void filterByRib(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getRib())) {
            query.where(root.rib.likeIgnoreCase("%" + accountFilters.getRib() + "%"));
        }
    }

    public void filterByType(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getTypes())) {
            List<String> types = accountFilters.getTypes()
                    .stream()
                    .map(Enum::name)
                    .toList();
            query.where(root.type.in(types));
        }
    }

    public void filterBySubType(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getSubTypes())) {
            List<String> subTypes = accountFilters.getSubTypes()
                    .stream()
                    .map(Enum::name)
                    .toList();
            query.where(root.subType.in(subTypes));
        }
    }

    public void filterByStatus(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getStatus())) {
            List<String> statuses = accountFilters.getStatus()
                    .stream()
                    .map(Account.Status::getValue)
                    .toList();
            query.where(root.status.in(statuses));
        }
    }

    public void filterByStatementSource(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getStatementSource())) {
            query.where(root.statementSource.in(accountFilters.getStatementSource().getValue()));
        }
    }

    public void filterByStatementPeriodicity(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getStatementPeriodicity())) {
            List<String> statementPeriodicities = accountFilters.getStatementPeriodicity()
                    .stream()
                    .map(Enum::name)
                    .toList();
            query.where(root.statementPeriodicity.in(statementPeriodicities));
        }
    }

    public void filterByCurrencyCode(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getCurrencyCodes())) {
            query.where(root.currency.code.in(accountFilters.getCurrencyCodes()));
        }
    }

    public void filterByCreatedAt(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getCreatedAtStart())) {
            LocalDateTime startDate = accountFilters.getCreatedAtStart().with(LocalTime.MIN);
            LocalDateTime endDate = Objects.isNull(accountFilters.getCreatedAtEnd()) ? startDate.with(LocalTime.MAX) :
                    accountFilters.getCreatedAtEnd().with(LocalTime.MAX);
            query.where(root.createdAt.between(startDate, endDate));
        }
    }

    public void filterByLastProcessedStatementDate(AccountFilters accountFilters, JPAQuery<?> query) {

        if (Objects.nonNull(accountFilters.getLastProcessedStatementDateStart())) {
            LocalDate startDate = accountFilters.getLastProcessedStatementDateStart();
            LocalDate endDate = Objects.isNull(accountFilters.getLastProcessedStatementDateEnd()) ? startDate :
                    accountFilters.getLastProcessedStatementDateEnd();
            query.where(root.lastProcessedStatementDate.between(startDate, endDate));
        }
    }

    public void filterByinvestmentId(AccountFilters accountFilters, JPAQuery<?> query) {
        if (!Objects.isNull(accountFilters.getInvestmentId())) {
            String investmentId = accountFilters.getInvestmentId();
            query.where(root.isin.eq(investmentId).or(root.contractId.eq(investmentId)));
        }
    }

    private BooleanExpression statementPeriodOutOfSyncCondition(DateExpression<LocalDate> datePath) {

        LocalDateTime todayTime = LocalDateTime.now(ZoneOffset.UTC);
        LocalDate today = todayTime.toLocalDate();
        return statement.isNull().and(
                        root.statementPeriodicity.eq("DAILY")
                                .and(root.createdAt.before(todayTime.minusHours(25)))
                                .or(// WEEKLY
                                        (root.statementPeriodicity.eq("WEEKLY")
                                                .and(root.createdAt.before(todayTime.minusWeeks(1)))))
                                .or(//MONTHLY
                                        (root.statementPeriodicity.eq("MONTHLY")
                                                .and(root.createdAt.before(todayTime.minusMonths(1)))))
                                .or(//BI_MONTHLY
                                        (root.statementPeriodicity.eq("BI_MONTHLY")
                                                .and(root.createdAt.before(todayTime.minusDays(14)))))
                                .or(//QUARTERLY
                                        (root.statementPeriodicity.eq("QUARTERLY")
                                                .and(root.createdAt.before(todayTime.minusMonths(3))))))
                .or(datePath.isNull())
                .or(datePath.isNotNull()
                        .and(root.statementPeriodicity.eq("DAILY")
                                .and(datePath.before(today.minusDays(1)))
                                .or(// WEEKLY
                                        (root.statementPeriodicity.eq("WEEKLY")
                                                .and(datePath.before(today.minusWeeks(1)))))
                                .or(//MONTHLY
                                        (root.statementPeriodicity.eq("MONTHLY")
                                                .and(datePath.before(today.minusMonths(1)))))
                                .or(//BI_MONTHLY
                                        (root.statementPeriodicity.eq("BI_MONTHLY")
                                                .and(datePath.before(today.minusDays(14)))))
                                .or(//QUARTERLY
                                        (root.statementPeriodicity.eq("QUARTERLY")
                                                .and(datePath.before(today.minusMonths(3)))))));

    }

    private void addTroubleShootingFilters(final JPAQuery<?> query, final BooleanExpression booleanExpression) {

        if (Objects.isNull(booleanExpression)) {
            return;
        }
        query.where(booleanExpression);
    }

    private BooleanExpression buildTroubleshootingPredicate(final AccountFilters filters) {

        if (Objects.isNull(filters)) {
            return null;
        }

        BooleanExpression statementInReview = statement.status.eq(AccountStatementStatus.REVIEW);

        BooleanExpression missingManualUpload = isAccountMissingManualUpload();

        BooleanExpression outOfSync = isAccountOutOfSync();

        if (Objects.nonNull(filters.getTroubleshooting())) {
            if (filters.getTroubleshooting().equals(TroubleShooting.STATEMENT_SYNC)) {
                return outOfSync;
            } else if (filters.getTroubleshooting().equals(TroubleShooting.STATEMENT_VALIDATION)) {
                return statementInReview;
            } else if (filters.getTroubleshooting().equals(TroubleShooting.MISSING_MANUAL_UPLOAD)) {
                return missingManualUpload;
            } else {
                return statementInReview.or(outOfSync).or(missingManualUpload);
            }
        } else {
            return statementInReview.or(outOfSync).or(missingManualUpload);
        }

    }

    private BooleanExpression hasFailedApiLog(QApiLogPsql logPsql) {
        return logPsql.logType.in("SFTP_BANK_STATEMENT_IMPORT", "API_BANK_STATEMENT_FETCH")
            .and(logPsql.logStatus.eq("FAILURE"))
            .and(logPsql.relatedEntityId.contains(root.accountNumber.trim()));
    }

    private BooleanExpression isAccountMissingManualUpload() {

        return root.statementSource.eq(StatementSource.MANUAL_UPLOAD.name())
                .and(statementPeriodOutOfSyncCondition(statement.finalDate)
                        .and(statementPeriodOutOfSyncCondition(root.lastProcessedStatementDate)));
    }

    private BooleanExpression isAccountOutOfSync() {

        return (root.statementSource.eq(StatementSource.SFTP.name())
                .or(root.statementSource.eq(StatementSource.API.name())))
                .and((hasFailedApiLog(apiLog)).or((statementPeriodOutOfSyncCondition(statement.finalDate).and(
                        statementPeriodOutOfSyncCondition(root.lastProcessedStatementDate))).and(apiLog.isNull())));
    }

    private SubQueryExpression<LocalDate> buildLatestSummaryDateSubquery() {
        QAccountDailySummaryPsql subSummary = new QAccountDailySummaryPsql("subSummary");
        
        return JPAExpressions
                .select(subSummary.transactionDate.max())
                .from(subSummary)
                .where(subSummary.account.id.eq(root.id));
    }

}
