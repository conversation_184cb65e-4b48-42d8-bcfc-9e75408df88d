package pt.jumia.services.brad.data.brad.repository.psql;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Jobs;
import pt.jumia.services.brad.domain.repository.brad.JobsRepository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class PsqlJobsRepository implements JobsRepository {

    private final SchedulerFactoryBean schedulerFactoryBean;
    @Override
    public List<Jobs> findAll() throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        List<Jobs> jobs = new ArrayList<>();

        for (String jobName : scheduler.getJobGroupNames()) {
            for (JobKey jobKey : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(jobName))) {
                jobs.add(getJobDetail(scheduler, jobKey));
            }
        }

        return jobs;
    }

    @Override
    public Jobs findJobByName(String jobName) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobDetail jobDetail = scheduler.getJobDetail(new JobKey(jobName));
        return getJobDetail(scheduler, jobDetail.getKey());
    }

    private Jobs getJobDetail(Scheduler scheduler, JobKey jobKey) throws SchedulerException {
        List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
        Trigger trigger = triggers.get(0);
        CronTrigger cronTrigger = (CronTrigger) scheduler.getTrigger(trigger.getKey());
        TimeZone timeZone = cronTrigger.getTimeZone();
        LocalDateTime lastFiredTime = cronTrigger.getPreviousFireTime() != null ?
                LocalDateTime.ofInstant(cronTrigger.getPreviousFireTime().toInstant(), timeZone.toZoneId()) : null;
        LocalDateTime nextFireTime = cronTrigger.getNextFireTime() != null ?
                LocalDateTime.ofInstant(cronTrigger.getNextFireTime().toInstant(), timeZone.toZoneId()) : null;
        return new Jobs(jobKey.getName(),
                cronTrigger.getCronExpression(),
                scheduler.getTriggerState(trigger.getKey()).name(),
                cronTrigger.getTimeZone().getID(),
                lastFiredTime,
                nextFireTime
        );
    }

    @Override
    public Jobs update(String jobName, Jobs job) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobKey jobKey = new JobKey(jobName);
        JobDetail jobDetail;
        List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);

        if (!triggers.isEmpty()) {
            Trigger trigger = triggers.get(0);

            TriggerKey triggerKey = trigger.getKey();
            CronTrigger cronTrigger = (CronTrigger) scheduler.getTrigger(triggerKey);
            CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(job.getCronExpression());
            trigger = cronTrigger.getTriggerBuilder().withSchedule(cronScheduleBuilder).build();

            scheduler.rescheduleJob(triggerKey, trigger);
        }

        jobDetail = scheduler.getJobDetail(new JobKey(jobName));
        triggers = scheduler.getTriggersOfJob(jobDetail.getKey());
        Trigger trigger = triggers.get(0);
        CronTrigger cronTrigger = (CronTrigger) scheduler.getTrigger(trigger.getKey());
        LocalDateTime lastFiredTime = cronTrigger.getPreviousFireTime() != null ?
                LocalDateTime.ofInstant(cronTrigger.getPreviousFireTime().toInstant(), cronTrigger.getTimeZone().toZoneId()) : null;
        LocalDateTime nextFireTime = cronTrigger.getNextFireTime() != null ?
                LocalDateTime.ofInstant(cronTrigger.getNextFireTime().toInstant(), cronTrigger.getTimeZone().toZoneId()) : null;
        return new Jobs(jobDetail.getKey().getName(),
                cronTrigger.getCronExpression(),
                scheduler.getTriggerState(trigger.getKey()).name(),
                cronTrigger.getTimeZone().getID(),
                lastFiredTime,
                nextFireTime
        );
    }

    @Override
    public void forceRun(String jobName) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        log.debug("Bale sync job: Triggering scheduler job: {}", jobName);
        scheduler.triggerJob(new JobKey(jobName));
    }

    @Override
    public void toggleState(String jobName) throws SchedulerException {

        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobKey jobKey = new JobKey(jobName);

        List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
        if (!triggers.isEmpty()) {
            for (Trigger trigger : triggers) {

                TriggerKey triggerKey = trigger.getKey();

                Trigger.TriggerState triggerState = scheduler.getTriggerState(triggerKey);

                if (triggerState == Trigger.TriggerState.PAUSED) {
                    scheduler.resumeJob(jobKey);
                } else {
                    scheduler.pauseJob(jobKey);
                }
            }
        } else {
            log.warn("No triggers found for job: {}", jobName);
        }
    }

    @Override
    public Jobs getEntityWithJob(String jobName) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobDetail jobDetail = scheduler.getJobDetail(new JobKey(jobName));
        return getJobDetail(scheduler, jobDetail.getKey());
    }

}
