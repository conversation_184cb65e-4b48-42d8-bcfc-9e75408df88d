package pt.jumia.services.brad.data.brad.job;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * Simplified Bale synchronization job using Spring Batch.
 *
 * This replaces the over-engineered custom batch processing with proven Spring Batch patterns.
 *
 * Key improvements:
 * - Delegates complex processing to Spring Batch framework
 * - Eliminates custom error handling, memory monitoring, and batch size calculations
 * - Provides better observability through Spring Batch job execution tracking
 * - Maintains compatibility with existing Quartz scheduling
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BaleSyncJob extends QuartzJobBean {

    private final JobLauncher jobLauncher;
    private final Job baleJob;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        try {
            log.info("Starting bale sync job execution using Spring Batch");

            // Create unique job parameters to ensure each execution is treated as a new job instance
            JobParameters jobParameters = new JobParametersBuilder()
                    .addLong("timestamp", System.currentTimeMillis())
                    .addString("triggeredBy", "quartz-scheduler")
                    .toJobParameters();

            // Launch the Spring Batch job
            jobLauncher.run(baleJob, jobParameters);

            log.info("Bale sync job completed successfully");

        } catch (Exception e) {
            log.error("Bale sync job execution failed: {}", e.getMessage(), e);
            throw new JobExecutionException("Bale sync job execution failed", e, true);
        }
    }

}
