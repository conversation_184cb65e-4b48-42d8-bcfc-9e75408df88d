package pt.jumia.services.brad.data.bale.repository.mssql;

import com.querydsl.jpa.impl.JPAQuery;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.bale.configuration.BaleEntityManagerConfiguration;
import pt.jumia.services.brad.data.bale.entities.BaleMssql;
import pt.jumia.services.brad.data.bale.entities.QBaleMssql;
import pt.jumia.services.brad.data.brad.repository.psql.PsqlExecutionLogRepository;
import pt.jumia.services.brad.data.shared.BaseMssqlRepository;
import pt.jumia.services.brad.data.shared.ViewContext;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.BaleRepository;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Repository
public class MssqlBaleRepository extends BaseMssqlRepository implements BaleRepository  {

    private final QBaleMssql root = new QBaleMssql("root");
    private final PsqlExecutionLogRepository psqlExecutionLogRepository;
    private final EntityManager entityManager;

    @SuppressWarnings({"PMD"})
    @SuppressFBWarnings("EI_EXPOSE_REP2")
    public MssqlBaleRepository(@Qualifier("baleEntityManager") EntityManager entityManager,
                               PsqlExecutionLogRepository psqlExecutionLogRepository,
                               @Qualifier("baleEntityManagerConfiguration") BaleEntityManagerConfiguration baleEntityManagerConfiguration){
        super(baleEntityManagerConfiguration, psqlExecutionLogRepository);
        this.psqlExecutionLogRepository = psqlExecutionLogRepository;
        this.entityManager = entityManager;
    }

    @Override
    public List<Bale> findAll(Integer entryNo, ViewEntity baleViewEntity, boolean retrying, ExecutionLog executionLog)
            throws DatabaseErrorsException, EntityErrorsException, ParseException {
        if (Objects.isNull(baleViewEntity)) {
            throw EntityErrorsException.createNullClassError(ViewEntity.class);
        }
        Optional<ExecutionLog> optionalExecutionLog = this.psqlExecutionLogRepository.findById(executionLog.getId());
        if (optionalExecutionLog.isPresent()) {
           executionLog = optionalExecutionLog.get();
        }
        try {
            ViewContext.setCurrentContext(baleViewEntity);
            return performQuery(entryNo, executionLog, baleViewEntity);
        } catch (Exception e) {
            handleException(e, retrying, baleViewEntity, executionLog);
        } finally {
            ViewContext.clear();
        }
        return List.of();
    }

    @Override
    public List<Bale> findAllBatched(Integer entryNo, ViewEntity baleViewEntity, boolean retrying, ExecutionLog executionLog,
                                    int offset, int limit) throws DatabaseErrorsException, EntityErrorsException, ParseException {
        if (Objects.isNull(baleViewEntity)) {
            throw EntityErrorsException.createNullClassError(ViewEntity.class);
        }
        Optional<ExecutionLog> optionalExecutionLog = this.psqlExecutionLogRepository.findById(executionLog.getId());
        if (optionalExecutionLog.isPresent()) {
            executionLog = optionalExecutionLog.get();
        }
        try {
            ViewContext.setCurrentContext(baleViewEntity);
            return performBatchedQuery(entryNo, executionLog, baleViewEntity, offset, limit);
        } catch (Exception e) {
            handleException(e, retrying, baleViewEntity, executionLog);
        } finally {
            ViewContext.clear();
        }
        return List.of();
    }


    @Override
    @Transactional(readOnly = true)
    public String fetchCompanyId(ViewEntity baleViewEntity, boolean retrying)
            throws DatabaseErrorsException, EntityErrorsException {
        if (Objects.isNull(baleViewEntity)) {
            throw EntityErrorsException.createNullClassError(ViewEntity.class);
        }
        try {
            ViewContext.setCurrentContext(baleViewEntity);

            JPAQuery<String> query = new JPAQuery<>(entityManager)
                    .select(root.idCompany)
                    .from(root);
            return query.fetchFirst();
        } catch (Exception e) {
            this.handleException(e, retrying, baleViewEntity, null);
        } finally {
            ViewContext.clear();
        }
        return null;
    }

    @Override
    public Integer findLastBaleWithOffset(ViewEntity baleViewEntity, Integer offset) throws EntityErrorsException {
        if (Objects.isNull(baleViewEntity)) {
            throw EntityErrorsException.createNullClassError(ViewEntity.class);
        }
        try {
            ViewContext.setCurrentContext(baleViewEntity);

            JPAQuery<BaleMssql> query = new JPAQuery<>(entityManager)
                    .select(root)
                    .from(root)
                    .orderBy(root.entryNo.desc())
                    .offset(offset)
                    .limit(1);
            return query.fetchFirst().toEntity().getEntryNo();
        } catch (Exception e) {
            log.error("Bale sync: Error fetching last bale with offset: {}", e.getMessage());
        } finally {
            ViewContext.clear();
        }
        return null;
    }

    @Override
    public List<Bale> findAllLastBaleWithOffset(ViewEntity baleViewEntity, Integer offset) throws EntityErrorsException {
        if (Objects.isNull(baleViewEntity)) {
            throw EntityErrorsException.createNullClassError(ViewEntity.class);
        }
        try {
            ViewContext.setCurrentContext(baleViewEntity);

            JPAQuery<BaleMssql> query = new JPAQuery<>(entityManager)
                    .select(root)
                    .from(root)
                    .orderBy(root.entryNo.desc())
                    .offset(offset);
            return query.fetch().stream()
                    .map(baleMssql -> Objects.isNull(baleMssql) ? null : baleMssql.toEntity())
                    .toList();
        } catch (Exception e) {
            log.error("Bale sync: Error fetching all last bales with offset: {}", e.getMessage());
        } finally {
            ViewContext.clear();
        }
        return List.of();
    }



    @Override
    public long countBales(Integer entryNo, ViewEntity baleViewEntity, boolean retrying)
            throws DatabaseErrorsException, EntityErrorsException {
        
        log.debug("Bale sync: Counting total bales for view: {}", baleViewEntity.getViewName());

        try {
            ExecutionLog dummyLog = ExecutionLog.builder()
                    .id(-1L)
                    .logType(ExecutionLog.ExecutionLogType.BALE)
                    .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
                    .build();
            
            List<Bale> sampleBales = findAllBatched(entryNo, baleViewEntity, retrying, dummyLog, 0, 10);
            
            if (sampleBales.isEmpty()) {
                return 0;
            } else {
                log.debug("Bale sync: Found bales, but exact count not available. Returning estimated count.");
                return -1;
            }
            
        } catch (ParseException e) {
            log.error("Bale sync: Error counting bales: {}", e.getMessage());
            throw new DatabaseErrorsException("Error counting bales: " + e.getMessage());
        }
    }


    private List<Bale> performQuery(Integer entryNo, ExecutionLog executionLog, ViewEntity baleViewEntity) {
        int pageSize = 1000;
        int page = 0;

        JPAQuery<BaleMssql> query = new JPAQuery<>(entityManager)
                .select(root)
                .from(root);

        boolean isReversed = false;
        filterByEntryNo(entryNo, query);
        filterOutReverse(isReversed, query);
        List<BaleMssql> allBalesMssqlList = new ArrayList<>();
        LocalDateTime startTime = LocalDateTime.now();
        List<BaleMssql> fetchedBalesMssqlList;
        do {
            fetchedBalesMssqlList = query.distinct()
                    .offset((long) page * pageSize)
                    .limit(pageSize)
                    .fetch();
            page++;
            allBalesMssqlList.addAll(fetchedBalesMssqlList);
        } while (!fetchedBalesMssqlList.isEmpty());

        LocalDateTime endTime = LocalDateTime.now();

        String appliedFilterString = "ViewEntity: " + baleViewEntity.getDatabaseName() + " - " + baleViewEntity.getViewName() +
                "\nentryNo: " + entryNo + ", isReversed: " + isReversed;
        logExecution(startTime, endTime, allBalesMssqlList.size(), appliedFilterString, query.toString(), executionLog);
        return allBalesMssqlList.stream()
                .map(baleMssql -> Objects.isNull(baleMssql) ? null : baleMssql.toEntity())
                .toList();
    }

    private List<Bale> performBatchedQuery(Integer entryNo, ExecutionLog executionLog, ViewEntity baleViewEntity, 
                                          int offset, int limit) {
        JPAQuery<BaleMssql> query = new JPAQuery<>(entityManager)
                .select(root)
                .from(root);

        boolean isReversed = false;
        filterByEntryNo(entryNo, query);
        filterOutReverse(isReversed, query);
        
        LocalDateTime startTime = LocalDateTime.now();
        
        List<BaleMssql> balesMssqlList = query.distinct()
                .offset(offset)
                .limit(limit)
                .fetch();
        
        LocalDateTime endTime = LocalDateTime.now();
        
        String appliedFilterString = "ViewEntity: " + baleViewEntity.getDatabaseName() + " - " + baleViewEntity.getViewName() +
                "\nentryNo: " + entryNo + ", isReversed: " + isReversed + ", offset: " + offset + ", limit: " + limit;
        
        logExecution(startTime, endTime, balesMssqlList.size(), appliedFilterString, query.toString(), executionLog);
        
        return balesMssqlList.stream()
                .map(baleMssql -> Objects.isNull(baleMssql) ? null : baleMssql.toEntity())
                .toList();
    }


    private void filterByEntryNo(Integer entryNo, JPAQuery<BaleMssql> query) {
        if (Objects.nonNull(entryNo)) {
            query.where(root.entryNo.gt(entryNo));
        }
    }

    private void filterOutReverse(boolean bool, JPAQuery<BaleMssql> query) {
        query.where(root.isReversed.eq(bool));
    }
}
