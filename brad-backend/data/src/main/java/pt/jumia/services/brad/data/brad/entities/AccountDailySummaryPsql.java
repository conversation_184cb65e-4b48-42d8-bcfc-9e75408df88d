package pt.jumia.services.brad.data.brad.entities;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinColumns;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.NoArgsConstructor;
import lombok.Setter;
import pt.jumia.services.brad.data.brad.entities.fxrates.FxRatePsql;
import pt.jumia.services.brad.data.brad.entities.keys.AccountDailySummaryId;
import pt.jumia.services.brad.data.shared.BaseEntityFieldMap;
import pt.jumia.services.brad.domain.entities.account.AccountDailySummary;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

@Entity
@Table(name = "account_daily_summary")
@IdClass(AccountDailySummaryId.class)
@NoArgsConstructor
public class AccountDailySummaryPsql extends BaseEntityFieldMap<AccountDailySummary.SortingFields> {

    @Id
    @Column(name = "transaction_date", nullable = false)
    private LocalDate transactionDate;

    @Id
    @ManyToOne(optional = false)
    @JoinColumn(name = "account_id", referencedColumnName = "id", nullable = false)
    private AccountPsql account;

    @Column(name = "total_credit_amount")
    private BigDecimal totalCreditAmount;

    @Column(name = "total_debit_amount")
    private BigDecimal totalDebitAmount;

    @Column(name = "total_credit_amount_usd")
    private BigDecimal totalCreditAmountUsd;

    @Column(name = "total_debit_amount_usd")
    private BigDecimal totalDebitAmountUsd;

    @Column(name = "net_amount")
    private BigDecimal netAmount;

    @Column(name = "net_amount_usd")
    private BigDecimal netAmountUsd;

    @Column(name = "initial_balance")
    private BigDecimal initialBalance;

    @Column(name = "final_balance")
    private BigDecimal finalBalance;

    @Column(name = "initial_balance_usd")
    private BigDecimal initialBalanceUsd;

    @Column(name = "final_balance_usd")
    private BigDecimal finalBalanceUsd;

    @Column(name = "transactions_count")
    private Integer transactionsCount;

    @Column(name = "debit_transactions_count")
    private Integer debitTransactionsCount;

    @Column(name = "credit_transactions_count")
    private Integer creditTransactionsCount;

    @ManyToOne
    @JoinColumn(name = "currency_id", nullable = false)
    private CurrencyPsql currency;

    @Setter
    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumns({@JoinColumn(name = "fx_rate_id", referencedColumnName = "id")})
    private FxRatePsql fxRate;

    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    public AccountDailySummaryPsql(AccountDailySummary accountDailySummary) {

        this.transactionDate = accountDailySummary.getTransactionDate();
        this.account = new AccountPsql(accountDailySummary.getAccount());
        this.totalCreditAmount = accountDailySummary.getTotalCreditAmount();
        this.totalDebitAmount = accountDailySummary.getTotalDebitAmount();
        this.totalCreditAmountUsd = accountDailySummary.getTotalCreditAmountUsd();
        this.totalDebitAmountUsd = accountDailySummary.getTotalDebitAmountUsd();
        this.netAmount = accountDailySummary.getNetAmount();
        this.netAmountUsd = accountDailySummary.getNetAmountUsd();
        this.initialBalance = accountDailySummary.getInitialBalance();
        this.finalBalance = accountDailySummary.getFinalBalance();
        this.initialBalanceUsd = accountDailySummary.getInitialBalanceUsd();
        this.finalBalanceUsd = accountDailySummary.getFinalBalanceUsd();
        this.transactionsCount = accountDailySummary.getTransactionsCount();
        this.debitTransactionsCount = accountDailySummary.getDebitTransactionsCount();
        this.creditTransactionsCount = accountDailySummary.getCreditTransactionsCount();
        this.currency = new CurrencyPsql(accountDailySummary.getCurrency());
        this.fxRate = Objects.isNull(accountDailySummary.getFxRate()) ? null : new FxRatePsql(accountDailySummary.getFxRate());
        this.createdAt = accountDailySummary.getCreatedAt();
        this.updatedAt = accountDailySummary.getUpdatedAt();
    }

    public AccountDailySummary toEntity() {

        return AccountDailySummary.builder()
            .transactionDate(this.transactionDate)
            .account(this.account.toEntity())
            .totalCreditAmount(this.totalCreditAmount)
            .totalDebitAmount(this.totalDebitAmount)
            .totalCreditAmountUsd(this.totalCreditAmountUsd)
            .totalDebitAmountUsd(this.totalDebitAmountUsd)
            .netAmount(this.netAmount)
            .netAmountUsd(this.netAmountUsd)
            .initialBalance(this.initialBalance)
            .finalBalance(this.finalBalance)
            .initialBalanceUsd(this.initialBalanceUsd)
            .finalBalanceUsd(this.finalBalanceUsd)
            .transactionsCount(this.transactionsCount)
            .debitTransactionsCount(this.debitTransactionsCount)
            .creditTransactionsCount(this.creditTransactionsCount)
            .currency(this.currency.toEntity())
            .fxRate(Objects.isNull(this.fxRate) ? null : this.fxRate.toEntity())
            .createdAt(this.createdAt)
            .updatedAt(this.updatedAt)
            .build();
    }

    static {

        Map<AccountDailySummary.SortingFields, String> entityFields = Map.ofEntries(

            Map.entry(AccountDailySummary.SortingFields.TRANSACTION_DATE, "transactionDate"),
            Map.entry(AccountDailySummary.SortingFields.ACCOUNT, "account"),
            Map.entry(AccountDailySummary.SortingFields.TOTAL_CREDIT_AMOUNT, "totalCreditAmount"),
            Map.entry(AccountDailySummary.SortingFields.TOTAL_DEBIT_AMOUNT, "totalDebitAmount"),
            Map.entry(AccountDailySummary.SortingFields.TOTAL_CREDIT_AMOUNT_USD, "totalCreditAmountUsd"),
            Map.entry(AccountDailySummary.SortingFields.TOTAL_DEBIT_AMOUNT_USD, "totalDebitAmountUsd"),
            Map.entry(AccountDailySummary.SortingFields.NET_AMOUNT, "netAmount"),
            Map.entry(AccountDailySummary.SortingFields.NET_AMOUNT_USD, "netAmountUsd"),
            Map.entry(AccountDailySummary.SortingFields.INITIAL_BALANCE, "initialBalance"),
            Map.entry(AccountDailySummary.SortingFields.FINAL_BALANCE, "finalBalance"),
            Map.entry(AccountDailySummary.SortingFields.INITIAL_BALANCE_USD, "initialBalanceUsd"),
            Map.entry(AccountDailySummary.SortingFields.FINAL_BALANCE_USD, "finalBalanceUsd"),
            Map.entry(AccountDailySummary.SortingFields.TRANSACTIONS_COUNT, "transactionsCount"),
            Map.entry(AccountDailySummary.SortingFields.DEBIT_TRANSACTIONS_COUNT, "debitTransactionsCount"),
            Map.entry(AccountDailySummary.SortingFields.CREDIT_TRANSACTIONS_COUNT, "creditTransactionsCount"),
            Map.entry(AccountDailySummary.SortingFields.CREATED_AT, "createdAt"),
            Map.entry(AccountDailySummary.SortingFields.UPDATED_AT, "updatedAt")
        );
        addEntityFieldMap(AccountDailySummary.SortingFields.class, entityFields);
    }


}
