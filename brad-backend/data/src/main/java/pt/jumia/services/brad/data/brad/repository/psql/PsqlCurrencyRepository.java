package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.CurrencyPsql;
import pt.jumia.services.brad.data.brad.entities.QCurrencyPsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaCurrencyRepository;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.filter.currency.CurrencyFilters;
import pt.jumia.services.brad.domain.repository.brad.CurrencyRepository;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlCurrencyRepository extends BaseRepository implements CurrencyRepository {

    private final QCurrencyPsql root = new QCurrencyPsql("root");
    private final JpaCurrencyRepository jpaCurrencyRepository;
    private final EntityManager entityManager;

    @Override
    @Transactional
    public Currency upsert(Currency currency) {
        return jpaCurrencyRepository.save(new CurrencyPsql(currency)).toEntity();
    }

    @Override
    public Optional<Currency> findById(long id) {
        return jpaCurrencyRepository.findById(id).map(CurrencyPsql::toEntity);
    }

    @Override
    @Transactional
    public Optional<Currency> findByCode(String code) {
        return jpaCurrencyRepository.findByCode(code).map(CurrencyPsql::toEntity);
    }

    @Override
    public List<Currency> findAll(CurrencyFilters currencyFilters) {
        JPAQuery<CurrencyPsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root);

        if (Objects.nonNull(currencyFilters)) {
            applyProjectionSelect(currencyFilters.getSelectedFields(), query, root, CurrencyPsql.class);
        }
        buildWhereClauses(currencyFilters, query);

        return query.distinct().fetch().stream()
                .map(CurrencyPsql::toEntity)
                .toList();
    }

    @Override
    public void deleteById(long id) {
        jpaCurrencyRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Currency> findByCurrencyCodes(List<String> currencyCodes) {
        if (currencyCodes == null || currencyCodes.isEmpty()) {
            return Map.of();
        }
        
        List<CurrencyPsql> currencies = jpaCurrencyRepository.findByCodeIn(currencyCodes);
        
        return currencies.stream()
                .map(CurrencyPsql::toEntity)
                .collect(Collectors.toMap(
                        Currency::getCode,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
    }

    private void buildWhereClauses(CurrencyFilters currencyFilters, JPAQuery<CurrencyPsql> query) {
        if (Objects.isNull(currencyFilters)) {
            return;
        }
        
        filterByFilterText(currencyFilters, query);
    }
    
    public void filterByFilterText(CurrencyFilters currencyFilters, JPAQuery<CurrencyPsql> query) {
        if (Objects.isNull(currencyFilters.getFilterText())) {
            return;
        }

        query.where(root.name.containsIgnoreCase(currencyFilters.getFilterText())
                .or(root.code.containsIgnoreCase(currencyFilters.getFilterText()))
                .or(root.symbol.containsIgnoreCase(currencyFilters.getFilterText())));
    }
}
