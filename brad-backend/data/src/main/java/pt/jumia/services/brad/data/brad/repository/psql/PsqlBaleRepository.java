package pt.jumia.services.brad.data.brad.repository.psql;

import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.data.brad.entities.BalePsql;
import pt.jumia.services.brad.data.brad.entities.QBalePsql;
import pt.jumia.services.brad.data.brad.entities.dto.BaleWithReconciliationDTO;
import pt.jumia.services.brad.data.brad.entities.fxrates.FxRatePsql;
import pt.jumia.services.brad.data.brad.entities.fxrates.bale.FxRateBalePsql;
import pt.jumia.services.brad.data.brad.entities.reconciliation.bale.QReconciliationBalePsql;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaBaleRepository;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaFxRateBaleRepository;
import pt.jumia.services.brad.data.brad.repository.psql.jpa.JpaFxRateRepository;
import pt.jumia.services.brad.data.brad.utils.PartitionCreator;
import pt.jumia.services.brad.data.shared.BaseRepository;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleFilters;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleGroupFilters;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleSortFilters;
import pt.jumia.services.brad.domain.entities.group.BaleGroup;
import pt.jumia.services.brad.domain.entities.group.GroupInfo;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;

import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
@Slf4j
public class PsqlBaleRepository extends BaseRepository implements BradBaleRepository {

    private final QBalePsql root = new QBalePsql("root");
    private final QReconciliationBalePsql reconciliationBale = new QReconciliationBalePsql("reconciliationBale");
    private final JpaBaleRepository jpaBaleRepository;
    private final EntityManager entityManager;
    private final PartitionCreator partitionCreator;

    private final JpaFxRateBaleRepository jpaFxRateBaleRepository;
    private final JpaFxRateRepository jpaFxRateRepository;

    @Override
    @Transactional
    public List<Bale> sync(List<Bale> baleList) throws DatabaseErrorsException {

        try {
            List<BalePsql> balePsqlList = jpaBaleRepository.saveAll(baleList.stream()
                            .map(BalePsql::new)
                            .collect(Collectors.toList()));

            Map<Bale, BalePsql> savedBalesMap = new HashMap<>();
            for (int i = 0; i < baleList.size(); i++) {
                savedBalesMap.put(balePsqlList.get(i).toEntity(), balePsqlList.get(i));
            }

            this.addFxRates(savedBalesMap);

            return balePsqlList.stream()
                    .map(BalePsql::toEntity)
                    .toList();
        } catch (Exception e) {
            log.error("Error while syncing bales. Exception {}", ExceptionUtils.getStackTrace(e));
            throw new DatabaseErrorsException(e.getMessage());
        }

    }

    private void addFxRates(Map<Bale, BalePsql> baleMap) {
        List<FxRateBalePsql> fxRateBalePsqlList = new ArrayList<>();
        for (int i = 0; i < baleMap.size(); i++) {
            Bale bale = baleMap.keySet().toArray(new Bale[0])[i];
            BalePsql balePsql = baleMap.get(bale);
            if (Objects.nonNull(bale.getFxRates())) {
                for (FxRate fxRate : bale.getFxRates()) {
                    FxRatePsql existingFxRate = jpaFxRateRepository.findById(fxRate.getId())
                            .orElseThrow(() -> new RuntimeException("FxRate not found while saving bale"));
                    FxRateBalePsql fxRateBalePsql = new FxRateBalePsql(
                            existingFxRate,
                            balePsql
                    );
                    fxRateBalePsqlList.add(fxRateBalePsql);
                }
            }
        }

        jpaFxRateBaleRepository.saveAll(fxRateBalePsqlList);

    }

    private Bale fetch(BaleWithReconciliationDTO baleWithReconciliationDTO) {
        Set<FxRate> fxRates = jpaFxRateBaleRepository.findByBaleId(baleWithReconciliationDTO.toEntity().getId()).stream()
                .map(FxRateBalePsql::getFxRate)
                .map(FxRatePsql::toEntity)
                .collect(Collectors.toSet());

        return baleWithReconciliationDTO.toEntity().toBuilder().fxRates(fxRates).build();
    }

    @Override
    @Transactional
    public void addFxRates(List<Bale> baleList) {
        Map<Bale, BalePsql> baleMap = new HashMap<>();

        baleList.forEach(bale -> {
            BalePsql balePsql = jpaBaleRepository.findById(bale.getId())
                    .orElseThrow(() -> new RuntimeException("Bale not found while saving fx rates"));
            baleMap.put(bale, balePsql);
        });

        this.addFxRates(baleMap);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Bale> findAll(BaleFilters baleFilters, BaleSortFilters baleSortFilters, PageFilters pageFilters)
            throws EntityErrorsException, ParseException {
        JPAQuery<BaleWithReconciliationDTO> query = new JPAQueryFactory(entityManager)
                .select(Projections.fields(BaleWithReconciliationDTO.class,
                        root.id.as("id"),
                        root.idCompany.as("idCompany"),
                        root.account.as("account"),
                        root.entryNo.as("entryNo"),
                        root.documentNo.as("documentNo"),
                        root.documentType.as("documentType"),
                        root.postingDate.as("postingDate"),
                        root.bankAccountPostingGroup.as("bankAccountPostingGroup"),
                        root.description.as("description"),
                        root.sourceCode.as("sourceCode"),
                        root.reasonCode.as("reasonCode"),
                        root.busLine.as("busLine"),
                        root.department.as("department"),
                        root.direction.as("direction"),
                        root.amount.as("amount"),
                        root.remainingAmount.as("remainingAmount"),
                        root.transactionCurrency.as("transactionCurrency"),
                        root.amountLcy.as("amountLcy"),
                        root.balanceAccountType.as("balanceAccountType"),
                        root.isOpen.as("isOpen"),
                        root.isReversed.as("isReversed"),
                        root.postedBy.as("postedBy"),
                        root.externalDocumentNo.as("externalDocumentNo"),
                        root.baleTimestamp.as("baleTimestamp"),
                        root.bankAccountTimestamp.as("bankAccountTimestamp"),
                        root.reconcileStatus.as("reconcileStatus"),
                        reconciliationBale.reconciliation.id.as("reconciliationId"),
                        reconciliationBale.reconciliation.creator.as("reconciliationCreator"),
                        reconciliationBale.reconciliation.creationDate.as("reconciliationCreationDate"),
                        reconciliationBale.reconciliation.reviewer.as("reconciliationReviewer"),
                        reconciliationBale.reconciliation.reviewDate.as("reconciliationReviewDate"),
                        reconciliationBale.reconciliation.status.as("reconciliationStatus")))
                .from(root)
                .leftJoin(reconciliationBale).on(reconciliationBale.balePsql.id.eq(root.id))
                .leftJoin(reconciliationBale.reconciliation).on(reconciliationBale.reconciliation.id
                        .eq(reconciliationBale.reconciliation.id));


        buildWhereClauses(baleFilters, query);
        applySort(baleSortFilters, query, root, Bale.SortingFields.class);
        applyPagination(pageFilters, query);

        return query.distinct().fetch().stream()
                .map(this::fetch)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public BaleGroup findBaleGroups(BaleFilters baleFilters, BaleGroupFilters baleGroupFilters) {

        List<String> groupingFields = baleGroupFilters.getFields().stream()
                .filter(Bale.GroupingFields::isBelongsToMainEntity).map(fields -> fields.groupField).toList();

        List<String> reconciliationGroupingFields = baleGroupFilters.getFields().stream()
                .filter(bale -> !bale.isBelongsToMainEntity()).map(fields -> fields.groupField).toList();

        JPAQuery<BaleWithReconciliationDTO> query = new JPAQueryFactory(entityManager)
                .select(Projections.fields(BaleWithReconciliationDTO.class,
                        root.id.as("id"),
                        root.idCompany.as("idCompany"),
                        root.account.as("account"),
                        root.entryNo.as("entryNo"),
                        root.documentNo.as("documentNo"),
                        root.documentType.as("documentType"),
                        root.postingDate.as("postingDate"),
                        root.bankAccountPostingGroup.as("bankAccountPostingGroup"),
                        root.description.as("description"),
                        root.sourceCode.as("sourceCode"),
                        root.reasonCode.as("reasonCode"),
                        root.busLine.as("busLine"),
                        root.department.as("department"),
                        root.direction.as("direction"),
                        root.amount.as("amount"),
                        root.remainingAmount.as("remainingAmount"),
                        root.transactionCurrency.as("transactionCurrency"),
                        root.amountLcy.as("amountLcy"),
                        root.balanceAccountType.as("balanceAccountType"),
                        root.isOpen.as("isOpen"),
                        root.isReversed.as("isReversed"),
                        root.postedBy.as("postedBy"),
                        root.externalDocumentNo.as("externalDocumentNo"),
                        root.baleTimestamp.as("baleTimestamp"),
                        root.bankAccountTimestamp.as("bankAccountTimestamp"),
                        root.reconcileStatus.as("reconcileStatus"),
                        reconciliationBale.reconciliation.id.as("reconciliationId"),
                        reconciliationBale.reconciliation.creator.as("reconciliationCreator"),
                        reconciliationBale.reconciliation.creationDate.as("reconciliationCreationDate"),
                        reconciliationBale.reconciliation.reviewer.as("reconciliationReviewer"),
                        reconciliationBale.reconciliation.reviewDate.as("reconciliationReviewDate"),
                        reconciliationBale.reconciliation.status.as("reconciliationStatus")))
                .from(root)
                .leftJoin(reconciliationBale).on(reconciliationBale.balePsql.id.eq(root.id))
                .leftJoin(reconciliationBale.reconciliation).on(reconciliationBale.reconciliation.id
                        .eq(reconciliationBale.reconciliation.id));

        applyGroup(groupingFields, query, root);
        if (!reconciliationGroupingFields.isEmpty()) {
            applyGroup(reconciliationGroupingFields, query, reconciliationBale.reconciliation);
        }
        buildWhereClauses(baleFilters, query);

        String queryStr = query.fetch().toString();

        return new BaleGroup(queryStr, baleGroupFilters.getFields());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Long> findAllIds(BaleFilters baleFilters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id)
                .from(root);

        buildWhereClauses(baleFilters, query);

        return query.distinct().fetch();
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getTotalAmountOfBales(Long bankAccountID, List<Long> baleIds) {
        int batchSize = 2000;
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (int start = 0; start < baleIds.size(); start += batchSize) {
            int end = Math.min(baleIds.size(), start + batchSize);
            List<Long> chunk = new ArrayList<>(baleIds.subList(start, end));

            BigDecimal chunkTotal = jpaBaleRepository.getTotalAmountOfBales(bankAccountID, chunk);

            if (chunkTotal != null) {
                totalAmount = totalAmount.add(chunkTotal);
            }
        }
        return totalAmount;
    }

    @Override
    public Optional<Bale> findById(Long id) {
        return jpaBaleRepository.findById(id)
                .map(BalePsql::toEntity);
    }


    @Override
    @Transactional(readOnly = true)
    public Optional<Bale> findLastBaleInBradOfCompanyId(String idCompany) {
        return jpaBaleRepository.findFirstByIdCompanyOrderByBaleTimestampDesc(idCompany)
                .map(BalePsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public GroupInfo fetchGroupInfo(BaleFilters baleFilters) {
        JPAQuery<GroupInfo> query = new JPAQueryFactory(entityManager)
                .select(Projections.bean(GroupInfo.class,
                        root.amount.sum().as("totalBalance"),
                        root.id.count().as("totalQuantity"),
                        root.reconcileStatus.countDistinct().as("statusAmount"),
                        root.reconcileStatus.min().as("status")
                ))
                .from(root);

        buildWhereClauses(baleFilters, query);

        GroupInfo groupInfo = query.fetchOne();

        if (Objects.isNull(groupInfo)) {
            return GroupInfo.builder().build();
        }

        groupInfo = groupInfo.toBuilder()
                .allIds(findAllIds(baleFilters))
                .build();

        //"Avoid using Literals in Conditional Statements" - PMD... thanks pmd
        int maxSizeForSharedStatus = 1;

        if (groupInfo.getStatusAmount() == maxSizeForSharedStatus) {
            groupInfo.setIsStatusShared(true);
            groupInfo.setIsReconciliationShared(
                    groupInfo.getStatus().equals(ReconcileStatus.NOT_RECONCILED.name()) ||
                            isReconciliationSharedByBales(baleFilters)
            );
        } else {
            groupInfo.setIsStatusShared(false);
            groupInfo.setIsReconciliationShared(false);
        }

        return groupInfo;
    }


    @Override
    public Boolean isReconciliationSharedByBales(BaleFilters baleFilters) {
        JPAQuery<Integer> query = new JPAQueryFactory(entityManager)
                .select(reconciliationBale.reconciliation.id)
                .from(root)
                .leftJoin(reconciliationBale).on(reconciliationBale.balePsql.id.eq(root.id))
                .distinct();

        buildWhereClauses(baleFilters, query);

        return query.fetch().size() <= 1;
    }

    @Override
    public long count(BaleFilters filters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(filters, query);
        return Math.toIntExact(Objects.requireNonNull(query.fetchOne()));
    }

    @Override
    public void createPartition(String idCompany) throws DatabaseErrorsException {
        partitionCreator.createListPartition(idCompany, PartitionCreator.TableNames.BALE);
        partitionCreator.createListPartition(idCompany, PartitionCreator.TableNames.RECONCILIATIONS);
    }


    @Override
    public void deleteById(Long id) {
        jpaBaleRepository.deleteById(id);
    }

    @Override
    public boolean areBalesAvailableToReconcile(List<Long> baleEntryNoList) {
        //"Avoid using Literals in Conditional Statements" - PMD... thanks pmd
        int maxSizeToReconcile = 0;

        return jpaBaleRepository.areBalesAvailable(baleEntryNoList).compareTo(baleEntryNoList.size()) == maxSizeToReconcile;
    }

    @Transactional
    @Override
    public void deleteAllFxRatesBales() {
        jpaFxRateBaleRepository.deleteAll();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Bale> findBalesWithoutFxRate(FxRate fxRate) {
        Currency quoteCurrency = fxRate.getQuoteCurrency();

        if (quoteCurrency.getCode().equals("USD")) {
            return findBaleWithoutFxRateUsd(fxRate);
        }
        return findBaleWithoutFxRateLcy(fxRate);
    }

    private List<Bale> findBaleWithoutFxRateUsd(FxRate fxRate) {
        LocalDate rateDate = fxRate.getRateDate();
        Long baseCurrencyId = fxRate.getBaseCurrency().getId();

        String sql = "SELECT b.* " +
                "FROM bale b " +
                "LEFT JOIN fx_rates_bale frb ON b.id = frb.bale_id " +
                "LEFT JOIN fx_rates fxr ON frb.fx_rates_id = fxr.id " +
                "LEFT JOIN currency c ON fxr.\"Quote_Currency\" = c.id " +
                "WHERE b.\"Posting Date\" = :rateDate AND " +
                "      b.\"Currency\" = :baseCurrencyId AND (" +
                "    c.code != 'USD' OR " +
                "    c.code IS NULL)";

        Query query = entityManager.createNativeQuery(sql, BalePsql.class);
        query.setParameter("rateDate", rateDate);
        query.setParameter("baseCurrencyId", baseCurrencyId);

        List<BalePsql> balePsqlList = query.getResultList();
        return balePsqlList.stream()
                .map(BalePsql::toEntity)
                .collect(Collectors.toList());
    }

    private List<Bale> findBaleWithoutFxRateLcy(FxRate fxRate) {
        LocalDate rateDate = fxRate.getRateDate();
        Long baseCurrencyId = fxRate.getBaseCurrency().getId();
        String sql = "SELECT b.* " +
                "FROM bale b " +
                "LEFT JOIN fx_rates_bale frb ON b.id = frb.bale_id " +
                "LEFT JOIN fx_rates fxr ON frb.fx_rates_id = fxr.id " +
                "LEFT JOIN currency c ON fxr.\"Quote_Currency\" = c.id " +
                "LEFT JOIN bank_account ba ON b.\"BankAccount\" = ba.id " +
                "LEFT JOIN country co ON ba.fk_country = co.id " +
                "LEFT JOIN currency local_currency ON co.currency_id = local_currency.id " +
                "WHERE b.\"Posting Date\" = :rateDate AND " +
                "      b.\"Currency\" = :baseCurrencyId AND (" +
                "    c.code != local_currency.code OR " +
                "    c.code IS NULL)";

        Query query = entityManager.createNativeQuery(sql, BalePsql.class);
        query.setParameter("rateDate", rateDate);
        query.setParameter("baseCurrencyId", baseCurrencyId);

        List<BalePsql> balePsqlList = query.getResultList();
        return balePsqlList.stream()
                .map(BalePsql::toEntity)
                .collect(Collectors.toList());
    }

    public void buildWhereClauses(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.isNull(baleFilters)) {
            return;
        }

        filterByFilterText(baleFilters, query);
        filterByIdCompany(baleFilters, query);
        filterByBankAccount(baleFilters, query);
        filterByEntryNo(baleFilters, query);
        filterByDocumentNo(baleFilters, query);
        filterByDocumentType(baleFilters, query);
        filterByPostingDate(baleFilters, query);
        filterByBankAccountPostingGroup(baleFilters, query);
        filterByDescription(baleFilters, query);
        filterBySourceCode(baleFilters, query);
        filterByReasonCode(baleFilters, query);
        filterByBusLine(baleFilters, query);
        filterByDepartment(baleFilters, query);
        filterByDirection(baleFilters, query);
        filterByAmount(baleFilters, query);
        filterByRemainingAmount(baleFilters, query);
        filterByTransactionCurrency(baleFilters, query);
        filterByAmountLcy(baleFilters, query);
        filterByBalanceAccountType(baleFilters, query);
        filterByIsOpen(baleFilters, query);
        filterByIsReversed(baleFilters, query);
        filterByPostedBy(baleFilters, query);
        filterByExternalDocumentNo(baleFilters, query);
        filterByBaleTimestamp(baleFilters, query);
        filterByBankAccountTimestamp(baleFilters, query);

        filterByReconciliationId(baleFilters, query);
        filterByReconciliationCreator(baleFilters, query);
        filterByReconciliationCreationDate(baleFilters, query);
        filterByReconciliationReviewer(baleFilters, query);
        filterByReconciliationReviewDate(baleFilters, query);
        filterByReconciliationStatus(baleFilters, query);

    }

    private void filterByFilterText(BaleFilters baleFilters, JPAQuery<?> query) {
        if (!Objects.isNull(baleFilters.getFilterText())) {
            query.where(root.documentNo.likeIgnoreCase("%" + baleFilters.getFilterText() + "%")
                    .or(root.externalDocumentNo.likeIgnoreCase("%" + baleFilters.getFilterText() + "%"))
                    .or(root.description.likeIgnoreCase("%" + baleFilters.getFilterText() + "%")));
        }
    }

    private void filterByIdCompany(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getIdCompany())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.idCompany.likeIgnoreCase("%" + baleFilters.getIdCompany() + "%"));
            } else {
                query.where(root.idCompany.eq(baleFilters.getIdCompany()));
            }
        }
    }

    private void filterByBankAccount(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getAccountNumber())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.account.navReference.likeIgnoreCase("%" + baleFilters.getAccountNumber() + "%"));
            } else {
                query.where(root.account.navReference.eq(baleFilters.getAccountNumber()));
            }
        }
    }

    private void filterByEntryNo(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getEntryNo())) {
            query.where(root.entryNo.in(baleFilters.getEntryNo()));
        }
    }

    private void filterByDocumentNo(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getDocumentNo())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.documentNo.likeIgnoreCase("%" + baleFilters.getDocumentNo() + "%"));
            } else {
                query.where(root.documentNo.eq(baleFilters.getDocumentNo()));
            }
        }
    }

    private void filterByDocumentType(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getDocumentType())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.documentType.likeIgnoreCase("%" + baleFilters.getDocumentType() + "%"));
            } else {
                query.where(root.documentType.eq(baleFilters.getDocumentType()));
            }
        }
    }

    private void filterByPostingDate(BaleFilters baleFilters, JPAQuery<?> query) {
        if (!Objects.isNull(baleFilters.getPostingDateStart())) {
            LocalDate startDate = baleFilters.getPostingDateStart();
            LocalDate endDate = Objects.isNull(baleFilters.getPostingDateEnd()) ? startDate : baleFilters.getPostingDateEnd();
            query.where(root.postingDate.between(startDate, endDate));
        }
    }

    private void filterByBankAccountPostingGroup(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getAccountPostingGroup())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.bankAccountPostingGroup.likeIgnoreCase("%" + baleFilters.getAccountPostingGroup() + "%"));
            } else {
                query.where(root.bankAccountPostingGroup.eq(baleFilters.getAccountPostingGroup()));
            }
        }
    }

    private void filterByDescription(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getDescription())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.description.likeIgnoreCase("%" + baleFilters.getDescription() + "%"));
            } else {
                query.where(root.description.eq(baleFilters.getDescription()));
            }
        }
    }

    private void filterBySourceCode(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getSourceCode())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.sourceCode.likeIgnoreCase("%" + baleFilters.getSourceCode() + "%"));
            } else {
                query.where(root.sourceCode.eq(baleFilters.getSourceCode()));
            }
        }
    }

    private void filterByReasonCode(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getReasonCode())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.reasonCode.likeIgnoreCase("%" + baleFilters.getReasonCode() + "%"));
            } else {
                query.where(root.reasonCode.eq(baleFilters.getReasonCode()));
            }
        }
    }

    private void filterByBusLine(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getBusLine())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.busLine.likeIgnoreCase("%" + baleFilters.getBusLine() + "%"));
            } else {
                query.where(root.busLine.eq(baleFilters.getBusLine()));
            }
        }
    }

    private void filterByDepartment(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getDepartment())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.department.likeIgnoreCase("%" + baleFilters.getDepartment() + "%"));
            } else {
                query.where(root.department.eq(baleFilters.getDepartment()));
            }
        }
    }

    private void filterByDirection(BaleFilters baleFilters, JPAQuery<?> query) {
        if (!Objects.isNull(baleFilters.getDirection())) {
            List<Integer> directions = baleFilters.getDirection().stream()
                    .map(Direction::valueOf)
                    .map(Direction::getValue)
                    .collect(Collectors.toList());
            query.where(root.direction.in(directions));
        }
    }

    private void filterByAmount(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getAmount())) {
            query.where(root.amount.eq(baleFilters.getAmount()));
        }
    }

    private void filterByRemainingAmount(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getRemainingAmount())) {
            query.where(root.remainingAmount.eq(baleFilters.getRemainingAmount()));
        }
    }

    private void filterByTransactionCurrency(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getTransactionCurrency())) {
            query.where(root.transactionCurrency.code.in(baleFilters.getTransactionCurrency()));
        }
    }

    private void filterByAmountLcy(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getAmountLcy())) {
            query.where(root.amountLcy.eq(baleFilters.getAmountLcy()));
        }
    }

    private void filterByBalanceAccountType(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getBalanceAccountType())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.balanceAccountType.likeIgnoreCase("%" + baleFilters.getBalanceAccountType() + "%"));
            } else {
                query.where(root.balanceAccountType.eq(baleFilters.getBalanceAccountType()));
            }
        }
    }

    private void filterByIsOpen(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getIsOpen())) {
            query.where(root.isOpen.eq(baleFilters.getIsOpen()));
        }
    }

    private void filterByIsReversed(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getIsReversed())) {
            query.where(root.isReversed.eq(baleFilters.getIsReversed()));
        }
    }

    private void filterByPostedBy(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getPostedBy())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.postedBy.likeIgnoreCase("%" + baleFilters.getPostedBy() + "%"));
            } else {
                query.where(root.postedBy.eq(baleFilters.getPostedBy()));
            }
        }
    }

    private void filterByExternalDocumentNo(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getExternalDocumentNo())) {
            if (!baleFilters.isExactFilters()) {
                query.where(root.externalDocumentNo.likeIgnoreCase("%" + baleFilters.getExternalDocumentNo() + "%"));
            } else {
                query.where(root.externalDocumentNo.eq(baleFilters.getExternalDocumentNo()));
            }
        }
    }

    private void filterByBaleTimestamp(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getBaleTimestamp())) {
            query.where(root.baleTimestamp.eq(baleFilters.getBaleTimestamp()));
        }
    }

    private void filterByBankAccountTimestamp(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getAccountTimestamp())) {
            query.where(root.bankAccountTimestamp.eq(baleFilters.getAccountTimestamp()));
        }
    }

    private void filterByReconciliationId(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getReconciliationId())) {
            query.leftJoin(reconciliationBale).on(reconciliationBale.balePsql.id.eq(root.id));
            query.where(reconciliationBale.reconciliation.id.eq(baleFilters.getReconciliationId()));
        }
    }

    private void filterByReconciliationCreator(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getReconciliationCreator())) {
            query.leftJoin(reconciliationBale).on(reconciliationBale.balePsql.id.eq(root.id))
                    .leftJoin(reconciliationBale.reconciliation).on(reconciliationBale.reconciliation.id.eq(reconciliationBale.reconciliation.id));

            if (!baleFilters.isExactFilters()) {
                query.where(reconciliationBale.reconciliation.creator.likeIgnoreCase("%" + baleFilters.getReconciliationCreator() + "%"));
            } else {
                query.where(reconciliationBale.reconciliation.creator.eq(baleFilters.getReconciliationCreator()));
            }
        }
    }


    private void filterByReconciliationCreationDate(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getReconciliationCreationDateStart())) {
            LocalDateTime startDate = baleFilters.getReconciliationCreationDateStart().atStartOfDay();
            LocalDateTime endDate = Objects.isNull(baleFilters.getReconciliationCreationDateEnd())
                    ? startDate.with(LocalTime.MAX)
                    : baleFilters.getReconciliationCreationDateEnd().atTime(23, 59, 59);

            query.leftJoin(reconciliationBale).on(reconciliationBale.balePsql.id.eq(root.id))
                    .leftJoin(reconciliationBale.reconciliation).on(reconciliationBale.reconciliation.id.eq(reconciliationBale.reconciliation.id));

            query.where(reconciliationBale.reconciliation.creationDate.between(startDate, endDate));
        }
    }


    private void filterByReconciliationReviewer(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getReconciliationReviewer())) {
            query.leftJoin(reconciliationBale).on(reconciliationBale.balePsql.id.eq(root.id))
                    .leftJoin(reconciliationBale.reconciliation).on(reconciliationBale.reconciliation.id.eq(reconciliationBale.reconciliation.id));

            if (!baleFilters.isExactFilters()) {
                query.where(reconciliationBale.reconciliation.reviewer.likeIgnoreCase("%" + baleFilters.getReconciliationReviewer() + "%"));
            } else {
                query.where(reconciliationBale.reconciliation.reviewer.eq(baleFilters.getReconciliationReviewer()));
            }
        }
    }


    private void filterByReconciliationReviewDate(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getReconciliationReviewDateStart())) {
            LocalDateTime startDate = baleFilters.getReconciliationReviewDateStart().atStartOfDay();
            LocalDateTime endDate = Objects.isNull(baleFilters.getReconciliationReviewDateEnd())
                    ? startDate.with(LocalTime.MAX)
                    : baleFilters.getReconciliationReviewDateEnd().atTime(23, 59, 59);

            query.leftJoin(reconciliationBale).on(reconciliationBale.balePsql.id.eq(root.id))
                    .leftJoin(reconciliationBale.reconciliation).on(reconciliationBale.reconciliation.id.eq(reconciliationBale.reconciliation.id));

            query.where(reconciliationBale.reconciliation.reviewDate.between(startDate, endDate));
        }
    }


    private void filterByReconciliationStatus(BaleFilters baleFilters, JPAQuery<?> query) {
        if (Objects.nonNull(baleFilters.getReconciliationStatus())) {
            query.leftJoin(reconciliationBale).on(reconciliationBale.balePsql.id.eq(root.id))
                    .leftJoin(reconciliationBale.reconciliation).on(reconciliationBale.reconciliation.id.eq(reconciliationBale.reconciliation.id));

            query.where(reconciliationBale.reconciliation.status.in(baleFilters.getReconciliationStatus()));
        }
    }
}
