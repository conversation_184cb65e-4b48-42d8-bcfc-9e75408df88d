package pt.jumia.services.brad.data.shared;

import com.zaxxer.hikari.HikariDataSource;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import pt.jumia.services.brad.domain.entities.ViewEntity;

import java.util.Map;

public class Datasource extends AbstractRoutingDataSource {
    private final Map<String, HikariDataSource> tenants;


    @SuppressFBWarnings("EI_EXPOSE_REP2")
    public Datasource(Map<String, HikariDataSource> tenants) {
        this.tenants = tenants;
        initialize(this.tenants);
    }

    private void initialize(Map<String, HikariDataSource> tenants) {
        this.setTargetDataSources((Map) tenants);
        this.afterPropertiesSet();
    }

    public void update(Map<String, HikariDataSource> tenants) {
        clear();
        this.tenants.putAll(tenants);
        initialize(tenants);
    }

    private void clear() {
        for (Map.Entry<String, HikariDataSource> tenant: this.tenants.entrySet()) {
            tenant.getValue().close();
        }
        this.tenants.clear();
    }

    @Override
    protected Object determineCurrentLookupKey() {
        return getLookupKey(ViewContext.getCurrentContext());
    }

    public static String getLookupKey(ViewEntity viewEntity) {
        if (viewEntity == null) {
            // Return a default lookup key when ViewEntity is null (e.g., during Spring Boot initialization)
            return "default";
        }
        return String.format("%s_%s", viewEntity.getDatabaseName(), viewEntity.getViewName());
    }

}
