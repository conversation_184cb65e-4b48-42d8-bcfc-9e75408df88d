package pt.jumia.services.brad.data.brad.entities.audit;


import jakarta.persistence.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.envers.RevisionType;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Account.Type;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.enumerations.StatementSource;

import java.time.LocalDateTime;


@Entity
@NoArgsConstructor
@Getter
@Table(name = "bank_account_aud", schema = "audit")
public class AccountAudPsql {

    @EmbeddedId
    private DefaultAudIdPsql id;

    @Enumerated(EnumType.ORDINAL)
    @Column(name = "revtype")
    private RevisionType revType;

    @Column(name = "company_id")
    private String companyID;

    @Column(name = "fk_country")
    private String country;

    @Column(name = "partner")
    private String partner;

    @Column(name = "nav_reference")
    private String navReference;

    @Column(name = "beneficiary_name")
    private String beneficiaryName;

    @Column(name = "beneficiary_address")
    private String beneficiaryAddress;

    @Column(name = "iban")
    private String iban;

    @Column(name = "bank_account_number")
    private String accountNumber;

    @Column(name = "bank_name")
    private String accountName;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "swift_code")
    private String swiftCode;

    @Column(name = "bank_routing_code")
    private String bankRoutingCode;

    @Column(name = "sort_code")
    private String sortCode;

    @Column(name = "branch_code")
    private String branchCode;

    @Column(name = "rib")
    private String rib;

    @Column(name = "type")
    private String type;

    @Column(name = "sub_type")
    private String subType;

    @Column(name = "status")
    private String status;

    @Column(name = "currency_id")
    private String currency;

    @Column(name = "statement_source")
    private String statementSource;

    @Column(name = "statement_periodicity")
    private String statementPeriodicity;

    @Column(name = "last_processed_statement_date")
    private LocalDate lastProcessedStatementDate;

    @Column(name = "isin")
    private String isin;

    @Column(name = "contract_id")
    private String contractId;

    @Column(name = "amount_deposited")
    private BigDecimal amountDeposited;

    @Column(name = "maturity_date")
    private LocalDate maturityDate;

    @Column(name = "nominal_amount")
    private BigDecimal nominalAmount;

    @Column(name = "coupon_payment_periodicity")
    private String couponPaymentPeriodicity;

    @Column(name = "coupon_rate")
    private BigDecimal couponRate;

    @Column(name = "interest")
    private BigDecimal interest;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    public Account toEntity() {
        return Account
                .builder()
                .id(id.getId())
                .companyID(companyID)
                .country(Country.builder().id(Long.parseLong(country)).build())
                .partner(partner)
                .navReference(navReference)
                .beneficiaryName(beneficiaryName)
                .beneficiaryAddress(beneficiaryAddress)
                .iban(iban)
                .accountNumber(accountNumber)
                .accountName(accountName)
                .phoneNumber(phoneNumber)
                .swiftCode(swiftCode)
                .bankRoutingCode(bankRoutingCode)
                .sortCode(sortCode)
                .branchCode(branchCode)
                .rib(rib)
                .currency(Currency.builder().id(Long.parseLong(currency)).build())
                .type(Type.valueOf(type))
                .subType(subType != null ? Account.SubType.valueOf(subType) : null)
                .status(Account.getStatus(status))
                .statementSource(StatementSource.valueOf(statementSource))
                .statementPeriodicity(Account.StatementPeriodicity.valueOf(statementPeriodicity))
                .lastProcessedStatementDate(lastProcessedStatementDate)
                .isin(isin)
                .contractId(contractId)
                .amountDeposited(amountDeposited)
                .maturityDate(maturityDate)
                .nominalAmount(nominalAmount)
                .couponPaymentPeriodicity(couponPaymentPeriodicity != null ?
                        Account.CouponPaymentPeriodicity.valueOf(couponPaymentPeriodicity) : null)
                .couponRate(couponRate)
                .interest(interest)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}
