package pt.jumia.services.brad.data.brad.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import pt.jumia.services.brad.data.brad.entities.CurrencyPsql;

import java.util.List;
import java.util.Optional;

public interface JpaCurrencyRepository extends JpaRepository<CurrencyPsql, Long>, JpaSpecificationExecutor<CurrencyPsql> {

    Optional<CurrencyPsql> findByCode(String code);

    List<CurrencyPsql> findByCodeIn(List<String> currencyCodes);
}
