package pt.jumia.services.brad.data.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.NonTransientResourceException;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.executionlogs.CreateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.Iterator;
import java.util.List;
import java.util.Optional;

/**
 * Spring Batch ItemReader for Bale entities.
 * 
 * This component replaces the complex custom reading logic with a simple,
 * Spring Batch-compliant reader that handles one ViewEntity at a time.
 * 
 * Key simplifications:
 * - No complex memory monitoring or batch size calculations
 * - No custom error handling (delegated to Spring Batch)
 * - Clear separation of concerns
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BaleItemReader implements ItemReader<Bale> {

    private final BaleRepository baleRepository;
    private final BradBaleRepository bradBaleRepository;
    private final ReadViewEntityUseCase readViewEntityUseCase;
    private final CreateExecutionLogsUseCase createExecutionLogsUseCase;

    private Iterator<ViewEntity> viewEntityIterator;
    private Iterator<Bale> currentBaleIterator;
    private ExecutionLog currentExecutionLog;
    private boolean initialized = false;

    @Override
    public Bale read() throws Exception, UnexpectedInputException, ParseException, NonTransientResourceException {
        
        if (!initialized) {
            initialize();
        }

        // If we have bales from current view entity, return the next one
        if (currentBaleIterator != null && currentBaleIterator.hasNext()) {
            return currentBaleIterator.next();
        }

        // Move to next view entity
        if (viewEntityIterator.hasNext()) {
            ViewEntity nextViewEntity = viewEntityIterator.next();
            loadBalesForViewEntity(nextViewEntity);
            
            // Return first bale from new view entity (if any)
            if (currentBaleIterator != null && currentBaleIterator.hasNext()) {
                return currentBaleIterator.next();
            }
        }

        // No more data
        return null;
    }

    private void initialize() throws Exception {
        log.info("Initializing Bale ItemReader");
        
        List<ViewEntity> viewEntities = readViewEntityUseCase.execute(ViewEntity.EntityType.BALE);
        if (viewEntities.isEmpty()) {
            log.info("No bale view entities found");
            viewEntityIterator = List.<ViewEntity>of().iterator();
        } else {
            log.info("Found {} bale view entities to process", viewEntities.size());
            viewEntityIterator = viewEntities.iterator();
        }
        
        initialized = true;
    }

    private void loadBalesForViewEntity(ViewEntity viewEntity) {
        try {
            log.info("Loading bales for view entity: {}", viewEntity.getViewName());
            
            // Create execution log for this view entity
            ExecutionLog executionLog = ExecutionLog.builder()
                    .logType(ExecutionLog.ExecutionLogType.BALE)
                    .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
                    .build();
            currentExecutionLog = createExecutionLogsUseCase.execute(executionLog);
            
            // Get company ID and create partition
            String companyId = baleRepository.fetchCompanyId(viewEntity, false);
            if (companyId != null) {
                bradBaleRepository.createPartition(companyId);
            }
            
            // Find starting entry number
            Optional<Bale> lastBale = bradBaleRepository.findLastBaleInBradOfCompanyId(companyId);
            Integer entryNo = lastBale.map(Bale::getEntryNo).orElse(null);
            
            log.info("Fetching bales for view entity: {} starting from entry: {}", 
                    viewEntity.getViewName(), entryNo);
            
            // Fetch bales using existing repository method
            List<Bale> bales = baleRepository.findAll(entryNo, viewEntity, false, currentExecutionLog);
            
            if (bales.isEmpty()) {
                log.info("No bales found for view entity: {}", viewEntity.getViewName());
                currentBaleIterator = null;
            } else {
                log.info("Loaded {} bales for view entity: {}", bales.size(), viewEntity.getViewName());
                currentBaleIterator = bales.iterator();
            }
            
        } catch (Exception e) {
            log.error("Error loading bales for view entity: {}", viewEntity.getViewName(), e);
            currentBaleIterator = null;
            throw new RuntimeException("Failed to load bales for view entity: " + viewEntity.getViewName(), e);
        }
    }
}
