# brad-backend

This project contains the base for a brad default backend project.

It is spring Boot application structured to follow the [clean architecture](https://8thlight.com/blog/uncle-bob/2012/08/13/the-clean-architecture.html). This means that it is divided in several modules:

### OpenJDK

This project uses `java-17`.

The recommended way to install and switch between JDK versions is to use *SDKMAN*

*SDKMAN* is available at https://sdkman.io

To install ojava17
```sh
sdk install java 17.0.6-tem (check the correct version name with sdk list java)
```
To list installed and current in use JDK versions
```sh
sdk list java
```
To switch JDK versions
```sh
sdk use java <version>
```

Installed *SDKMAN* JDK's live under *$SDKMAN_DIR/candidates/java*. Remember to set these in your choice IDE.

### PROJECT

#### SETUP

To first setup the application to run locally, we need to create a `src/main/resources/application-secrets.yml` from the `src/main/resources/application-secrets.yml.template` file and update it with the appropriate secrets.
The application should be run in with the profile `secrets` (on intellij it can be configured on `Edit Configurations -> Active profiles`).

On a docker environment, these values can be overridden through environment variables as specified in [spring boot's documentation](https://docs.spring.io/spring-boot/docs/2.1.6.RELEASE/reference/html/boot-features-external-config.html#boot-features-external-config-application-property-files).

#### RUN


We use gradle as our build tool.
To run the app you only need to execute:

<code>./gradlew</code> or <code>./gradlew bootRun</code>


To run our tests:

<code>./gradlew test</code>

If you want to run tests only for a specific module:

<code>./gradlew data:test</code>

## PROJECT STRUCTURE

#### Domain
Contains the business logic of the application, mainly entities and use cases.
It is also here that we create interfaces that will be implemented by other modules (data and network).

Should be very well tested (close to 100% code coverage) and all dependencies should be mocked. 
#### Network
Contains the code to communicate with other systems (http, rabbit, etc).

In here we should test the contracts and test real calls to those systems.
#### Data
Represents the persistence layer, which main job is to implement the repository defined in the **domain**.

We are using flyway to run the migrations, and we aim at using a database running in a docker for testing environments.

Tests should use the db in the docker and run all migrations and then test all the CRUD operations and more complex queries for all our managed entities.
#### Api
This is our current delivery mechanism (our interface with the outside world). It basically contains all our controllers and payload definitions.

In here tests should mainly focus on payload validations, by using mockMVC.
#### src
This basically only includes the Application class....

As for tests, it will include all the functional tests. This means tests end to end (using memDB) for all endpoints and then include some nice happy trails that simulate the main workflows of the app.
