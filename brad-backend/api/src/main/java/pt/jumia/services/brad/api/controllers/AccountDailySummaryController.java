package pt.jumia.services.brad.api.controllers;

import com.neovisionaries.i18n.CountryCode;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.brad.api.payloads.request.accountdailysummary.RecalculateApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.GroupedAccountSummaryResponsePayload;
import pt.jumia.services.brad.api.payloads.response.StackedCashPositionApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashEvolutionFilters;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashPositionFilters;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CommonFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.accountdailysummary.CalculateAccountSummaryUseCase;
import pt.jumia.services.brad.domain.usecases.accountdailysummary.ReadAccountDailySummaryUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/accounts-summary")
public class AccountDailySummaryController {

    private final CalculateAccountSummaryUseCase calculateAccountSummaryUseCase;
    private final ReadAccountDailySummaryUseCase readAccountDailySummaryUseCase;
    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final ReadCountriesUseCase readCountriesUseCase;

    @PatchMapping(value = "/recalculate")
    @ResponseStatus(HttpStatus.ACCEPTED)
    public void update(@RequestBody(required = false) RecalculateApiRequestPayload payload) throws EntityErrorsException {

        log.info("Recalculating accounts daily summaries - Triggered by user with identifier {}", RequestContext.getUsername());

        if (Objects.isNull(payload)) {
            calculateAccountSummaryUseCase.execute(null, null);
        } else {
            calculateAccountSummaryUseCase.execute(payload.getAccountNavReference(), payload.getStartDate());
        }
    }

    @GetMapping(value = "/cash-evolution")
    public List<GroupedAccountSummaryResponsePayload> getCashEvolution(CashEvolutionFilters cashEvolutionFilters)
        throws UserForbiddenException, EntityErrorsException {

        log.info("Getting cash evolution by user with identifier {}", RequestContext.getUsername());

        List<CountryCode> countriesWithCanViewPermission = validateUserAccessUseCase.getCountriesCanViewAccountsOrThrow(
            RequestContext.getUser());

        List<Long> filteredCountries = filterCountryPermissions(cashEvolutionFilters, countriesWithCanViewPermission);

        if (CollectionUtils.isEmpty(filteredCountries)) {
            throw new UserForbiddenException("Access denied!");
        }
        cashEvolutionFilters.setCountries(filteredCountries);

        return readAccountDailySummaryUseCase.executeCashEvolution(cashEvolutionFilters).stream()
            .map(GroupedAccountSummaryResponsePayload::new)
            .collect(Collectors.toList());

    }

    @GetMapping(value = "/cash-position")
    public List<GroupedAccountSummaryResponsePayload> getCashPosition(CashPositionFilters cashPositionFilters)
            throws UserForbiddenException, EntityErrorsException {

        log.info("Getting cash position by user with identifier {}", RequestContext.getUsername());

        List<CountryCode> countriesWithCanViewPermission = validateUserAccessUseCase.getCountriesCanViewAccountsOrThrow(
                RequestContext.getUser());

        List<Long> filteredCountries = filterCountryPermissions(cashPositionFilters, countriesWithCanViewPermission);

        if (CollectionUtils.isEmpty(filteredCountries)) {
            throw new UserForbiddenException("Access denied!");
        }

        cashPositionFilters.setCountries(filteredCountries);

        return readAccountDailySummaryUseCase.executeCashPosition(cashPositionFilters).stream()
                .map(GroupedAccountSummaryResponsePayload::new)
                .collect(Collectors.toList());

    }

    @GetMapping(value = "/cash-position-stacked")
    public List<StackedCashPositionApiResponsePayload> getCashPositionStacked(CashPositionFilters cashPositionFilters)
        throws UserForbiddenException, EntityErrorsException {

        log.info("Getting stacked cash position by user with identifier {}", RequestContext.getUsername());

        List<CountryCode> countriesWithCanViewPermission = validateUserAccessUseCase.getCountriesCanViewAccountsOrThrow(
            RequestContext.getUser());

        List<Long> filteredCountries = filterCountryPermissions(cashPositionFilters, countriesWithCanViewPermission);

        if (CollectionUtils.isEmpty(filteredCountries)) {
            throw new UserForbiddenException("Access denied!");
        }

        cashPositionFilters.setCountries(filteredCountries);

        return readAccountDailySummaryUseCase.executeCashPositionStacked(cashPositionFilters).stream()
            .map(StackedCashPositionApiResponsePayload::new)
            .collect(Collectors.toList());

    }

    private List<Long> filterCountryPermissions(CommonFilters filters, List<CountryCode> countriesWithCanViewPermission)
        throws EntityErrorsException, UserForbiddenException {

        final List<Country> countries = readCountriesUseCase.executeCountries();
        List<Country> countriesFromFilters = null;
        if (filters != null && !CollectionUtils.isEmpty(filters.getCountries())) {
            countriesFromFilters = countries.stream().filter(countryInBrad
                    -> filters.getCountries().stream().anyMatch(code -> code.equals(countryInBrad.getId())))
                .collect(Collectors.toList());
        }
        return getAccountFiltersAccordingToCountryPermissions(
            countriesWithCanViewPermission, countriesFromFilters, countries);

    }

    private List<Long> getAccountFiltersAccordingToCountryPermissions
        (List<CountryCode> countriesWithCanViewPermission, List<Country> countriesFromFilters, List<Country> allCountriesInBrad)
        throws UserForbiddenException {

        if (countriesFromFilters != null && !CollectionUtils.isEmpty(countriesFromFilters)) {
            final List<Long> result = countriesFromFilters.stream().filter(countryInBrad
                    -> countriesWithCanViewPermission.stream().anyMatch(code -> String.valueOf(code).equalsIgnoreCase(countryInBrad.getCode())))
                .map(Country::getId).collect(Collectors.toList());
            if (result.size() != countriesFromFilters.size()) {
                throw new UserForbiddenException("Access denied for some of the countries");
            }
            return result;
        } else {
            return allCountriesInBrad.stream().filter(countryInBrad
                    -> countriesWithCanViewPermission.stream().anyMatch(code -> String.valueOf(code).equalsIgnoreCase(countryInBrad.getCode())))
                .map(Country::getId).collect(Collectors.toList());
        }
    }


}
