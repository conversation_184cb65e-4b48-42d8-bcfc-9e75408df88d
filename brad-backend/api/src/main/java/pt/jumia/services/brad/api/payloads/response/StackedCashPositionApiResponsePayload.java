package pt.jumia.services.brad.api.payloads.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.dtos.StackedCashPositionDto;


@Data
@Value
@AllArgsConstructor
public class StackedCashPositionApiResponsePayload {

    String parentGroupLabel;
    GroupedAccountSummaryResponsePayload[] stack;

    public StackedCashPositionApiResponsePayload(StackedCashPositionDto dto) {

        this.parentGroupLabel = dto.getParentGroupLabel();
        this.stack = dto.getStack().stream()
            .map(GroupedAccountSummaryResponsePayload::new)
            .toArray(GroupedAccountSummaryResponsePayload[]::new);
    }

}
