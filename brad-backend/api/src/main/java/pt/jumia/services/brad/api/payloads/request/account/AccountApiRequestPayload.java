package pt.jumia.services.brad.api.payloads.request.account;

import com.neovisionaries.i18n.CountryCode;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.api.validations.annotations.ValidPayload;
import pt.jumia.services.brad.api.validations.validators.AccountTypePayloadValidator;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Account.CouponPaymentPeriodicity;
import pt.jumia.services.brad.domain.entities.account.Account.StatementPeriodicity;
import pt.jumia.services.brad.domain.entities.account.Account.SubType;
import pt.jumia.services.brad.domain.entities.account.Account.Type;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.enumerations.StatementSource;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@ValidPayload(payloadValidator = AccountTypePayloadValidator.class)
public class AccountApiRequestPayload {
    private Long id;

    @NotBlank
    private String companyID;

    @NotBlank
    @ValidEnumValue(enumClass = CountryCode.class)
    private String countryCode;

    private String partner;

    @NotBlank
    private String navReference;

    private String beneficiaryName;

    private String beneficiaryAddress;

    private String iban;

    private String accountNumber;

    @NotBlank
    private String accountName;

    @Size(max = 20, message = "Phone Number must have at most 20 characters")
    private String phoneNumber;

    private String swiftCode;

    private String accountRoutingCode;

    private String sortCode;

    private String branchCode;

    private String rib;

    @NotBlank
    @ValidEnumValue(required = true, enumClass = Type.class)
    private String type;

    @ValidEnumValue(enumClass = SubType.class)
    private String subType;

    @NotBlank
    private String status;

    @NotBlank
    private String currencyCode;

    @NotBlank
    @ValidEnumValue(enumClass = StatementSource.class)
    private String statementSource;

    @NotBlank
    @ValidEnumValue(required = true, enumClass = StatementPeriodicity.class)
    private String statementPeriodicity;

    private String isin;

    private String contractId;

    @Digits(integer = 14, fraction = 4,
            message = "Amount Deposited must be a valid number with up to 14 digits before and 4 digits after the decimal point")
    private BigDecimal amountDeposited;

    private LocalDate maturityDate;

    @Digits(integer = 14, fraction = 4,
            message = "Nominal Amount must be a valid number with up to 14 digits before and 4 digits after the decimal point")
    private BigDecimal nominalAmount;

    @ValidEnumValue(enumClass = CouponPaymentPeriodicity.class)
    private String couponPaymentPeriodicity;

    @Digits(integer = 14, fraction = 4,
            message = "Coupon Rate must be a valid number with up to 14 digits before and 4 digits after the decimal point")
    private BigDecimal couponRate;

    @Digits(integer = 14, fraction = 4,
            message = "Interest must be a valid number with up to 14 digits before and 4 digits after the decimal point")
    private BigDecimal interest;


    public AccountApiRequestPayload(Account account) {
        this.id = account.getId();
        this.companyID = account.getCompanyID();
        this.countryCode = account.getCountry().getCode();
        this.partner = account.getPartner();
        this.navReference = account.getNavReference();
        this.beneficiaryName = account.getBeneficiaryName();
        this.beneficiaryAddress = account.getBeneficiaryAddress();
        this.iban = account.getIban();
        this.accountNumber = account.getAccountNumber();
        this.accountName = account.getAccountName();
        this.phoneNumber = account.getPhoneNumber();
        this.swiftCode = account.getSwiftCode();
        this.accountRoutingCode = account.getBankRoutingCode();
        this.sortCode = account.getSortCode();
        this.branchCode = account.getBranchCode();
        this.rib = account.getRib();
        this.type = account.getType().name();
        this.subType = account.getSubType() != null ? account.getSubType().name() : null;
        this.status = account.getStatus().getValue();
        this.currencyCode = account.getCurrency().getCode();
        this.statementSource = account.getStatementSource().getValue();
        this.statementPeriodicity = account.getStatementPeriodicity().name();
        this.contractId = account.getContractId();
        this.isin = account.getIsin();
        this.amountDeposited = account.getAmountDeposited();
        this.maturityDate = account.getMaturityDate();
        this.nominalAmount = account.getNominalAmount();
        this.couponPaymentPeriodicity = account.getCouponPaymentPeriodicity() != null ? 
            account.getCouponPaymentPeriodicity().name() : null;
        this.couponRate = account.getCouponRate();
        this.interest = account.getInterest();
    }

    public Account toEntity() throws EntityErrorsException {
        return Account
                .builder()
                .id(id)
                .companyID(companyID)
                .country(Country.builder().code(countryCode).build())
                .partner(partner)
                .currency(Currency.builder().code(currencyCode).build())
                .navReference(navReference)
                .beneficiaryName(beneficiaryName)
                .beneficiaryAddress(beneficiaryAddress)
                .iban(iban)
                .accountNumber(accountNumber)
                .accountName(accountName)
                .phoneNumber(phoneNumber)
                .swiftCode(swiftCode)
                .bankRoutingCode(accountRoutingCode)
                .sortCode(sortCode)
                .branchCode(branchCode)
                .rib(rib)
                .type(Account.Type.valueOf(type))
                .subType(StringUtils.hasText(subType) ? Account.SubType.valueOf(subType) : null)
                .status(Account.Status.fromString(status))
                .statementSource(StatementSource.valueOf(statementSource))
                .statementPeriodicity(StatementPeriodicity.valueOf(statementPeriodicity))
                .isin(isin)
                .contractId(contractId)
                .amountDeposited(amountDeposited)
                .maturityDate(maturityDate)
                .nominalAmount(nominalAmount)
                .couponPaymentPeriodicity(StringUtils.hasText(couponPaymentPeriodicity) ?
                    CouponPaymentPeriodicity.valueOf(couponPaymentPeriodicity) : null)
                .couponRate(couponRate)
                .interest(interest)
                .build();
    }
}
