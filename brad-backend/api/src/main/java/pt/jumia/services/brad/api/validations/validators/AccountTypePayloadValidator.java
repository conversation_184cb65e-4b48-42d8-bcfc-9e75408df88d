package pt.jumia.services.brad.api.validations.validators;


import jakarta.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import pt.jumia.services.brad.api.payloads.request.account.AccountApiRequestPayload;
import pt.jumia.services.brad.domain.entities.account.Account;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
public class AccountTypePayloadValidator implements PayloadValidator<AccountApiRequestPayload> {
    @Override
    public boolean isValid(AccountApiRequestPayload accountPayload, ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();

        int failedCount = 0;

        Account.Type accountType = null;

        try {
            accountType = Account.Type.valueOf(accountPayload.getType());
        } catch (Exception e) {
            addConstraintViolation(context, "type",
                    String.format("Field <%s> is not a valid attribute for account Type: Permitted fields: <%s>",
                            accountPayload.getSubType(), Arrays.toString(Account.SubType.values())));
            failedCount++;
        }

        Account.SubType accountSubType = null;
        try {
            accountSubType = StringUtils.hasText(accountPayload.getSubType()) ?
                    Account.SubType.valueOf(accountPayload.getSubType()) : null;
        } catch (Exception e) {
            addConstraintViolation(context, "subType",
                    String.format("Field <%s> is not a valid attribute for account SubType: Permitted fields: <%s>",
                            accountPayload.getSubType(), Arrays.toString(Account.SubType.values())));
            failedCount++;
        }

        if (failedCount > 0) {
            return false;
        }

        List<AccountTypeField> fieldsToValidate = Arrays.stream(AccountTypeField.values()).toList();

        for (AccountTypeField field : fieldsToValidate) {
            boolean validationPassed = switch (field) {
                case ACCOUNT_NUMBER ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getAccountNumber());
                case IBAN -> validateField(context, accountType, accountSubType, field, accountPayload.getIban());
                case BENEFICIARY_NAME ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getBeneficiaryName());
                case BENEFICIARY_ADDRESS ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getBeneficiaryAddress());
                case SWIFT_CODE ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getSwiftCode());
                case BANK_ROUTING_CODE ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getAccountRoutingCode());
                case SORT_CODE ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getSortCode());
                case BRANCH_CODE ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getBranchCode());
                case RIB -> validateField(context, accountType, accountSubType, field, accountPayload.getRib());
                case PARTNER -> validateField(context, accountType, accountSubType, field, accountPayload.getPartner());
                case PHONE_NUMBER ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getPhoneNumber());
                case SUB_TYPE ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getSubType());
                case ISIN -> validateField(context, accountType, accountSubType, field, accountPayload.getIsin());
                case CONTRACT_ID ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getContractId());
                case INTEREST ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getInterest());
                case AMOUNT_DEPOSITED ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getAmountDeposited());
                case MATURITY_DATE ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getMaturityDate());
                case NOMINAL_AMOUNT ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getNominalAmount());
                case COUPON_PAYMENT_PERIODICITY ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getCouponPaymentPeriodicity());
                case COUPON_RATE ->
                        validateField(context, accountType, accountSubType, field, accountPayload.getCouponRate());
            };

            if (!validationPassed) {
                failedCount++;
            }
        }

        log.debug("Validation completed for account type {}: {}", accountType, failedCount > 0 ? "FAILED" : "SUCCESS");

        return failedCount < 1;
    }

    private <T> boolean validateField(ConstraintValidatorContext context, Account.Type accountType, Account.SubType accountSubType,
                                      AccountTypeField field, T value) {

        Optional<FieldValidationRule> applicableRule = findApplicableRule(field, accountType, accountSubType);

        if (applicableRule.isPresent()) {
            if (applicableRule.get().isRequired() && isEmpty(value)) {
                addConstraintViolation(context, field.getFieldName(),
                        formatRequiredFieldMessage(field.getFieldName(), accountType, accountSubType));
                return false;
            }
        } else {
            if (!isEmpty(value)) {
                addConstraintViolation(context, field.getFieldName(),
                        formatNotApplicableFieldMessage(field.getFieldName(), accountType, accountSubType));
                return false;
            }
        }
        return true;
    }

    private Optional<FieldValidationRule> findApplicableRule(AccountTypeField field,
                                                             Account.Type accountType,
                                                             Account.SubType accountSubType) {
        List<FieldValidationRule> rulesForType = field.getFieldValidationRules().stream()
                .filter(rule -> rule.getAccountType() == accountType)
                .toList();

        if (accountSubType != null) {
            Optional<FieldValidationRule> specificRule = rulesForType.stream()
                    .filter(rule -> !rule.getAccountSubTypes().isEmpty() && rule.getAccountSubTypes().contains(accountSubType))
                    .findFirst();
            if (specificRule.isPresent()) {
                return specificRule;
            }
        }

        return rulesForType.stream()
                .filter(rule -> rule.getAccountSubTypes().isEmpty())
                .findFirst();
    }

    private String formatRequiredFieldMessage(String fieldName, Account.Type accountType, Account.SubType accountSubType) {
        return String.format("Field <%s> is required attribute for account type <%s>%s",
                fieldName,
                accountType,
                (accountSubType != null || Account.Type.INVESTMENTS.equals(accountType)) ?
                        String.format(" with subType <%s>", accountSubType) : "");
    }

    private String formatNotApplicableFieldMessage(String fieldName, Account.Type accountType, Account.SubType accountSubType) {
        return String.format("Field <%s> is not a valid attribute for account type <%s>%s",
                fieldName,
                accountType,
                (accountSubType != null || Account.Type.INVESTMENTS.equals(accountType)) ?
                        String.format(" with subType <%s>", accountSubType) : "");
    }

    private void addConstraintViolation(ConstraintValidatorContext context, String propertyName, String message) {
        context.buildConstraintViolationWithTemplate(message)
                .addPropertyNode(propertyName)
                .addConstraintViolation();
    }

    private <T> boolean isEmpty(T value) {
        if (value == null) {
            return true;
        }

        if (value instanceof String stringValue) {
            return stringValue.trim().isEmpty();
        }

        return false;
    }

    @Getter
    @AllArgsConstructor
    private static class FieldValidationRule {
        Account.Type accountType;
        List<Account.SubType> accountSubTypes;
        boolean required;

        public List<Account.SubType> getAccountSubTypes() {
            return (accountSubTypes == null) ? Collections.emptyList() : Collections.unmodifiableList(accountSubTypes);
        }
    }

    @Getter
    @AllArgsConstructor
    public enum AccountTypeField {
        ACCOUNT_NUMBER("accountNumber",
                List.of(new FieldValidationRule(Account.Type.BANK_ACCOUNT, List.of(), true),
                        new FieldValidationRule(Account.Type.PSP, List.of(), true),
                        new FieldValidationRule(Account.Type.MOBILE_MONEY, List.of(), true),
                        new FieldValidationRule(Account.Type.WALLET, List.of(), true),
                        new FieldValidationRule(Account.Type.INVESTMENTS, List.of(), false))),
        IBAN("iban",
                List.of(new FieldValidationRule(Account.Type.BANK_ACCOUNT, List.of(), false))),
        BENEFICIARY_NAME("beneficiaryName",
                List.of(new FieldValidationRule(Account.Type.BANK_ACCOUNT, List.of(), false))),
        BENEFICIARY_ADDRESS("beneficiaryAddress",
                List.of(new FieldValidationRule(Account.Type.BANK_ACCOUNT, List.of(), false))),
        SWIFT_CODE("swiftCode",
                List.of(new FieldValidationRule(Account.Type.BANK_ACCOUNT, List.of(), false))),
        BANK_ROUTING_CODE("accountRoutingCode",
                List.of(new FieldValidationRule(Account.Type.BANK_ACCOUNT, List.of(), false))),
        SORT_CODE("sortCode",
                List.of(new FieldValidationRule(Account.Type.BANK_ACCOUNT, List.of(), false))),
        BRANCH_CODE("branchCode",
                List.of(new FieldValidationRule(Account.Type.BANK_ACCOUNT, List.of(), false))),
        RIB("rib",
                List.of(new FieldValidationRule(Account.Type.BANK_ACCOUNT, List.of(), false))),
        PARTNER("partner",
                List.of(new FieldValidationRule(Account.Type.PSP, List.of(), true),
                        new FieldValidationRule(Account.Type.MOBILE_MONEY, List.of(), true),
                        new FieldValidationRule(Account.Type.WALLET, List.of(), true))),
        PHONE_NUMBER("phoneNumber",
                List.of(new FieldValidationRule(Account.Type.PSP, List.of(), false),
                        new FieldValidationRule(Account.Type.MOBILE_MONEY, List.of(), true))),
        SUB_TYPE("subType",
                List.of(new FieldValidationRule(Account.Type.INVESTMENTS, List.of(), true))),
        ISIN("isin",
                List.of(new FieldValidationRule(Account.Type.INVESTMENTS, List.of(Account.SubType.BONDS), true))),
        CONTRACT_ID("contractId",
                List.of(new FieldValidationRule(Account.Type.INVESTMENTS,
                        List.of(Account.SubType.TERM_DEPOSITS, Account.SubType.TLF, Account.SubType.BANK_GUARANTEES), true))),
        AMOUNT_DEPOSITED("amountDeposited",
                List.of(new FieldValidationRule(Account.Type.INVESTMENTS,
                        List.of(Account.SubType.TERM_DEPOSITS, Account.SubType.TLF, Account.SubType.BANK_GUARANTEES, Account.SubType.BONDS), true))),
        MATURITY_DATE("maturityDate",
                List.of(new FieldValidationRule(Account.Type.INVESTMENTS,
                        List.of(Account.SubType.TERM_DEPOSITS, Account.SubType.TLF, Account.SubType.BANK_GUARANTEES, Account.SubType.BONDS), true))),
        NOMINAL_AMOUNT("nominalAmount",
                List.of(new FieldValidationRule(Account.Type.INVESTMENTS, List.of(Account.SubType.BONDS), true))),
        COUPON_PAYMENT_PERIODICITY("couponPaymentPeriodicity",
                List.of(new FieldValidationRule(Account.Type.INVESTMENTS, List.of(Account.SubType.BONDS), true))),
        COUPON_RATE("couponRate",
                List.of(new FieldValidationRule(Account.Type.INVESTMENTS, List.of(Account.SubType.BONDS), true))),
        INTEREST("interest",
                List.of(new FieldValidationRule(Account.Type.INVESTMENTS,
                        List.of(Account.SubType.TERM_DEPOSITS, Account.SubType.TLF, Account.SubType.BANK_GUARANTEES), true)));

        private final String fieldName;
        private final List<FieldValidationRule> fieldValidationRules;
    }
}
