package pt.jumia.services.brad.api.controllers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.neovisionaries.i18n.CountryCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.brad.api.csvs.CsvBuilder;
import pt.jumia.services.brad.api.csvs.exports.StatementExportCSV;
import pt.jumia.services.brad.api.csvs.imports.TransactionImportCsv;
import pt.jumia.services.brad.api.payloads.request.PageFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.accountstatement.*;
import pt.jumia.services.brad.api.payloads.response.AccountStatementApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.ApiLog;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementSortFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementFlow;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityMismatchException;
import pt.jumia.services.brad.domain.exceptions.InvalidFileException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.NotPositiveException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.FindExceptionRootCauseUseCase;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.UpdateAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.apilog.CreateApiLogUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.CreateAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.DiscardAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.ReadAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.RetryAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.finrecstatement.ReadFinrecStatementUseCase;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.text.ParseException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/account-statements")
public class AccountStatementController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;

    private final CreateAccountStatementUseCase createAccountStatementUseCase;
    private final ReadAccountStatementUseCase readAccountStatementUseCase;
    private final RetryAccountStatementUseCase retryAccountStatementUseCase;
    private final DiscardAccountStatementUseCase discardAccountStatementUseCase;
    private final ReadAccountsUseCase readAccountsUseCase;
    private final UpdateAccountStatementUseCase updateAccountStatementUseCase;

    private final ReadFinrecStatementUseCase readFinrecStatementUseCase;

    private final CreateApiLogUseCase createApiLogUseCase;
    @GetMapping
    public PageApiResponsePayload<AccountStatementApiResponsePayload> fetch(HttpServletRequest httpServletRequest,
                       @Valid AccountStatementFiltersApiRequestPayload accountStatementFiltersApiRequestPayload,
                       @Valid AccountStatementSortFiltersApiRequestPayload accountStatementSortFiltersApiRequestPayload,
                       @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload)
            throws UserForbiddenException, ParseException, EntityErrorsException, NotFoundException {

        String country = accountStatementFiltersApiRequestPayload.getPartitionKey() != null
                ? readAccountsUseCase.execute(Integer.parseInt(accountStatementFiltersApiRequestPayload.getPartitionKey())).getCountry().getCode()
                : readAccountsUseCase.execute(Math.toIntExact(accountStatementFiltersApiRequestPayload.getAccountID())).getCountry().getCode();

        validateUserAccessUseCase.checkCanAccessStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching all account statements for user with identifier {}",
                RequestContext.getUsername());


        AccountStatementFilters accountStatementFilters = accountStatementFiltersApiRequestPayload.toEntity();
        AccountStatementSortFilters accountStatementSortFilters = accountStatementSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();


        List<AccountStatementApiResponsePayload> accountStatement = readAccountStatementUseCase.execute(accountStatementFilters,
                accountStatementSortFilters, pageFilters)
                .stream().map(AccountStatementApiResponsePayload::new).collect(Collectors.toList());


        long total = readAccountStatementUseCase.executeCount(accountStatementFilters);


        return PageApiResponsePayload.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                accountStatement,
                total
        );
    }

    @GetMapping(value = "/troubleshooting-ordered")
    public List<AccountStatementApiResponsePayload> fetch(HttpServletRequest httpServletRequest,
                       @Valid AccountStatementFiltersApiRequestPayload accountStatementFiltersApiRequestPayload)
            throws Throwable {

        String country = readAccountsUseCase.execute(Math.toIntExact(accountStatementFiltersApiRequestPayload.getAccountID()))
                .getCountry().getCode();

        validateUserAccessUseCase.checkCanAccessStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching all account statements, ordered, for user with identifier {}",
                RequestContext.getUsername());

        AccountStatementFilters accountStatementFilters = accountStatementFiltersApiRequestPayload.toEntity().toBuilder()
                .status(
                    accountStatementFiltersApiRequestPayload.getStatus() == null || accountStatementFiltersApiRequestPayload.getStatus().isEmpty() ?
                        List.of(AccountStatementStatus.REVIEW) :
                        accountStatementFiltersApiRequestPayload.getStatus().stream().map(AccountStatementStatus::fromString).toList())
                .build();

        return readAccountStatementUseCase.executeAllStatementsOrdered(accountStatementFilters)
                .stream().map(AccountStatementApiResponsePayload::new).toList();

    }

    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public AccountStatementApiResponsePayload create(
        @Valid @RequestBody AccountStatementWithTransactionApiRequestPayload payload)
            throws ParseException, UserForbiddenException, EntityErrorsException,
            DatabaseErrorsException, JsonProcessingException, AlreadyExistsException,
            NotFoundException, EntityMismatchException, NotPositiveException {

        String country = readAccountsUseCase.execute(payload.getAccountStatement().getAccountID())
                .getCountry().getCode();

        validateUserAccessUseCase.checkCanAccessStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Creating statement with transactions {} for user with identifier {}",
                payload.getAccountStatement(), RequestContext.getUsername());

        ObjectMapper objectMapper = new ObjectMapper();
        String requestPayloadJson = objectMapper.writer().withDefaultPrettyPrinter().writeValueAsString(payload);


        try {
            AccountStatement createdAccountStatement = createAccountStatementUseCase.execute(
                    payload.toAccountStatementEntity().toBuilder()
                            .flow(AccountStatementFlow.AUTOMATIC)
                            .build(),
                    payload.getAccountStatement().getAccountID(),
                    payload.toTransactionEntities()
            );

            AccountStatementApiResponsePayload responsePayload = new AccountStatementApiResponsePayload(createdAccountStatement);

            JSONObject responsePayloadJson = new JSONObject();
            responsePayload.toJson(responsePayloadJson);

            this.createApiLog(requestPayloadJson,
                    responsePayloadJson.toString(),
                    getRelatedEntities(createdAccountStatement.getStatementId(),
                            createdAccountStatement.getAccount().getAccountNumber()),
                    ApiLog.ApiLogStatus.SUCCESS);

            return responsePayload;
        } catch (Exception e) {
            Throwable ex = FindExceptionRootCauseUseCase.findRootCause(e);
            this.createApiLog(requestPayloadJson,
                    ex.getMessage(),
                    getRelatedEntities(payload.getAccountStatement().getStatementId(),
                            null),
                    ApiLog.ApiLogStatus.FAILURE);
            throw e;
        }
    }

    @GetMapping(value = "/download")
    @ResponseStatus(HttpStatus.OK)
    public Future<ResponseEntity<byte[]>> download(HttpServletRequest httpServletRequest,
                                                  @Valid AccountStatementFiltersApiRequestPayload accountStatementFiltersApiRequestPayload,
                                                  @Valid AccountStatementSortFiltersApiRequestPayload accountStatementSortFiltersApiRequestPayload,
                                                  @Valid PageFiltersApiRequestPayload pageFiltersApiRequestPayload)
            throws UserForbiddenException, ParseException, NotFoundException {

        String country = readAccountsUseCase.execute(Integer.parseInt(accountStatementFiltersApiRequestPayload.getPartitionKey()))
                .getCountry().getCode();

        validateUserAccessUseCase.checkCanExportStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Downloading statements for user with identifier {}", RequestContext.getUsername());


        AccountStatementFilters accountStatementFilters = accountStatementFiltersApiRequestPayload.toEntity();
        AccountStatementSortFilters accountStatementSortFilters = accountStatementSortFiltersApiRequestPayload.toEntity();
        PageFilters pageFilters = pageFiltersApiRequestPayload.toBuilder()
                .size(readAccountStatementUseCase.executeCount(accountStatementFilters))
                .build()
                .toEntity();

        CompletableFuture<ResponseEntity<byte[]>> future = new CompletableFuture<>();
        CompletableFuture.runAsync(() -> {
            try {
                List<AccountStatement> accountStatements = readAccountStatementUseCase.execute(accountStatementFilters,
                    accountStatementSortFilters, pageFilters);
                if (accountStatements.isEmpty()) {
                    future.completeExceptionally(new NotFoundException("No statements found"));
                    return;
                }
                future.complete(StatementExportCSV.buildStatementCSV(accountStatements));
            } catch (Exception e) {
                log.error("Error downloading statements", e);
                future.completeExceptionally(e);
            }
        });
        return future;
    }

    @GetMapping(value = "/{id}")
    public AccountStatementApiResponsePayload fetchByID(@PathVariable Long id)
            throws NotFoundException, UserForbiddenException {

        AccountStatement accountStatement = readAccountStatementUseCase.execute(id);
        String country = accountStatement.getAccount().getCountry().getCode();

        validateUserAccessUseCase.checkCanAccessStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Reading statement with id {} for user with identifier {}", id, RequestContext.getUsername());

        return new AccountStatementApiResponsePayload(accountStatement);
    }


    @PostMapping(value = "/upload-file")
    @ResponseStatus(value = HttpStatus.CREATED)
    public AccountStatementApiResponsePayload createWithFile(
            @Valid @RequestBody AccountStatementWithFileApiRequestPayload payload)
            throws ParseException, UserForbiddenException, InvalidFileException,
            AlreadyExistsException, EntityErrorsException, NotFoundException,
            DatabaseErrorsException, EntityMismatchException, NotPositiveException {

        String country = readAccountsUseCase.execute(payload.getAccountStatement().getAccountID()).getCountry().getCode();
        validateUserAccessUseCase.checkCanUploadStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Creating statement with file {} for user with identifier {}",
                 payload.getAccountStatement(), RequestContext.getUsername());

        final AccountStatement accountStatement = payload.toAccountStatementEntity().toBuilder()
                .flow(AccountStatementFlow.MANUAL)
                .build();

        log.info("Parsing csv file to extract transactions.");
        List<Transaction> transactions = CsvBuilder.readCsv(payload.getTransactions(), new TransactionImportCsv());
        log.info("File parsed. Total transactions extracted {}. Creating account statement", transactions.size());

        AccountStatement result = createAccountStatementUseCase.execute(
                accountStatement,
                payload.getAccountStatement().getAccountID(),
                transactions,
                payload.getNextStatementId());

        log.info("Account statement created {}.", result.getStatementId());

        return new AccountStatementApiResponsePayload(result);
    }

    @GetMapping(value = "/status-types")
    public List<String> fetchStatusTypes() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());

        log.info("Fetching all statement status types for user with identifier {}",
                RequestContext.getUsername());
        return readAccountStatementUseCase.executeStatusTypes();
    }

    @GetMapping(value = "/error-types")
    public List<String> fetchErrorTypes() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());

        log.info("Fetching all statement error types for user with identifier {}",
                RequestContext.getUsername());
        return readAccountStatementUseCase.executeErrorTypes();
    }

    @GetMapping(value = "/directions")
    public List<String> fetchDirections() throws UserForbiddenException {

        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());

        log.info("Fetching all statement directions for user with identifier {}",
                RequestContext.getUsername());
        return readAccountStatementUseCase.executeDirections();
    }

    @GetMapping("/retry")
    public String retryStatement(@RequestParam Long statementId) throws UserForbiddenException, NotFoundException,
            EntityErrorsException, DatabaseErrorsException {

        AccountStatement accountStatement = readAccountStatementUseCase.execute(statementId);
        String country = readAccountsUseCase.execute(Math.toIntExact(accountStatement.getAccount().getId())).getCountry().getCode();

        validateUserAccessUseCase.checkCanAccessStatements(RequestContext.getUser(), CountryCode.valueOf(country));
        validateUserAccessUseCase.checkCanRetryStatement(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Retrying statement with identifier {} for user with identifier {}",
                 statementId, RequestContext.getUsername());

        return retryAccountStatementUseCase.execute(statementId);
    }

    @DeleteMapping("/discard/{id}")
    public void discardStatement(@PathVariable Long id) throws UserForbiddenException, NotFoundException, EntityErrorsException {

        AccountStatement accountStatement = readAccountStatementUseCase.execute(id);
        String country = readAccountsUseCase.execute(Math.toIntExact(accountStatement.getAccount().getId())).getCountry().getCode();

        validateUserAccessUseCase.checkCanDiscardStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Discarding statement with identifier {} for user with identifier {}",
                 id, RequestContext.getUsername());

        discardAccountStatementUseCase.execute(id);
    }

    @DeleteMapping("/discard/last-imported/{id}")
    public void discardLastImportedStatement(@PathVariable Long id) throws UserForbiddenException, EntityErrorsException {

        AccountStatement accountStatement = readAccountStatementUseCase.execute(id);
        String country = readAccountsUseCase.execute(Math.toIntExact(accountStatement.getAccount().getId())).getCountry().getCode();

        validateUserAccessUseCase.checkCanDiscardImportedStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Discarding Last imported statement with identifier {} for user with identifier {}",
                id, RequestContext.getUsername());

        discardAccountStatementUseCase.executeDiscardLastImportedStatement(id);
    }

    @GetMapping("/last/{accountId}")
    public AccountStatementApiResponsePayload fetchLastStatement(@PathVariable long accountId)
            throws UserForbiddenException, NotFoundException {

        String country = readAccountsUseCase.execute(Math.toIntExact(accountId)).getCountry().getCode();
        validateUserAccessUseCase.checkCanAccessStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching last statement for account with identifier {} for user with identifier {}",
            accountId, RequestContext.getUsername());

        return readAccountStatementUseCase.executeLastStatement(accountId)
                .map(AccountStatementApiResponsePayload::new)
                .orElse(null);
    }

    @GetMapping("/first-error/{accountId}")
    public AccountStatementApiResponsePayload fetchFirstInError(@PathVariable long accountId)
            throws NotFoundException, UserForbiddenException, EntityErrorsException {

        String country = readAccountsUseCase.execute(Math.toIntExact(accountId)).getCountry().getCode();
        validateUserAccessUseCase.checkCanAccessStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetch the first statement in error for account with identifier {} for user with identifier {}",
            accountId, RequestContext.getUsername());

        return new AccountStatementApiResponsePayload(readAccountStatementUseCase.executeFirstStatementInError(accountId));
    }

    @GetMapping("/last-imported/{accountId}")
    public AccountStatementApiResponsePayload fetchLastImportedStatement(@PathVariable long accountId)
            throws UserForbiddenException, EntityErrorsException, NotFoundException {

        String country = readAccountsUseCase.execute(Math.toIntExact(accountId)).getCountry().getCode();
        validateUserAccessUseCase.checkCanAccessStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        log.info("Fetching last imported statement for account with identifier {} for user with identifier {}",
            accountId, RequestContext.getUsername());

        AccountStatement accountStatement = readAccountStatementUseCase.executeFetchLastImportedStatement(accountId);

        return accountStatement != null ? new AccountStatementApiResponsePayload(accountStatement) : null;

    }

    @GetMapping(value = "/sync/{date}")
    public void syncByDate(@PathVariable(value = "date") String date)
            throws UserForbiddenException, DatabaseErrorsException, ParseException {

        validateUserAccessUseCase.checkCanManageScheduler(RequestContext.getUser());

        log.info("Syncing finrec account statements by date {} for user with identifier {}",
                date, RequestContext.getUsername());

        readFinrecStatementUseCase.executeByDate(DateParser.parseToLocalDateTime(date));

    }

    @PostMapping(value = "/update-statement")
    @ResponseStatus(value = HttpStatus.OK)
    public ResponseEntity<JSONObject> updateStatement(@Valid @RequestBody UpdateStatementRequestPayload payload)
            throws UserForbiddenException, NotFoundException, EntityErrorsException, JsonProcessingException {
        AccountStatement accountStatement = readAccountStatementUseCase.execute(payload.getStatementId());
        String country = accountStatement.getAccount().getCountry().getCode();
        validateUserAccessUseCase.checkCanChangeStatements(RequestContext.getUser(), CountryCode.valueOf(country));

        ObjectMapper objectMapper = new ObjectMapper();

        log.info("DataFix request from {} for statement id {} details: {}",
                RequestContext.getUsername(),
                payload.getStatementId(),
                objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(payload));


        AccountStatement updated = accountStatement.toBuilder()
                .status(AccountStatementStatus.fromString(payload.getNewStatus()))
                .statusDescription(AccountStatementStatus.Description.valueOf(payload.getNewDescription()))
                .build();

        updateAccountStatementUseCase.execute(updated);

        JSONObject response = new JSONObject();
        response.put("status", "SUCCESS");
        response.put("request", payload);

        return ResponseEntity.ok(response);
    }

    private void createApiLog(String requestPayloadJson, String responsePayloadJson, String relatedEntityId,
                              ApiLog.ApiLogStatus status) {
        try {
            createApiLogUseCase.execute(ApiLog.builder()
                    .logType(ApiLog.ApiLogType.BANK_STATEMENT_CREATION)
                    .request(requestPayloadJson)
                    .relatedEntityId(relatedEntityId)
                    .response(responsePayloadJson)
                    .logStatus(status)
                    .build());
        } catch (Exception ex) {
            log.error("Error creating api log: {}", ex.getMessage());
        }
    }


    private String getRelatedEntities(String accountStatement, String account) {
        JSONObject relatedEntitiesJson = new JSONObject();
        if (!Objects.isNull(accountStatement)) {
            relatedEntitiesJson.put("accountStatement", accountStatement);
        }
        if (!Objects.isNull(account)) {
            relatedEntitiesJson.put("account", account);
        }
        return relatedEntitiesJson.toString();
    }
}
