package pt.jumia.services.brad.api.csvs.exports;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import org.springframework.http.ResponseEntity;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.api.validations.annotations.CsvHeaderName;
import pt.jumia.services.brad.domain.entities.account.User;

import java.time.LocalDateTime;
import java.util.List;

import static pt.jumia.services.brad.api.csvs.CsvBuilder.buildCsv;

@Builder(toBuilder = true)
public class UserExportsCSV {

    @CsvHeaderName("ID")
    public Long id;
    @CsvHeaderName("Company ID")
    public String companyID;
    @CsvHeaderName("Country")
    public String countryName;
    @CsvHeaderName("NAV Reference")
    public String navReference;
    @CsvHeaderName("Name")
    public String name;
    @CsvHeaderName("Email")
    public String email;
    @CsvHeaderName("Status")
    public String status;
    @CsvHeaderName("Bank Account Number")
    public String bankAccountNumber;
    @CsvHeaderName("Created At")
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    public LocalDateTime createdAt;
    @CsvHeaderName("Created By")
    public String createdBy;
    @CsvHeaderName("Updated At")
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    public LocalDateTime updatedAt;
    @CsvHeaderName("Updated By")
    public String updatedBy;
    @CsvHeaderName("HR Role")
    public String hrRole;
    @CsvHeaderName("Permission Type")
    public String permissionType;
    @CsvHeaderName("Phone Number")
    public String mobilePhoneNumber;

    public static ResponseEntity<byte[]> buildUsersCSV(List<User> users) {

        List<UserExportsCSV> contactExportCSVs = users.stream()
                .map((user) -> UserExportsCSV.builder()
                        .id(user.getId())
                        .companyID(user.getAccount().getCompanyID())
                        .countryName(user.getAccount().getCountry().getName())
                        .navReference(user.getAccount().getNavReference())
                        .name(user.getName())
                        .email(user.getEmail())
                        .status(user.getStatus().name())
                        .bankAccountNumber(user.getAccount().getAccountNumber())
                        .createdAt(user.getCreatedAt())
                        .createdBy(user.getCreatedBy())
                        .updatedAt(user.getUpdatedAt())
                        .updatedBy(user.getUpdatedBy())
                        .hrRole(user.getHrRole())
                        .permissionType(user.getPermissionType())
                        .mobilePhoneNumber(user.getMobilePhoneNumber())
                        .build())
                .toList();

        return buildCsv(contactExportCSVs);
    }
}

