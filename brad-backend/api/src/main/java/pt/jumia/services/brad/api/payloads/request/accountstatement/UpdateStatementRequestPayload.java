package pt.jumia.services.brad.api.payloads.request.accountstatement;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

@Data
public class UpdateStatementRequestPayload {

    @NotNull(message = "Statement ID cannot be null")
    private Long statementId;

    @NotBlank(message = "New status is required")
    private String newStatus;

    @NotBlank(message = "New status description is required")
    private String newDescription;

    @NotBlank(message = "JIRA ticket is required")
    private String jiraTicket;

}
