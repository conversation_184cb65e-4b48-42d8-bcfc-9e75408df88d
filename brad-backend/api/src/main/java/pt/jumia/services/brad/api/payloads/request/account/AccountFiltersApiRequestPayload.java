package pt.jumia.services.brad.api.payloads.request.account;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Account.StatementPeriodicity;
import pt.jumia.services.brad.domain.entities.account.Account.TroubleShooting;
import pt.jumia.services.brad.domain.entities.filter.account.AccountFilters;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;
import pt.jumia.services.brad.domain.enumerations.StatementSource;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class AccountFiltersApiRequestPayload {

    private static final String yyyy_MM_dd = "yyyy-MM-dd";

    private String filterText;

    private String companyID;

    private List<Long> countryCodes;

    private String navReference;

    private String beneficiaryName;

    private String beneficiaryAddress;

    private String iban;

    private String accountNumber;

    private String accountName;

    private String swiftCode;

    private String accountRoutingCode;

    private String sortCode;

    private String branchCode;

    private String rib;

    private String partner;

    private String phoneNumber;

    @ValidEnumValue(required = false, enumClass = TroubleShooting.class)
    private String troubleshooting;

    private List<Account.Type> types;

    private List<Account.SubType> subTypes;

    private List<Account.Status> status;

    private List<String> currencyCodes;

    @DateTimeFormat(pattern = yyyy_MM_dd)
    private LocalDate lastProcessedStatementDateStart;

    @DateTimeFormat(pattern = yyyy_MM_dd)
    private LocalDate lastProcessedStatementDateEnd;

    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAtStart;

    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAtEnd;

    private List<String> selectedFields;

    @ValidEnumValue(required = false, enumClass = StatementSource.class)
    private String statementSource;

    private List<StatementPeriodicity> statementPeriodicity;

    private String investmentId;

    private Boolean isNegativeBalance;

    public AccountFilters toEntity() throws ParseException {
        return AccountFilters
                .builder()
                .filterText(filterText)
                .companyID(companyID)
                .countryCodes(countryCodes)
                .navReference(navReference)
                .beneficiaryName(beneficiaryName)
                .beneficiaryAddress(beneficiaryAddress)
                .iban(iban)
                .accountNumber(accountNumber)
                .accountName(accountName)
                .swiftCode(swiftCode)
                .bankRoutingCode(accountRoutingCode)
                .sortCode(sortCode)
                .branchCode(branchCode)
                .rib(rib)
                .partner(partner)
                .phoneNumber(phoneNumber)
                .troubleshooting(troubleshooting == null ? null : TroubleShooting.valueOf(troubleshooting))
                .types(types)
                .subTypes(subTypes)
                .status(status)
                .currencyCodes(currencyCodes)
                .lastProcessedStatementDateStart(lastProcessedStatementDateStart)
                .lastProcessedStatementDateEnd(lastProcessedStatementDateEnd)
                .createdAtStart(createdAtStart)
                .createdAtEnd(createdAtEnd)
                .selectedFields(Objects.isNull(selectedFields) ? List.of() : BaseSelectFields.fromSelectCodes(
                                Account.SelectFields.class, selectedFields)
                        .stream().map(Account.SelectFields::getQueryField).toList()
                )
                .statementSource(statementSource == null ? null : StatementSource.valueOf(statementSource))
                .statementPeriodicity(statementPeriodicity)
                .investmentId(investmentId)
                .isNegativeBalance(isNegativeBalance != null && isNegativeBalance)
                .build();
    }
}
