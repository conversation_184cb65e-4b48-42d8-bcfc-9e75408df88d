package pt.jumia.services.brad.api.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.api.csvs.CsvBuilder;
import pt.jumia.services.brad.api.payloads.request.account.AccountApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.account.AccountFiltersApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.account.AccountNetChangeRequestPayload;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Account.CouponPaymentPeriodicity;
import pt.jumia.services.brad.domain.entities.account.Account.SubType;
import pt.jumia.services.brad.domain.entities.account.Account.TroubleShooting;
import pt.jumia.services.brad.domain.entities.account.Account.Type;
import pt.jumia.services.brad.domain.entities.dtos.AccountNetChangeResultDto;
import pt.jumia.services.brad.domain.entities.dtos.AccountTroubleshootingDto;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.accounts.CreateAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.accounts.DeleteAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.accounts.ExportAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.accounts.UpdateAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.hamcrest.Matchers.hasSize;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Tests the controller at API level.
 * In here you should test the API validations and return content
 *
 * Note that you need to mock all the dependencies of your controller
 */
@WebMvcTest(AccountController.class)
public class AccountControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private CreateAccountsUseCase createAccountsUseCase;

    @MockBean
    private ReadAccountsUseCase readAccountsUseCase;

    @MockBean
    private UpdateAccountsUseCase updateAccountsUseCase;

    @MockBean
    private DeleteAccountsUseCase deleteAccountsUseCase;

    @MockBean
    private ExportAccountsUseCase exportAccountsUseCase;

    @MockBean
    private CsvBuilder csvBuilder;

    @MockBean
    private ReadCountriesUseCase readCountriesUseCase;

    private ObjectMapper objectMapper;

    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);

        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
    }


    //Create Account Tests

    @Test
    public void createAccount_failure_AlreadyExists() throws Exception {
        when(createAccountsUseCase.execute(any()))
                .thenThrow(AlreadyExistsException.createAlreadyExists(Account.class, "1"));
        Account account = FakeAccounts.getFakeAccounts(1, null).get(0);
        AccountApiRequestPayload payload = new AccountApiRequestPayload(account);

        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isConflict());
    }

    //Fetch  Account Tests

    @Test
    public void testFetch_success() throws Exception {
        List<Account> accounts = FakeAccounts.getFakeAccounts(20, null);
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.DESC);

        AccountFilters accountFilters = AccountFilters.builder().build();
        when(readAccountsUseCase.execute(accountFilters, accountSortFilters, pageFilters))
                .thenReturn(accounts);

        mockMvc
                .perform(get("/api/accounts"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetch_emptyList_success() throws Exception {
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.DESC);

        AccountFilters accountFilters = AccountFilters.builder().build();
        when(readAccountsUseCase.execute(accountFilters, accountSortFilters, pageFilters))
                .thenReturn(Collections.emptyList());

        mockMvc
                .perform(get("/api/accounts"))
                .andExpect(status().isOk())
                .andReturn();
    }

    //Fetch  Account by ID Tests

    @Test
    public void testFetchById_validId_success() throws Exception {
        Account mockAccount = FakeAccounts.getFakeAccounts(1, null).get(0);

        when(readAccountsUseCase.execute(1)).thenReturn(mockAccount);

        mockMvc.perform(get("/api/accounts/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(mockAccount.getId()))
                .andExpect(jsonPath("$.companyID").value(mockAccount.getCompanyID()))
                .andExpect(jsonPath("$.country.id").value(mockAccount.getCountry().getId()))
                .andExpect(jsonPath("$.navReference").value(mockAccount.getNavReference()))
                .andExpect(jsonPath("$.beneficiaryName").value(mockAccount.getBeneficiaryName()))
                .andExpect(jsonPath("$.beneficiaryAddress").value(mockAccount.getBeneficiaryAddress()))
                .andExpect(jsonPath("$.accountNumber").value(mockAccount.getAccountNumber()))
                .andExpect(jsonPath("$.accountName").value(mockAccount.getAccountName()))
                .andExpect(jsonPath("$.swiftCode").value(mockAccount.getSwiftCode()))
                .andExpect(jsonPath("$.accountRoutingCode").value(mockAccount.getBankRoutingCode()))
                .andExpect(jsonPath("$.sortCode").value(mockAccount.getSortCode()))
                .andExpect(jsonPath("$.branchCode").value(mockAccount.getBranchCode()))
                .andExpect(jsonPath("$.rib").value(mockAccount.getRib()));

    }

    @Test
    public void testFetchById_invalidId_notFoundException() throws Exception {
        when(readAccountsUseCase.execute(2)).thenThrow(new NotFoundException("Account not found"));

        mockMvc.perform(get("/api/accounts/2"))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testFetchById_nonIntegerId_methodArgumentTypeMismatchException() throws Exception {
        mockMvc.perform(get("/api/accounts/abc"))
                .andExpect(status().isBadRequest())
                .andExpect(result -> assertInstanceOf(MethodArgumentTypeMismatchException.class, result.getResolvedException()));
    }

    @Test
    public void testFetchAdditionalInfoById_validId_success() throws Exception {
        int id = 1;
        Account mockAccount = FakeAccounts.getFakeAccounts(1, null).get(0).toBuilder()
                .balanceLocalCurrency(BigDecimal.TEN)
                .balanceUSD(BigDecimal.ONE)
                .localCurrency(FakeCurrencies.NGN)
                .build();

        when(readAccountsUseCase.executeAdditionalInfo(id)).thenReturn(mockAccount);

        mockMvc
                .perform(get("/api/accounts/additional-info/" + id))
                .andExpect(status().isOk())
                .andReturn();
    }


    //Delete Account Tests

    @Test
    public void testDeleteSuccess() throws Exception {
        Account account = FakeAccounts.FAKE_ACCOUNT;
        when(readAccountsUseCase.execute(Math.toIntExact(account.getId()))).thenReturn(account);
        int id = 1;
        doNothing().when(deleteAccountsUseCase).execute(id);
        mockMvc.perform(delete("/api/accounts/{id}", id))
                .andExpect(status().isNoContent());
    }

    @Test
    public void testDeleteNonExisting() throws Exception {
        Account account = FakeAccounts.FAKE_ACCOUNT;
        when(readAccountsUseCase.execute(Math.toIntExact(account.getId()))).thenReturn(account);

        int id = 1;
        doThrow(new NotFoundException("Account not found")).when(deleteAccountsUseCase).execute(id);
        mockMvc.perform(delete("/api/accounts/{id}", id))
                .andExpect(status().isNotFound());
    }

    @Test
    public void testDeleteInvalidId() throws Exception {
        String id = "invalidId";
        mockMvc.perform(delete("/api/accounts/{id}", id))
                .andExpect(status().isBadRequest());

    }

    //Update Account Tests

    @Test
    public void testUpdateAccount() throws Exception {
        when(readAccountsUseCase.execute(1))
                .thenReturn(FakeAccounts.getFakeAccounts(1, null).get(0));

        Account account = FakeAccounts.getFakeAccounts(1, null).get(0);
        AccountApiRequestPayload payload = new AccountApiRequestPayload(account);
        payload.setAccountName("newBankName");


        mockMvc.perform(put("/api/accounts/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isOk());

        verify(updateAccountsUseCase).execute(any(Account.class));

    }

    //Update Account last processed statement date

    @Test
    public void testUpdateAccountLastProcessedStatementDate() throws Exception {

        when(readAccountsUseCase.execute(anyInt())).thenReturn(FakeAccounts.getFakeAccounts(1, null).get(0));
        doNothing().when(validateUserAccessUseCase).checkCanManageAccounts(any(), any());
        List<Integer> payload = List.of(1, 2, 3, 4);

        mockMvc.perform(patch("/api/accounts/sync-last-processed-statement-date")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isOk());

        verify(updateAccountsUseCase, times(payload.size())).executeLastProcessedStatementDate(any());

    }

    //Download Account Tests

    //@Test
    public void testDownloadAccount() throws Exception {
        Account account = FakeAccounts.FAKE_ACCOUNT;
        AccountFiltersApiRequestPayload payload = new AccountFiltersApiRequestPayload();
        List<Account> accounts = FakeAccounts.getFakeAccounts(20, null);
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(accounts.size());

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.DESC);

        AccountFilters accountFilters = AccountFilters.builder()
                .accountNumber("fakeAccountNumber").build();

        when(readAccountsUseCase.execute(accountFilters.getAccountNumber())).thenReturn(account);

        when(readAccountsUseCase.executeCount(accountFilters))
                .thenReturn(accounts.size());

        when(readAccountsUseCase.execute(accountFilters, accountSortFilters, pageFilters))
                .thenReturn(accounts);


        mockMvc.perform(get("/api/accounts/download?accountNumber=fakeAccountNumber")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isOk());
    }

    @Test
    public void fetchTroubleShootingAccounts_success() throws Exception {
        List<AccountTroubleshootingDto> troubleshootingAccounts = FakeAccounts.getFakeAccounts(10, Type.BANK_ACCOUNT).stream()
                .map(v -> new AccountTroubleshootingDto(v, true, false, true)).collect(
                        Collectors.toList());
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.DESC);

        AccountFilters accountFilters = AccountFilters.builder().troubleshooting(TroubleShooting.STATEMENT_VALIDATION).build();
        when(readAccountsUseCase.executeAllTroubleshootingAccounts(accountFilters, accountSortFilters, pageFilters))
                .thenReturn(troubleshootingAccounts);

        mockMvc
                .perform(get("/api/accounts/troubleshooting"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testBankAccountValidation_withInvalidFields_success() throws Exception {
        var fakeBankAccount = FakeAccounts.getFakeAccounts(1, Account.Type.BANK_ACCOUNT).get(0);
        var invalidFakeBankAccount = fakeBankAccount.toBuilder()
                .iban("**********************")
                .beneficiaryName("Valid Name")
                .beneficiaryAddress("Valid Address")
                .swiftCode("VALIDBIC")
                .bankRoutingCode("VALIDROUTE")
                .sortCode("123456")
                .branchCode("789")
                .rib("12345678901234567890123")
                .partner("invalidPartner")
                .phoneNumber("invalidPhone")
                .build();
        AccountApiRequestPayload payload = new AccountApiRequestPayload(invalidFakeBankAccount);
        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorFields").isArray())
                .andExpect(jsonPath("$.errorFields[?(@.field=='phoneNumber')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='phoneNumber')].error")
                        .value("Field <phoneNumber> is not a valid attribute for account type <BANK_ACCOUNT>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='partner')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='partner')].error")
                        .value("Field <partner> is not a valid attribute for account type <BANK_ACCOUNT>"))
                .andReturn();
    }

    @Test
    public void testWalletAccountValidation_withInvalidFields_success() throws Exception {
        var fakeWalletAccount = FakeAccounts.getFakeAccounts(1, Account.Type.WALLET).get(0);
        var invalidFakeWalletAccount = fakeWalletAccount.toBuilder()
                .accountNumber("WALLET123")
                .iban("invalidIban")
                .beneficiaryName("invalidBeneficiaryName")
                .beneficiaryAddress("invalidBeneficiaryAddress")
                .swiftCode("invalidSwiftCode")
                .bankRoutingCode("invalidBankRoutingCode")
                .sortCode("invalidSortCode")
                .branchCode("invalidBranchCode")
                .rib("invalidRib")
                .phoneNumber("invalidPhone")
                .partner(null)
                .build();
        AccountApiRequestPayload payload = new AccountApiRequestPayload(invalidFakeWalletAccount);
        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorFields").isArray())
                .andExpect(jsonPath("$.errorFields[?(@.field=='accountRoutingCode')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='accountRoutingCode')].error").value("Field <accountRoutingCode> is not a valid attribute for account type <WALLET>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='beneficiaryName')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='beneficiaryName')].error").value("Field <beneficiaryName> is not a valid attribute for account type <WALLET>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='iban')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='iban')].error").value("Field <iban> is not a valid attribute for account type <WALLET>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='sortCode')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='sortCode')].error").value("Field <sortCode> is not a valid attribute for account type <WALLET>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='branchCode')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='branchCode')].error").value("Field <branchCode> is not a valid attribute for account type <WALLET>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='beneficiaryAddress')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='beneficiaryAddress')].error").value("Field <beneficiaryAddress> is not a valid attribute for account type <WALLET>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='swiftCode')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='swiftCode')].error").value("Field <swiftCode> is not a valid attribute for account type <WALLET>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='rib')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='rib')].error").value("Field <rib> is not a valid attribute for account type <WALLET>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='phoneNumber')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='phoneNumber')].error").value("Field <phoneNumber> is not a valid attribute for account type <WALLET>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='partner')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='partner')].error").value("Field <partner> is required attribute for account type <WALLET>"))
                .andReturn();
    }

    @Test
    public void testMobileMoneyAccountValidation_withInvalidFields_success() throws Exception {
        var fakeMMAccount = FakeAccounts.getFakeAccounts(1, Account.Type.MOBILE_MONEY).get(0);
        var invalidMMAccount = fakeMMAccount.toBuilder()
                .accountNumber("MM123")
                .iban("invalidIban")
                .beneficiaryName("invalidBeneficiaryName")
                .beneficiaryAddress("invalidBeneficiaryAddress")
                .swiftCode("invalidSwiftCode")
                .bankRoutingCode("invalidBankRoutingCode")
                .sortCode("invalidSortCode")
                .branchCode("invalidBranchCode")
                .rib("invalidRib")
                .phoneNumber(null)
                .partner(null)
                .build();
        AccountApiRequestPayload payload = new AccountApiRequestPayload(invalidMMAccount);
        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorFields").isArray())
                .andExpect(jsonPath("$.errorFields[?(@.field=='sortCode')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='sortCode')].error").value("Field <sortCode> is not a valid attribute for account type <MOBILE_MONEY>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='accountRoutingCode')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='accountRoutingCode')].error").value("Field <accountRoutingCode> is not a valid attribute for account type <MOBILE_MONEY>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='beneficiaryAddress')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='beneficiaryAddress')].error").value("Field <beneficiaryAddress> is not a valid attribute for account type <MOBILE_MONEY>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='swiftCode')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='swiftCode')].error").value("Field <swiftCode> is not a valid attribute for account type <MOBILE_MONEY>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='beneficiaryName')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='beneficiaryName')].error").value("Field <beneficiaryName> is not a valid attribute for account type <MOBILE_MONEY>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='iban')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='iban')].error").value("Field <iban> is not a valid attribute for account type <MOBILE_MONEY>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='rib')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='rib')].error").value("Field <rib> is not a valid attribute for account type <MOBILE_MONEY>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='branchCode')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='branchCode')].error").value("Field <branchCode> is not a valid attribute for account type <MOBILE_MONEY>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='phoneNumber')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='phoneNumber')].error").value("Field <phoneNumber> is required attribute for account type <MOBILE_MONEY>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='partner')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='partner')].error").value("Field <partner> is required attribute for account type <MOBILE_MONEY>"))
                .andReturn();
    }

    @Test
    public void testPSPAccountValidation_withInvalidFields_success() throws Exception {
        var fakePSPAccount = FakeAccounts.getFakeAccounts(1, Account.Type.PSP).get(0);
        var invalidPSPAccount = fakePSPAccount.toBuilder()
                .accountNumber("PSP123")
                .iban("invalidIban")
                .beneficiaryName("invalidBeneficiaryName")
                .beneficiaryAddress("invalidBeneficiaryAddress")
                .swiftCode("invalidSwiftCode")
                .bankRoutingCode("invalidBankRoutingCode")
                .sortCode("invalidSortCode")
                .branchCode("invalidBranchCode")
                .rib("invalidRib")
                .phoneNumber(null)
                .partner(null)
                .build();
        AccountApiRequestPayload payload = new AccountApiRequestPayload(invalidPSPAccount);
        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorFields").isArray())
                .andExpect(jsonPath("$.errorFields[?(@.field=='branchCode')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='branchCode')].error").value("Field <branchCode> is not a valid attribute for account type <PSP>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='swiftCode')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='swiftCode')].error").value("Field <swiftCode> is not a valid attribute for account type <PSP>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='rib')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='rib')].error").value("Field <rib> is not a valid attribute for account type <PSP>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='accountRoutingCode')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='accountRoutingCode')].error").value("Field <accountRoutingCode> is not a valid attribute for account type <PSP>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='beneficiaryAddress')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='beneficiaryAddress')].error").value("Field <beneficiaryAddress> is not a valid attribute for account type <PSP>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='beneficiaryName')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='beneficiaryName')].error").value("Field <beneficiaryName> is not a valid attribute for account type <PSP>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='sortCode')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='sortCode')].error").value("Field <sortCode> is not a valid attribute for account type <PSP>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='iban')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='iban')].error").value("Field <iban> is not a valid attribute for account type <PSP>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='partner')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='partner')].error").value("Field <partner> is required attribute for account type <PSP>"))
                .andReturn();
    }

    @Test
    public void getNetChangechange_success() throws Exception {
        List<AccountNetChangeResultDto> netChangeResultDtos = List.of(AccountNetChangeResultDto.builder()
                .partitionKey("1")
                .netChange(BigDecimal.ONE)
                .build());

        AccountNetChangeRequestPayload filter = AccountNetChangeRequestPayload.builder()
                .partitionKeys(Collections.emptyList())
                .fromBeginning(false)
                .build();

        MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
        queryParams.add("fromBeginning", "false");

        when(readAccountsUseCase.executeAccountNetChange(filter.getPartitionKeys(), filter.toEntity()))
                .thenReturn(netChangeResultDtos);

        mockMvc
                .perform(get("/api/accounts/net-change")
                        .queryParams(queryParams))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testInvestmentAccount_TermDeposits_Success() throws Exception {
        when(createAccountsUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        Account account = FakeAccounts.getFakeAccounts(1, Type.INVESTMENTS).get(0);
        account = account.toBuilder()
                .subType(SubType.TERM_DEPOSITS)
                .contractId("TD123456")
                .amountDeposited(BigDecimal.valueOf(10000))
                .maturityDate(LocalDate.now().plusMonths(6))
                .interest(BigDecimal.valueOf(5.25))
                .build();

        AccountApiRequestPayload payload = new AccountApiRequestPayload(account);

        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvestmentAccount_TLF_Success() throws Exception {
        when(createAccountsUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        Account account = FakeAccounts.getFakeAccounts(1, Type.INVESTMENTS).get(0);
        account = account.toBuilder()
                .subType(SubType.TLF)
                .contractId("TLF789012")
                .amountDeposited(BigDecimal.valueOf(25000))
                .maturityDate(LocalDate.now().plusYears(1))
                .interest(BigDecimal.valueOf(3.75))
                .build();

        AccountApiRequestPayload payload = new AccountApiRequestPayload(account);

        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvestmentAccount_BankGuarantees_Success() throws Exception {
        when(createAccountsUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        Account account = FakeAccounts.getFakeAccounts(1, Type.INVESTMENTS).get(0);
        account = account.toBuilder()
                .subType(SubType.BANK_GUARANTEES)
                .contractId("BG345678")
                .amountDeposited(BigDecimal.valueOf(50000))
                .maturityDate(LocalDate.now().plusYears(2))
                .interest(BigDecimal.valueOf(4.5))
                .build();

        AccountApiRequestPayload payload = new AccountApiRequestPayload(account);

        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvestmentAccount_Bonds_Success() throws Exception {
        when(createAccountsUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        Account account = FakeAccounts.getFakeAccounts(1, Type.INVESTMENTS).get(0);
        account = account.toBuilder()
                .subType(SubType.BONDS)
                .isin("US123456AB12")
                .amountDeposited(BigDecimal.valueOf(100000))
                .maturityDate(LocalDate.now().plusYears(5))
                .nominalAmount(BigDecimal.valueOf(100000))
                .couponPaymentPeriodicity(CouponPaymentPeriodicity.SEMI_ANNUAL)
                .couponRate(BigDecimal.valueOf(3.25))
                .build();

        AccountApiRequestPayload payload = new AccountApiRequestPayload(account);

        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvestmentAccount_MissingRequiredFields_Failure() throws Exception {
        Account account = FakeAccounts.getFakeAccounts(1, Type.INVESTMENTS).get(0);
        account = account.toBuilder()
                .accountNumber(null)
                .subType(SubType.BONDS)
                .isin(null)
                .amountDeposited(null)
                .maturityDate(null)
                .nominalAmount(null)
                .couponPaymentPeriodicity(null)
                .couponRate(null)
                .build();

        AccountApiRequestPayload payload = new AccountApiRequestPayload(account);

        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorFields").isArray())
                .andExpect(jsonPath("$.errorFields[?(@.field=='isin')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='isin')].error")
                        .value("Field <isin> is required attribute for account type <INVESTMENTS> with subType <BONDS>")) // Subtype specific
                .andExpect(jsonPath("$.errorFields[?(@.field=='amountDeposited')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='amountDeposited')].error")
                        .value("Field <amountDeposited> is required attribute for account type <INVESTMENTS> with subType <BONDS>")) // Subtype specific
                .andExpect(jsonPath("$.errorFields[?(@.field=='maturityDate')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='maturityDate')].error")
                        .value("Field <maturityDate> is required attribute for account type <INVESTMENTS> with subType <BONDS>")) // Subtype specific
                .andExpect(jsonPath("$.errorFields[?(@.field=='nominalAmount')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='nominalAmount')].error")
                        .value("Field <nominalAmount> is required attribute for account type <INVESTMENTS> with subType <BONDS>")) // Subtype specific
                .andExpect(jsonPath("$.errorFields[?(@.field=='couponPaymentPeriodicity')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='couponPaymentPeriodicity')].error")
                        .value("Field <couponPaymentPeriodicity> is required attribute for account type <INVESTMENTS> with subType <BONDS>")) // Subtype specific
                .andExpect(jsonPath("$.errorFields[?(@.field=='couponRate')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='couponRate')].error")
                        .value("Field <couponRate> is required attribute for account type <INVESTMENTS> with subType <BONDS>")) // Subtype specific
                .andReturn();
    }

    @Test
    public void testInvestmentAccount_WrongSubtype_Failure() throws Exception {
        Account account = FakeAccounts.getFakeAccounts(1, Type.INVESTMENTS).get(0);
        account = account.toBuilder()
                .accountNumber("ACC123")
                .subType(SubType.BONDS)
                .contractId("TD123456")
                .amountDeposited(BigDecimal.valueOf(10000))
                .maturityDate(LocalDate.now().plusMonths(6))
                .interest(BigDecimal.valueOf(5.25))
                .build();

        AccountApiRequestPayload payload = new AccountApiRequestPayload(account);

        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorFields").isArray())
                .andExpect(jsonPath("$.errorFields[?(@.field=='isin')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='isin')].error")
                        .value("Field <isin> is required attribute for account type <INVESTMENTS> with subType <BONDS>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='nominalAmount')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='nominalAmount')].error")
                        .value("Field <nominalAmount> is required attribute for account type <INVESTMENTS> with subType <BONDS>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='couponPaymentPeriodicity')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='couponPaymentPeriodicity')].error")
                        .value("Field <couponPaymentPeriodicity> is required attribute for account type <INVESTMENTS> with subType <BONDS>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='couponRate')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='couponRate')].error")
                        .value("Field <couponRate> is required attribute for account type <INVESTMENTS> with subType <BONDS>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='contractId')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='contractId')].error")
                        .value("Field <contractId> is not a valid attribute for account type <INVESTMENTS> with subType <BONDS>"))
                .andExpect(jsonPath("$.errorFields[?(@.field=='interest')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='interest')].error")
                        .value("Field <interest> is not a valid attribute for account type <INVESTMENTS> with subType <BONDS>"))
                .andReturn();
    }

    @Test
    public void testInvestmentAccount_MissingSubType_Failure() throws Exception {
        Account account = FakeAccounts.getFakeAccounts(1, Type.INVESTMENTS).get(0);
        account = account.toBuilder()
                .accountNumber(null)
                .subType(null)
                .contractId("TD123456")
                .amountDeposited(BigDecimal.valueOf(10000))
                .maturityDate(LocalDate.now().plusMonths(6))
                .interest(BigDecimal.valueOf(5.25))
                .build();

        AccountApiRequestPayload payload = new AccountApiRequestPayload(account);

        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorFields").isArray())
                .andExpect(jsonPath("$.errorFields[?(@.field=='subType')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='subType')].error")
                        .value("Field <subType> is required attribute for account type <INVESTMENTS> with subType <null>"))
                .andReturn();
    }

    @Test
    public void testInvestmentAccount_TermDeposit_WithBondField_Failure() throws Exception {
        Account account = FakeAccounts.getFakeAccounts(1, Type.INVESTMENTS).get(0);
        account = account.toBuilder()
                .accountNumber("ACC123")
                .subType(SubType.TERM_DEPOSITS)
                .contractId("TD123456")
                .amountDeposited(BigDecimal.valueOf(10000))
                .maturityDate(LocalDate.now().plusMonths(6))
                .interest(BigDecimal.valueOf(5.25))
                .isin("US123456AB12")
                .build();

        AccountApiRequestPayload payload = new AccountApiRequestPayload(account);

        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorFields").isArray())
                .andExpect(jsonPath("$.errorFields[?(@.field=='isin')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='isin')].error")
                        .value("Field <isin> is not a valid attribute for account type <INVESTMENTS> with subType <TERM_DEPOSITS>"))
                .andReturn();
    }

    @Test
    public void testInvestmentAccount_Bonds_WithTermDepositField_Failure() throws Exception {
        Account account = FakeAccounts.getFakeAccounts(1, Type.INVESTMENTS).get(0);
        account = account.toBuilder()
                .accountNumber("ACC456")
                .subType(SubType.BONDS)
                .isin("US123456AB12")
                .amountDeposited(BigDecimal.valueOf(100000))
                .maturityDate(LocalDate.now().plusYears(5))
                .nominalAmount(BigDecimal.valueOf(100000))
                .couponPaymentPeriodicity(CouponPaymentPeriodicity.SEMI_ANNUAL)
                .couponRate(BigDecimal.valueOf(3.25))
                .contractId("TD123456")
                .build();

        AccountApiRequestPayload payload = new AccountApiRequestPayload(account);

        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorFields").isArray())
                .andExpect(jsonPath("$.errorFields[?(@.field=='contractId')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='contractId')].error")
                        .value("Field <contractId> is not a valid attribute for account type <INVESTMENTS> with subType <BONDS>"))
                .andReturn();
    }

    @Test
    public void testInvestmentAccount_WithNonInvestmentField_Failure() throws Exception {
        Account account = FakeAccounts.getFakeAccounts(1, Type.INVESTMENTS).get(0);
        account = account.toBuilder()
                .accountNumber("ACC789")
                .subType(SubType.TERM_DEPOSITS)
                .contractId("TD123456")
                .amountDeposited(BigDecimal.valueOf(10000))
                .maturityDate(LocalDate.now().plusMonths(6))
                .interest(BigDecimal.valueOf(5.25))
                .iban("DE89 3704 0044 0532 0130 00")
                .build();

        AccountApiRequestPayload payload = new AccountApiRequestPayload(account);

        mockMvc
                .perform(post("/api/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorFields").isArray())
                .andExpect(jsonPath("$.errorFields[?(@.field=='iban')]").exists())
                .andExpect(jsonPath("$.errorFields[?(@.field=='iban')].error")
                        .value("Field <iban> is not a valid attribute for account type <INVESTMENTS> with subType <TERM_DEPOSITS>"))
                .andReturn();
    }

    @Test
    public void getAccounts_withNegativeBalanceParameter_shouldBindCorrectly() throws Exception {
        when(readAccountsUseCase.execute(any(AccountFilters.class), any(AccountSortFilters.class), any(PageFilters.class)))
                .thenAnswer(invocation -> {
                    AccountFilters filter = invocation.getArgument(0);
                    return List.of();
                });

        when(readAccountsUseCase.executeCount(any(AccountFilters.class)))
                .thenReturn(0);

        mockMvc
                .perform(get("/api/accounts").param("isNegativeBalance", "true"))
                .andExpect(status().isOk());

        mockMvc
                .perform(get("/api/accounts").param("isNegativeBalance", "false"))
                .andExpect(status().isOk());
    }

    @Test
    public void getAccounts_withNegativeBalanceFilter_shouldPassCorrectFiltersToUseCase() throws Exception {
        when(readAccountsUseCase.execute(any(AccountFilters.class), any(AccountSortFilters.class), any(PageFilters.class)))
                .thenReturn(Collections.emptyList());
        when(readAccountsUseCase.executeCount(any(AccountFilters.class)))
                .thenReturn(0);

        mockMvc
                .perform(get("/api/accounts")
                        .param("isNegativeBalance", "true"))
                .andExpect(status().isOk());

        verify(readAccountsUseCase).execute(
                argThat(filter -> filter.isNegativeBalance() == true),
                any(AccountSortFilters.class),
                any(PageFilters.class)
        );

        mockMvc
                .perform(get("/api/accounts")
                        .param("isNegativeBalance", "false"))
                .andExpect(status().isOk());

        verify(readAccountsUseCase).execute(
                argThat(filter -> filter.isNegativeBalance() == false),
                any(AccountSortFilters.class),
                any(PageFilters.class)
        );
    }

    @Test
    public void getAccounts_withoutNegativeBalanceParameter_shouldDefaultToFalse() throws Exception {
        when(readAccountsUseCase.execute(any(AccountFilters.class), any(AccountSortFilters.class), any(PageFilters.class)))
                .thenReturn(Collections.emptyList());
        when(readAccountsUseCase.executeCount(any(AccountFilters.class)))
                .thenReturn(0);

        // WHEN: Request accounts without isNegativeBalance parameter
        mockMvc
                .perform(get("/api/accounts"))
                .andExpect(status().isOk());

        // THEN: Verify the use case was called with isNegativeBalance=false (default)
        verify(readAccountsUseCase).execute(
                argThat(filter -> filter.isNegativeBalance() == false),
                any(AccountSortFilters.class),
                any(PageFilters.class)
        );
    }

    @Test
    public void getAccounts_withNegativeBalanceFilter_shouldReturnReturnAccountsWithNegativeBalance() throws Exception {
        List<Account> allAccounts = FakeAccounts.getFakeAccounts(4, null);

        Account negativeBalanceAccount1 = allAccounts.get(0).toBuilder()
                .id(1L)
                .accountNumber("KE-PETTY-001")
                .accountName("Kenya Petty Cash Account")
                .beneficiaryName("Kenya Operations Ltd")
                .build();

        Account negativeBalanceAccount2 = allAccounts.get(1).toBuilder()
                .id(2L)
                .accountNumber("NG-OVERDRAFT-002")
                .accountName("Nigeria Overdraft Facility")
                .beneficiaryName("Nigeria Finance Dept")
                .build();

        Account positiveBalanceAccount1 = allAccounts.get(2).toBuilder()
                .id(3L)
                .accountNumber("EG-MAIN-003")
                .accountName("Egypt Main Operating Account")
                .beneficiaryName("Egypt Operations Ltd")
                .build();

        Account positiveBalanceAccount2 = allAccounts.get(3).toBuilder()
                .id(4L)
                .accountNumber("MA-RESERVE-004")
                .accountName("Morocco Reserve Fund")
                .beneficiaryName("Morocco Treasury")
                .build();

        List<Account> allTestAccounts = List.of(
                negativeBalanceAccount1,
                negativeBalanceAccount2,
                positiveBalanceAccount1,
                positiveBalanceAccount2
        );

        List<Account> negativeBalanceAccounts = List.of(
                negativeBalanceAccount1,
                negativeBalanceAccount2
        );

        when(readAccountsUseCase.execute(any(AccountFilters.class), any(AccountSortFilters.class), any(PageFilters.class)))
                .thenAnswer(invocation -> {
                    AccountFilters filter = invocation.getArgument(0);
                    return filter.isNegativeBalance() ? negativeBalanceAccounts : allTestAccounts;
                });

        when(readAccountsUseCase.executeCount(any(AccountFilters.class)))
                .thenAnswer(invocation -> {
                    AccountFilters filter = invocation.getArgument(0);
                    return filter.isNegativeBalance() ? negativeBalanceAccounts.size() : allTestAccounts.size();
                });

        mockMvc
                .perform(get("/api/accounts"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.results", hasSize(4)))
                .andExpect(jsonPath("$.total").value(4))
                .andExpect(jsonPath("$.results[0].accountNumber").value("KE-PETTY-001"))
                .andExpect(jsonPath("$.results[1].accountNumber").value("NG-OVERDRAFT-002"))
                .andExpect(jsonPath("$.results[2].accountNumber").value("EG-MAIN-003"))
                .andExpect(jsonPath("$.results[3].accountNumber").value("MA-RESERVE-004"));

        var negativeFilterResult = mockMvc
                .perform(get("/api/accounts")
                        .param("isNegativeBalance", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.results", hasSize(2)))
                .andExpect(jsonPath("$.total").value(2))
                .andExpect(jsonPath("$.results[0].accountNumber").value("KE-PETTY-001"))
                .andExpect(jsonPath("$.results[0].accountName").value("Kenya Petty Cash Account"))
                .andExpect(jsonPath("$.results[0].beneficiaryName").value("Kenya Operations Ltd"))
                .andExpect(jsonPath("$.results[1].accountNumber").value("NG-OVERDRAFT-002"))
                .andExpect(jsonPath("$.results[1].accountName").value("Nigeria Overdraft Facility"))
                .andExpect(jsonPath("$.results[1].beneficiaryName").value("Nigeria Finance Dept"))
                .andReturn();

        mockMvc
                .perform(get("/api/accounts")
                        .param("isNegativeBalance", "false"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.results", hasSize(4)))
                .andExpect(jsonPath("$.total").value(4));

        String responseBody = negativeFilterResult.getResponse().getContentAsString();

        assertTrue(responseBody.contains("Kenya Petty Cash Account"),
                "Response should contain Kenya Petty Cash Account");
        assertTrue(responseBody.contains("Nigeria Overdraft Facility"),
                "Response should contain Nigeria Overdraft Facility");
        assertFalse(responseBody.contains("Egypt Main Operating Account"),
                "Response should NOT contain Egypt Main Operating Account when filtering negative balances");
        assertFalse(responseBody.contains("Morocco Reserve Fund"),
                "Response should NOT contain Morocco Reserve Fund when filtering negative balances");

        verify(readAccountsUseCase, times(3)).execute(
                any(AccountFilters.class),
                any(AccountSortFilters.class),
                any(PageFilters.class)
        );
    }

}

