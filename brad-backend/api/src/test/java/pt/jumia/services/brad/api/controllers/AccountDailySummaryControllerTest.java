package pt.jumia.services.brad.api.controllers;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.neovisionaries.i18n.CountryCode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.brad.api.payloads.request.accountdailysummary.RecalculateApiRequestPayload;
import pt.jumia.services.brad.domain.entities.dtos.GroupedAccountDailySummaryDto;
import pt.jumia.services.brad.domain.entities.dtos.StackedCashPositionDto;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;
import pt.jumia.services.brad.domain.usecases.accountdailysummary.CalculateAccountSummaryUseCase;
import pt.jumia.services.brad.domain.usecases.accountdailysummary.ReadAccountDailySummaryUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;

@ExtendWith(SpringExtension.class)
@WebMvcTest(AccountDailySummaryController.class)
class AccountDailySummaryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private ReadAccountDailySummaryUseCase readAccountDailySummaryUseCase;

    @MockBean
    private CalculateAccountSummaryUseCase calculateAccountSummaryUseCase;

    @MockBean
    private ReadCountriesUseCase readCountriesUseCase;


    @Test
    public void trigger_recalculate_success() throws Exception {

        doNothing().when(calculateAccountSummaryUseCase).execute(any(), any());
        mockMvc
            .perform(patch("/api/accounts-summary/recalculate"))
            .andExpect(status().isAccepted())
            .andReturn();
        verify(calculateAccountSummaryUseCase).execute(null, null);


    }

    @Test
    public void recalculate_withPayload_success() throws Exception {

        RecalculateApiRequestPayload payload = RecalculateApiRequestPayload.builder()
            .accountNavReference("accountNavReference")
            .startDate(LocalDate.now())
            .build();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        doNothing().when(calculateAccountSummaryUseCase).execute(any(), any());
        mockMvc
            .perform(patch("/api/accounts-summary/recalculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(payload)))
            .andExpect(status().isAccepted())
            .andReturn();
        verify(calculateAccountSummaryUseCase).execute(payload.getAccountNavReference(), payload.getStartDate());

    }


    @Test
    public void fetchCashEvolution_whenCashEvolutionFound_thenReturnSuccess() throws Exception {

        when(validateUserAccessUseCase.getCountriesCanViewAccountsOrThrow(any())).thenReturn(java.util.List.of(CountryCode.EG));
        when(readAccountDailySummaryUseCase.executeCashEvolution(any())).thenReturn(List.of(getDummyGroupedAccountDailySummaryDto()));
        when(readCountriesUseCase.executeCountries()).thenReturn(List.of(FakeCountries.EGYPT));

        mockMvc
            .perform(get("/api/accounts-summary/cash-evolution"))
            .andExpect(status().isOk())
            .andReturn();

    }

    @Test
    public void fetchCashEvolution_whenWithFilters_thenReturnSuccess() throws Exception {

        when(validateUserAccessUseCase.getCountriesCanViewAccountsOrThrow(any())).thenReturn(java.util.List.of(CountryCode.EG));
        when(readAccountDailySummaryUseCase.executeCashEvolution(any())).thenReturn(List.of(getDummyGroupedAccountDailySummaryDto()));
        when(readCountriesUseCase.executeCountries()).thenReturn(List.of(FakeCountries.EGYPT));

        mockMvc
            .perform(get("/api/accounts-summary/cash-evolution?groupBy=ACCOUNT&isAggregatedByPeriod=false"))
            .andExpect(status().isOk())
            .andReturn();

    }


    @Test
    public void fetchCashEvolution_whenPermissionMissing_thenReturn403() throws Exception {

        when(validateUserAccessUseCase.getCountriesCanViewAccountsOrThrow(any())).thenReturn(Collections.emptyList());
        when(readCountriesUseCase.executeCountries()).thenReturn(Collections.emptyList());

        mockMvc
            .perform(get("/api/accounts-summary/cash-evolution"))
            .andExpect(status().isForbidden())
            .andReturn();

    }

    @Test
    public void fetchCashPosition_whenCashPositionFound_thenReturnSuccess() throws Exception {

        when(validateUserAccessUseCase.getCountriesCanViewAccountsOrThrow(any())).thenReturn(java.util.List.of(CountryCode.EG));
        when(readAccountDailySummaryUseCase.executeCashPosition(any())).thenReturn(List.of(getDummyGroupedAccountDailySummaryDto()));
        when(readCountriesUseCase.executeCountries()).thenReturn(List.of(FakeCountries.EGYPT));

        mockMvc
                .perform(get("/api/accounts-summary/cash-position"))
                .andExpect(status().isOk())
                .andReturn();

    }
    @Test
    public void fetchCashPositionStacked_whenStackedCashPositionFound_thenReturnSuccess() throws Exception {
        when(validateUserAccessUseCase.getCountriesCanViewAccountsOrThrow(any())).thenReturn(List.of(CountryCode.EG));
        when(readAccountDailySummaryUseCase.executeCashPositionStacked(any())).thenReturn(List.of(getDummyStackedCashPositionDto()));
        when(readCountriesUseCase.executeCountries()).thenReturn(List.of(FakeCountries.EGYPT));

        mockMvc
            .perform(get("/api/accounts-summary/cash-position-stacked"))
            .andExpect(status().isOk())
            .andReturn();
    }

    @Test
    public void fetchCashPositionStacked_whenWithFilters_thenReturnSuccess() throws Exception {
        when(validateUserAccessUseCase.getCountriesCanViewAccountsOrThrow(any())).thenReturn(List.of(CountryCode.EG));
        when(readAccountDailySummaryUseCase.executeCashPositionStacked(any())).thenReturn(List.of(getDummyStackedCashPositionDto()));
        when(readCountriesUseCase.executeCountries()).thenReturn(List.of(FakeCountries.EGYPT));

        mockMvc
            .perform(get("/api/accounts-summary/cash-position-stacked?groupBy=ACCOUNT&isAggregatedByPeriod=false"))
            .andExpect(status().isOk())
            .andReturn();
    }

    @Test
    public void fetchCashPositionStacked_whenPermissionMissing_thenReturn403() throws Exception {
        when(validateUserAccessUseCase.getCountriesCanViewAccountsOrThrow(any())).thenReturn(Collections.emptyList());
        when(readCountriesUseCase.executeCountries()).thenReturn(Collections.emptyList());

        mockMvc
            .perform(get("/api/accounts-summary/cash-position-stacked"))
            .andExpect(status().isForbidden())
            .andReturn();
    }

    private StackedCashPositionDto getDummyStackedCashPositionDto() {
        return StackedCashPositionDto.builder()
            .parentGroupLabel("parentGroupLabel")
            .stack(List.of(GroupedAccountDailySummaryDto.builder()
                .groupLabel("groupLabel")
                .finalBalanceUsd(BigDecimal.TEN)
                .build()))
            .build();
    }

    private GroupedAccountDailySummaryDto getDummyGroupedAccountDailySummaryDto() {

        return GroupedAccountDailySummaryDto.builder()
            .groupLabel("groupLabel")
            .creditAmountUsd(BigDecimal.TEN)
            .debitAmountUsd(BigDecimal.TEN)
            .finalBalanceUsd(BigDecimal.TEN)
            .initialBalanceUsd(BigDecimal.TEN)
            .build();
    }

}