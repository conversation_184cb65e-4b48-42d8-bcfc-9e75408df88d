package pt.jumia.services.brad.api.controllers;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.domain.entities.Jobs;
import pt.jumia.services.brad.domain.entities.fake.FakeJobs;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.jobs.ReadJobsUseCase;
import pt.jumia.services.brad.domain.usecases.jobs.RunJobsUsecase;
import pt.jumia.services.brad.domain.usecases.jobs.UpdateJobUseCase;
import pt.jumia.services.brad.domain.utils.ResourceLoader;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
@WebMvcTest(JobController.class)
public class JobControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private ReadJobsUseCase readJobsUseCase;

    @MockBean
    private UpdateJobUseCase updateJobUseCase;

    @MockBean
    private RunJobsUsecase runJobsUsecase;

    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);
        doNothing().when(validateUserAccessUseCase).checkCanManageScheduler(requestUser);
    }


    @Test
    public void testGetAllJob() throws Exception {
        List<Jobs> jobsList = FakeJobs.ALL;

        when(readJobsUseCase.execute()).thenReturn(jobsList);

        mockMvc
                .perform(get("/api/jobs"))
                .andExpect(status().isOk())
                .andReturn();

    }
    @Test
    public void testGetJob() throws Exception {
        List<Jobs> jobsList = FakeJobs.ALL;

        when(readJobsUseCase.execute(jobsList.get(0).getJobName())).thenReturn(jobsList.get(0));

        mockMvc
                .perform(get("/api/jobs/"+jobsList.get(0).getJobName()))
                .andExpect(status().isOk())
                .andReturn();

    }

    @Test
    public void testUpdateJob() throws Exception {
        List<Jobs> jobsList = FakeJobs.ALL;

        when(updateJobUseCase.execute(jobsList.get(0).getJobName(), jobsList.get(0))).thenReturn(jobsList.get(0));
        String payload = ResourceLoader.getStringFromFile("jobs/valid_job.json");

        mockMvc
                .perform(put("/api/jobs/"+jobsList.get(0).getJobName())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testUpdateJob_Error() throws Exception {
        List<Jobs> jobsList = FakeJobs.ALL;


        when(updateJobUseCase.execute(any(),any())).thenThrow(new SchedulerException(""));
        String payload = ResourceLoader.getStringFromFile("jobs/valid_job.json");

        mockMvc
                .perform(put("/api/jobs/"+jobsList.get(0).getJobName())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isBadRequest())
                .andReturn();
    }

    @Test
    public void testRunJob() throws Exception {
        List<Jobs> jobsList = FakeJobs.ALL;

        doNothing().when(runJobsUsecase).execute(jobsList.get(0).getJobName());

        mockMvc
                .perform(post("/api/jobs/"+jobsList.get(0).getJobName()+"/force-run"))
                .andExpect(status().isOk())
                .andReturn();

    }

    @Test
    public void testPauseJob() throws Exception {

        List<Jobs> jobsList = FakeJobs.ALL;

        doNothing().when(updateJobUseCase).toggleState(jobsList.get(0).getJobName());

        mockMvc
            .perform(post("/api/jobs/" + jobsList.get(0).getJobName() + "/toggle-state"))
            .andExpect(status().isOk())
            .andReturn();
    }

    @Test
    public void testPauseJob_Error() throws Exception {

        List<Jobs> jobsList = FakeJobs.ALL;

        doNothing().when(validateUserAccessUseCase).checkCanAccess(any());
        doNothing().when(validateUserAccessUseCase).checkCanManageScheduler(any());
        org.mockito.Mockito.doThrow(new SchedulerException("")).when(updateJobUseCase).toggleState(any());

        mockMvc
            .perform(post("/api/jobs/" + jobsList.get(0).getJobName() + "/toggle-state"))
            .andExpect(status().isBadRequest())
            .andReturn();
    }
}
