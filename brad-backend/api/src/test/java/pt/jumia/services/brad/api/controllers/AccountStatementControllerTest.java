package pt.jumia.services.brad.api.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.api.payloads.request.accountstatement.AccountStatementWithTransactionApiRequestPayload;
import pt.jumia.services.brad.api.payloads.request.accountstatement.UpdateStatementRequestPayload;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.fake.FakeTransaction;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.usecases.accountstatement.UpdateAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.apilog.CreateApiLogUseCase;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.CreateAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.DiscardAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.ReadAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.RetryAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.finrecstatement.ReadFinrecStatementUseCase;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@ExtendWith(SpringExtension.class)
@WebMvcTest(AccountStatementController.class)
public class AccountStatementControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private CreateAccountStatementUseCase createAccountStatementUseCase;

    @MockBean
    private ReadAccountStatementUseCase readAccountStatementUseCase;

    @MockBean
    private RetryAccountStatementUseCase retryAccountStatementUseCase;

    @MockBean
    private DiscardAccountStatementUseCase discardAccountStatementUseCase;

    @MockBean
    private ReadFinrecStatementUseCase readFinrecStatementUseCase;

    @MockBean
    private CreateApiLogUseCase apiLogUseCase;

    @MockBean
    private ReadAccountsUseCase readAccountsUseCase;

    @MockBean
    private UpdateAccountStatementUseCase updateAccountStatementUseCase;


    @Test
    public void createAccountStatement_success() throws Exception {
        doNothing().when(validateUserAccessUseCase).checkCanAccessStatements(new RequestUser(), CountryCode.NG);

        when(createAccountStatementUseCase.execute(any(), any(), any())).thenReturn(FakeAccountStatements.FAKE_ACCOUNT_STATEMENT);
        AccountStatement accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT;
        List<Transaction> transactionList = FakeTransaction.getFakeCreditTransactions(10, accountStatement);

        AccountStatementWithTransactionApiRequestPayload payload =
                new AccountStatementWithTransactionApiRequestPayload(accountStatement, transactionList);
        when(readAccountsUseCase.execute(payload.getAccountStatement().getAccountID())).thenReturn(accountStatement.getAccount());
        mockMvc
                .perform(post("/api/account-statements")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(payload)))
                .andExpect(status().isCreated())
                .andReturn();

    }

    @Test
    public void createAccountStatement_notFound() throws Exception {
       doNothing().when(validateUserAccessUseCase).checkCanAccessStatements(new RequestUser(), CountryCode.NG);

       when(createAccountStatementUseCase.execute(any(), any(), any())).thenThrow(new NotFoundException(""));

        AccountStatement accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT;
        List<Transaction> transactionList = FakeTransaction.getFakeCreditTransactions(10, accountStatement);

        AccountStatementWithTransactionApiRequestPayload payload =
                new AccountStatementWithTransactionApiRequestPayload(accountStatement, transactionList);

        when(readAccountsUseCase.execute(payload.getAccountStatement().getAccountID())).thenReturn(accountStatement.getAccount());

        mockMvc
               .perform(post("/api/account-statements")
                       .contentType(MediaType.APPLICATION_JSON)
                       .content(new ObjectMapper().writeValueAsString(payload)))
               .andExpect(status().isNotFound())
               .andReturn();
   }

    @Test
    public void createAccountStatement_EmptyTransactions() throws Exception {
       doNothing().when(validateUserAccessUseCase).checkCanAccessStatements(new RequestUser(), CountryCode.NG);

       when(createAccountStatementUseCase.execute(any(), any(), any())).thenThrow(new EntityErrorsException(""));

        AccountStatement accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT;
        List<Transaction> transactionList = new ArrayList<>();

        AccountStatementWithTransactionApiRequestPayload payload =
                new AccountStatementWithTransactionApiRequestPayload(accountStatement, transactionList);
        when(readAccountsUseCase.execute(payload.getAccountStatement().getAccountID())).thenReturn(accountStatement.getAccount());

        mockMvc
               .perform(post("/api/account-statements")
                       .contentType(MediaType.APPLICATION_JSON)
                       .content(new ObjectMapper().writeValueAsString(payload)))
               .andExpect(status().isBadRequest())
               .andReturn();
   }

    @Test
    public void testFetch_success() throws Exception {

        Account account = FakeAccounts.getFakeAccounts(1, null).get(0);
        List<AccountStatement> accountStatements = FakeAccountStatements.getFakeAccountStatements(20, account);
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountStatementSortFilters accountStatementSortFilters = new AccountStatementSortFilters(AccountStatement.SortingFields.ID, OrderDirection.DESC);

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder().accountID(1L).build();
        when(readAccountsUseCase.execute(Math.toIntExact(accountStatementFilters.getAccountID()))).thenReturn(account);
        when(readAccountStatementUseCase.execute(accountStatementFilters, accountStatementSortFilters, pageFilters))
                .thenReturn(accountStatements);



        mockMvc
                .perform(get("/api/account-statements?accountID=1"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetch_emptyList_success() throws Exception {

        Account account = FakeAccounts.getFakeAccounts(1, null).get(0);
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountStatementSortFilters accountStatementSortFilters = new AccountStatementSortFilters(AccountStatement.SortingFields.ID, OrderDirection.DESC);

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder().accountID(1L).build();
        when(readAccountsUseCase.execute(Math.toIntExact(accountStatementFilters.getAccountID()))).thenReturn(account);
        when(readAccountStatementUseCase.execute(accountStatementFilters, accountStatementSortFilters, pageFilters))
                .thenReturn(Collections.emptyList());

        mockMvc
                .perform(get("/api/account-statements?accountID=1"))
                .andExpect(status().isOk())
                .andReturn();
    }


    @Test
    public void testFetchStatusTypes_success() throws Exception {

        List<String> statusTypes = new ArrayList<>();

        when(readAccountStatementUseCase.executeStatusTypes()).thenReturn(statusTypes);

        mockMvc
                .perform(get("/api/account-statements/status-types"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetchErrorTypes_success() throws Exception {

        List<String> errorTypes = new ArrayList<>();

        when(readAccountStatementUseCase.executeErrorTypes()).thenReturn(errorTypes);

        mockMvc
                .perform(get("/api/account-statements/error-types"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testFetchDirections_success() throws Exception {

        List<String> directions = new ArrayList<>();

        when(readAccountStatementUseCase.executeDirections()).thenReturn(directions);

        mockMvc
                .perform(get("/api/account-statements/directions"))
                .andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testRetryStatement_success() throws Exception {
        doNothing().when(validateUserAccessUseCase).checkCanAccessStatements(new RequestUser(), CountryCode.NG);
        AccountStatement accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT;

        when(readAccountStatementUseCase.execute(accountStatement.getId())).thenReturn(accountStatement);
        when(readAccountsUseCase.execute(Math.toIntExact(accountStatement.getAccount().getId()))).thenReturn(accountStatement.getAccount());
        mockMvc
                .perform(get("/api/account-statements/retry?statementId=1"))
                .andExpect(status().isOk())
                .andReturn();

        verify(retryAccountStatementUseCase, times(1)).execute(1L);
    }

    @Test
    public void testDiscardStatement_success() throws Exception {
        doNothing().when(validateUserAccessUseCase).checkCanAccessStatements(new RequestUser(), CountryCode.NG);
        AccountStatement accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT;
        when(readAccountStatementUseCase.execute(accountStatement.getId())).thenReturn(accountStatement);
        when(readAccountsUseCase.execute(Math.toIntExact(accountStatement.getAccount().getId()))).thenReturn(accountStatement.getAccount());
        mockMvc
                .perform(delete("/api/account-statements/discard/1"))
                .andExpect(status().isOk())
                .andReturn();

        verify(discardAccountStatementUseCase, times(1)).execute(1L);
    }

    @Test
    public void testDiscardLastImportedStatement_success() throws Exception {
        doNothing().when(validateUserAccessUseCase).checkCanDiscardImportedStatements(new RequestUser(), CountryCode.NG);
        AccountStatement accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT;
        when(readAccountStatementUseCase.execute(accountStatement.getId())).thenReturn(accountStatement);
        when(readAccountsUseCase.execute(Math.toIntExact(accountStatement.getAccount().getId()))).thenReturn(accountStatement.getAccount());
        mockMvc
                .perform(delete("/api/account-statements/discard/last-imported/1"))
                .andExpect(status().isOk())
                .andReturn();

        verify(discardAccountStatementUseCase, times(1)).executeDiscardLastImportedStatement(1L);
    }

    @Test
    void updateStatement_Success() throws Exception {
        UpdateStatementRequestPayload payload = new UpdateStatementRequestPayload();
        payload.setStatementId(9L);
        payload.setNewStatus("REVIEW");
        payload.setNewDescription("ERROR_PREVIOUS_STATEMENT");
        payload.setJiraTicket("AFRFINIT-7817");

        AccountStatement mockStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT;
        Mockito.when(readAccountStatementUseCase.execute(9L)).thenReturn(mockStatement);
        doNothing().when(validateUserAccessUseCase).checkCanAccessStatements(any(), any());

        ObjectMapper objectMapper = new ObjectMapper();
        mockMvc.perform(post("/api/account-statements/update-statement")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isOk())
                .andExpect(org.springframework.test.web.servlet.result.MockMvcResultMatchers
                        .content().json("{\"status\":\"SUCCESS\",\"request\":{\"statementId\":9,\"newStatus\":\"REVIEW\",\"newDescription\":\"ERROR_PREVIOUS_STATEMENT\",\"jiraTicket\":\"AFRFINIT-7817\"}}"));
    }

    @Test
    void updateStatement_ValidationError() throws Exception {
        UpdateStatementRequestPayload payload = new UpdateStatementRequestPayload();
        payload.setStatementId(null);

        ObjectMapper objectMapper = new ObjectMapper();
        mockMvc.perform(post("/api/account-statements/update-statement")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void updateStatement_NotFound() throws Exception {
        UpdateStatementRequestPayload payload = new UpdateStatementRequestPayload();
        payload.setStatementId(9L);
        payload.setNewStatus("REVIEW");
        payload.setNewDescription("ERROR_PREVIOUS_STATEMENT");
        payload.setJiraTicket("AFRFINIT-7817");

        Mockito.when(readAccountStatementUseCase.execute(9L)).thenThrow(new NotFoundException("Statement not found"));

        ObjectMapper objectMapper = new ObjectMapper();
        mockMvc.perform(post("/api/account-statements/update-statement")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isNotFound());
    }
}
