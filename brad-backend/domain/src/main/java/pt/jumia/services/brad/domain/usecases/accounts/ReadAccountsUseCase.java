package pt.jumia.services.brad.domain.usecases.accounts;

import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.AccountStatement.SortingFields;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.dtos.AccountNetChangeResultDto;
import pt.jumia.services.brad.domain.entities.dtos.AccountTroubleshootingDto;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountNetChangeFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountSortFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementSortFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.AccountRepository;
import pt.jumia.services.brad.domain.usecases.accountstatement.ReadAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.transactions.ReadTransactionUseCase;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ReadAccountsUseCase {

    public static final LocalDate MIN_DATE = LocalDate.of(1980, 1, 1);
    public static final LocalDate MAX_DATE = LocalDate.of(9000, 12, 31);

    private final AccountRepository accountRepository;


    private final ReadAccountStatementUseCase readAccountStatementUseCase;

    private final ReadTransactionUseCase readTransactionUseCase;

    public List<Account> execute(AccountFilters accountFilters, AccountSortFilters accountSortFilters,
                                 PageFilters pageFilters) throws EntityErrorsException {

        return accountRepository.findAll(accountFilters, accountSortFilters, pageFilters)
                .stream()
                .map(account -> {
                    Optional<AccountStatement> lastImportedStatement =
                            readAccountStatementUseCase.executeLastImportedStatement(account.getId());

                    boolean hasError = readAccountStatementUseCase.executeLastStatement(account.getId())
                        .map(statement -> statement.getStatus() != AccountStatementStatus.IMPORTED)
                        .orElse(false);

                    if (lastImportedStatement.isPresent()) {

                        AccountStatement accountStatement = lastImportedStatement.get();

                        account = account.toBuilder()
                                .balanceUSD(getAccountBalanceUsd(accountStatement))
                                .balance(accountStatement.getFinalDirection() == Direction.CREDIT ?
                                        accountStatement.getFinalAmount() :
                                        accountStatement.getFinalAmount().negate())
                                .lastStatementDate(accountStatement.getFinalDate())
                                .hasError(hasError)
                                .build();
                    }

                    return account;

                })
                .collect(Collectors.toList());
    }

    public Account execute(int id) throws NotFoundException {
        Optional<Account> optionalAccount = accountRepository.findById(id);

        if (optionalAccount.isEmpty()) {
            throw NotFoundException.createNotFound(Account.class, id);
        }

        return optionalAccount.get();
    }

    public Account execute(String accountNumber) throws NotFoundException {
        Optional<Account> optionalAccount = accountRepository.findByAccountNumber(accountNumber);

        return optionalAccount.orElseThrow(() -> new NotFoundException("Account with account number " + accountNumber + " not found"));
    }

    public Account executeByNavReference(String navReference) throws NotFoundException {
        Optional<Account> optionalAccount = accountRepository.findAccountNavReference(navReference);

        return optionalAccount.orElseThrow(() -> NotFoundException.createNotFound(Account.class, navReference));
    }

    public Map<String, Account> executeByNavReferences(List<String> navReferences) {
        if (navReferences == null || navReferences.isEmpty()) {
            return Map.of();
        }
        
        return accountRepository.findByNavReferences(navReferences);
    }

    public Optional<Account> executeByNavReferenceAndCompanyID(String navReference, String companyID) {
        return accountRepository.findAccountNavReferenceAndCompanyID(navReference, companyID);
    }

    public Integer executeCount(AccountFilters filters) {
        return accountRepository.count(filters);
    }

    public Integer countTroubleShooting(AccountFilters filters) {

        return accountRepository.countTroubleShooting(filters);
    }

    public Account executeAdditionalInfo(int id) throws NotFoundException, EntityErrorsException {
        Optional<Account> optionalAccount = accountRepository.findById(id);

        if (optionalAccount.isEmpty()) {
            throw NotFoundException.createNotFound(Account.class, id);
        }

        Account account = optionalAccount.get();

        Optional<AccountStatement> optionalAccountStatement =
                readAccountStatementUseCase.executeLastImportedStatement(account.getId());

        boolean hasError = readAccountStatementUseCase.executeLastStatement(account.getId())
                .map(statement -> statement.getStatus() != AccountStatementStatus.IMPORTED)
                .orElse(false);

        if (optionalAccountStatement.isEmpty()) {
            return account.toBuilder()
                    .hasError(hasError)
                    .build();
        }

        AccountStatement accountStatement = optionalAccountStatement.get();

        TransactionFilters transactionFilters = TransactionFilters.builder()
                .accountStatementID(String.valueOf(accountStatement.getId()))
                .build();

        Transaction lastTransaction = readTransactionUseCase.executeLastTransactionDate(transactionFilters);

        final LocalDate nullTransactionDate = null;

        BigDecimal accountStatementFinalAmountUsd = getAccountBalanceUsd(accountStatement);

        BigDecimal accountStatementFinalAmountLcy = accountStatement.getCurrency().getCode()
                .equals(account.getCountry().getCurrency().getCode()) ? accountStatement.getFinalAmount() :
                accountStatement.getAmountInLocalCurrency(accountStatement.getFinalAmount());

        if (accountStatementFinalAmountLcy != null) {
            accountStatementFinalAmountLcy = accountStatement.getFinalDirection() == Direction.CREDIT ? accountStatementFinalAmountLcy :
                    accountStatementFinalAmountLcy.negate();
        }

        return account.toBuilder()
                .balance(accountStatement.getFinalDirection() == Direction.CREDIT ? accountStatement.getFinalAmount() :
                        accountStatement.getFinalAmount().negate())
                .balanceUSD(accountStatementFinalAmountUsd)
                .balanceLocalCurrency(accountStatementFinalAmountLcy)
                .localCurrency(accountStatement.getAccount().getCountry().getCurrency())
                .lastStatementDate(accountStatement.getFinalDate())
                .lastTransactionDate(Objects.isNull(lastTransaction) ? nullTransactionDate : lastTransaction.getTransactionDate())
                .hasError(hasError)
                .build();
    }

    public Optional<Account> executeByUserLastUpdatedStatement(String username) {
        return readAccountStatementUseCase.executeFetchLastUpdatedStatement(username)
                .map(AccountStatement::getAccount);
    }

    public List<Account> executeAccountNavReferences(AccountFilters accountFilters) {
        return accountRepository.findAccountNavReferences(accountFilters);
    }

    private static @Nullable BigDecimal getAccountBalanceUsd(AccountStatement accountStatement) {

        BigDecimal accountStatementFinalAmountUsd = accountStatement.getCurrency().getCode().equals("USD") ?
                accountStatement.getFinalAmount() :
                accountStatement.getAmountInUsd(accountStatement.getFinalAmount());

        if (accountStatementFinalAmountUsd != null) {

            accountStatementFinalAmountUsd = accountStatement.getFinalDirection() == Direction.CREDIT ?
                    accountStatementFinalAmountUsd :
                    accountStatementFinalAmountUsd.negate();
        }

        return accountStatementFinalAmountUsd;
    }

    public List<AccountTroubleshootingDto> executeAllTroubleshootingAccounts(AccountFilters accountFilters,
        AccountSortFilters sortFilters, PageFilters pageFilters) throws EntityErrorsException {

        return accountRepository.findAllAccountsInTroubleShooting(accountFilters, sortFilters, pageFilters);
    }

    public List<AccountNetChangeResultDto> executeAccountNetChange(List<Long> partitionKeys, AccountNetChangeFilters netChangeFilters)
        throws EntityErrorsException {

        List<AccountNetChangeResultDto> netChangeResultDtos = new ArrayList<>();

        for (Long partitionKey : partitionKeys) {
            LocalDate startDate = Boolean.TRUE.equals(netChangeFilters.fromBeginning()) ? MIN_DATE : netChangeFilters.startDate();

            AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .accountID(partitionKey)
                .status(List.of(AccountStatementStatus.OPEN, AccountStatementStatus.IMPORTED, AccountStatementStatus.REVIEW))
                .initialDateStart(MIN_DATE)
                .initialDateEnd(netChangeFilters.endDate())
                .finalDateStart(startDate)
                .finalDateEnd(MAX_DATE)
                .build();

            AccountStatementSortFilters sortFilters = AccountStatementSortFilters.builder()
                .direction(OrderDirection.ASC)
                .field(SortingFields.INITIAL_DATE)
                .build();

            List<AccountStatement> accountStatements = readAccountStatementUseCase.execute(accountStatementFilters, sortFilters, null);
            accountStatements.sort(new StatementsComparator());

            if (Boolean.TRUE.equals(netChangeFilters.fromBeginning())) {
                if (accountStatements.isEmpty()) {
                    netChangeResultDtos.add(AccountNetChangeResultDto.builder()
                        .partitionKey(String.valueOf(partitionKey))
                        .netChange(BigDecimal.ZERO)
                        .build());
                } else {
                    AccountStatement lastStmt = accountStatements.get(accountStatements.size() - 1);

                    BigDecimal partialNetChangeInLastStmt = getPartialNetChangeInLastStmt(netChangeFilters, partitionKey, lastStmt);
                    BigDecimal netChange = lastStmt.getInitialAmount().add(partialNetChangeInLastStmt);
                    netChangeResultDtos.add(AccountNetChangeResultDto.builder()
                        .partitionKey(String.valueOf(partitionKey))
                        .netChange(netChange)
                        .build());
                }
            } else {
                switch (accountStatements.size()) {
                    case 0: {
                        netChangeResultDtos.add(AccountNetChangeResultDto.builder()
                            .partitionKey(String.valueOf(partitionKey))
                            .netChange(BigDecimal.ZERO)
                            .build());
                        break;
                    }
                    case 1: {

                        TransactionFilters transactionFilters = TransactionFilters.builder()
                            .accountStatementID(String.valueOf(accountStatements.get(0).getId()))
                            .transactionDateStart(startDate)
                            .transactionDateEnd(netChangeFilters.endDate())
                            .partitionKey(String.valueOf(partitionKey))
                            .build();

                        BigDecimal netChange = readTransactionUseCase.executeNetChange(transactionFilters);

                        netChangeResultDtos.add(AccountNetChangeResultDto.builder()
                            .partitionKey(String.valueOf(partitionKey))
                            .netChange(netChange)
                            .build());
                        break;
                    }
                    default: {
                        AccountStatement firstStmt = accountStatements.get(0);
                        AccountStatement lastStmt = accountStatements.get(accountStatements.size() - 1);

                        BigDecimal firstStmtFinalAmount = firstStmt.getFinalAmount();
                        BigDecimal lastStmtInitialAmount = lastStmt.getInitialAmount();

                        BigDecimal partialNetChangeInFirstStmt = getPartialNetChangeInFirstStmt(partitionKey, firstStmt, startDate);

                        BigDecimal partialNetChangeInLastStmt = getPartialNetChangeInLastStmt(netChangeFilters, partitionKey, lastStmt);

                        BigDecimal netChange = lastStmtInitialAmount.subtract(firstStmtFinalAmount).add(partialNetChangeInFirstStmt)
                            .add(partialNetChangeInLastStmt);

                        netChangeResultDtos.add(AccountNetChangeResultDto.builder()
                            .partitionKey(String.valueOf(partitionKey))
                            .netChange(netChange)
                            .build());
                        break;
                    }
                }
            }
        }
        return netChangeResultDtos;
    }

    private BigDecimal getPartialNetChangeInLastStmt(final AccountNetChangeFilters netChangeFilters, final Long partitionKey,
        final AccountStatement lastStmt) throws EntityErrorsException {

        TransactionFilters filters = TransactionFilters.builder()
            .accountStatementID(String.valueOf(lastStmt.getId()))
            .transactionDateStart(lastStmt.getInitialDate())
            .transactionDateEnd(netChangeFilters.endDate())
            .partitionKey(String.valueOf(partitionKey))
            .build();

        return readTransactionUseCase.executeNetChange(filters);
    }

    private BigDecimal getPartialNetChangeInFirstStmt(final Long partitionKey, final AccountStatement firstStmt,
        final LocalDate startDate)
        throws EntityErrorsException {

        TransactionFilters filters = TransactionFilters.builder()
            .accountStatementID(String.valueOf(firstStmt.getId()))
            .transactionDateStart(startDate)
            .transactionDateEnd(firstStmt.getFinalDate())
            .partitionKey(String.valueOf(partitionKey))
            .build();

        return readTransactionUseCase.executeNetChange(filters);
    }


    public static class StatementsComparator implements Comparator<AccountStatement> {

        @Override
        public int compare(AccountStatement stmt1, AccountStatement stmt2) {

            int initialDateComparison = stmt1.getInitialDate().compareTo(stmt2.getInitialDate());
            if (initialDateComparison != 0) {
                return initialDateComparison;
            }
            int createdAtComparison = stmt1.getCreatedAt().compareTo(stmt2.getCreatedAt());
            return createdAtComparison * -1;
        }

    }
}
