package pt.jumia.services.brad.domain.repository.brad;

import org.quartz.SchedulerException;
import pt.jumia.services.brad.domain.entities.Jobs;

import java.util.List;

public interface JobsRepository {

    List<Jobs> findAll() throws SchedulerException;

    Jobs findJobByName(String jobName) throws SchedulerException;

    Jobs update(String jobName, Jobs job) throws SchedulerException;

    void forceRun(String jobName) throws SchedulerException;

    void toggleState(String jobName) throws SchedulerException;

    Jobs getEntityWithJob(String jobName) throws SchedulerException;

}
