package pt.jumia.services.brad.domain.usecases.processconvertfiles.abstractmxadapter;

import com.prowidesoftware.swift.model.mx.AbstractMX;
import com.prowidesoftware.swift.model.mx.MxCamt05300101;
import com.prowidesoftware.swift.model.mx.dic.BalanceType9Code;
import com.prowidesoftware.swift.model.mx.dic.CashBalance2;
import com.prowidesoftware.swift.model.mx.dic.StatementEntry1;
import pt.jumia.services.brad.domain.enumerations.Direction;

import java.math.BigDecimal;
import java.util.List;


public class MxCamt05300101File implements AbstractMXFile {

    private MxCamt05300101 mxCamt05300101;
    private List<StatementEntry1> statementEntryList;
    private final int initialBalanceIndex;
    private final int finalBalanceIndex;
    int index = -1;

    public MxCamt05300101File(AbstractMX abstractMX) {
        if (abstractMX instanceof MxCamt05300101) {
            mxCamt05300101 = new MxCamt05300101(abstractMX.message());
        } else {
            throw new IllegalArgumentException("wrong file format");
        }
        statementEntryList = mxCamt05300101.getBkToCstmrStmtV01().getStmt().get(0).getNtry();
        initialBalanceIndex = getInitialBalanceIndex();
        finalBalanceIndex = getFinalBalanceIndex();
    }

    @Override
    public String getCurrency() {
        return mxCamt05300101.getBkToCstmrStmtV01().getStmt().get(0).getAcct().getCcy();
    }

    @Override
    public String getStatementId() {
        return mxCamt05300101.getBkToCstmrStmtV01().getStmt().get(0).getId();
    }

    @Override
    public String getInitialDate() {
        return mxCamt05300101.getBkToCstmrStmtV01().getStmt().get(0).getBal().get(initialBalanceIndex).getDt().getDt().toString();
    }

    @Override
    public Direction getInitialDirection() {
        return Direction.getTranslatedDirectionForMXFile(mxCamt05300101.getBkToCstmrStmtV01().getStmt().get(0)
                .getBal().get(initialBalanceIndex).getCdtDbtInd());
    }

    @Override
    public BigDecimal getInitialAmount() {
        return mxCamt05300101.getBkToCstmrStmtV01().getStmt().get(0).getBal().get(initialBalanceIndex).getAmt().getValue();
    }

    @Override
    public String getFinalDate() {
        return mxCamt05300101.getBkToCstmrStmtV01().getStmt().get(0).getBal().get(finalBalanceIndex).getDt().getDt().toString();
    }

    @Override
    public Direction getFinalDirection() {
        return Direction.getTranslatedDirectionForMXFile(mxCamt05300101.getBkToCstmrStmtV01().getStmt().get(0).getBal()
                .get(finalBalanceIndex).getCdtDbtInd());
    }

    @Override
    public BigDecimal getFinalAmount() {
        return mxCamt05300101.getBkToCstmrStmtV01().getStmt().get(0).getBal().get(finalBalanceIndex).getAmt().getValue();
    }

    @Override
    public String getNtryValueDate() {
        return statementEntryList.get(index).getValDt().getDt().toString();
    }

    @Override
    public String getNtryStatementDate() {
        return statementEntryList.get(index).getBookgDt().getDt().toString();
    }

    @Override
    public String getNtryTransactionDate() {
        return statementEntryList.get(index).getValDt().getDt().toString();
    }

    @Override
    public Direction getNtryDirection() {
        return Direction.getTranslatedDirectionForMXFile(statementEntryList.get(index).getCdtDbtInd());
    }

    @Override
    public BigDecimal getNtryAmount() {
        return statementEntryList.get(index).getAmt().getValue();
    }

    @Override
    public String getNtryRefrence() {
        return statementEntryList.get(index).getAcctSvcrRef();
    }

    @Override
    public String getNtryDescription() {
        return statementEntryList.get(index).getBkTxCd().getPrtry().getCd();
    }

    @Override
    public String getNtryRemittanceInformation() {
        return null;
    }

    @Override
    public String getOrderingPartyName() {
        return null;
    }

    @Override
    public String getAccountNumber() {
        return mxCamt05300101.getBkToCstmrStmtV01().getStmt().get(0).getAcct().getId().getPrtryAcct().getId();
    }

    @Override
    public boolean hasNextNtry() {
        return ++index < statementEntryList.size();
    }


    private int getInitialBalanceIndex() {
        List<CashBalance2> cashBalance2s = mxCamt05300101.getBkToCstmrStmtV01().getStmt().get(0).getBal();
        for (int index = 0; index < cashBalance2s.size(); index++) {
            if (cashBalance2s.get(index).getTp().getCd() == BalanceType9Code.OPBD) {
                return index;
            }
        }
        throw new IllegalArgumentException("No initial balance index found");
    }


    private int getFinalBalanceIndex() {
        List<CashBalance2> cashBalance2s = mxCamt05300101.getBkToCstmrStmtV01().getStmt().get(0).getBal();
        for (int index = 0; index < cashBalance2s.size(); index++) {
            if (cashBalance2s.get(index).getTp().getCd() == BalanceType9Code.CLBD) {
                return index;
            }
        }
        throw new IllegalArgumentException("No final balance index found");
    }


}
