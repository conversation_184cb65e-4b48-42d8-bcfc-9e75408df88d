package pt.jumia.services.brad.domain.entities.error;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import pt.jumia.services.brad.domain.entities.Bale;

@Getter
@Builder
@AllArgsConstructor
@ToString
public class BaleProcessingErrorResult {
    
    private static final String UNKNOWN_ACCOUNT = "UNKNOWN";
    
    private final Bale bale;
    private final Throwable error;
    private final BaleErrorClassifier.ErrorCategory category;
    private final String operationContext;
    private final String errorDescription;
    private final String navReference;
    private final Integer entryNo;

    public static BaleProcessingErrorResult fromException(Bale bale, Throwable error, String operationContext) {
        BaleErrorClassifier.ErrorCategory category = BaleErrorClassifier.categorizeError(error);
        String errorDescription = BaleErrorClassifier.createErrorContext(error, operationContext);
        
        return BaleProcessingErrorResult.builder()
                .bale(bale)
                .error(error)
                .category(category)
                .operationContext(operationContext)
                .errorDescription(errorDescription)
                .navReference(bale != null && bale.getAccount() != null ?
                    bale.getAccount().getNavReference() : UNKNOWN_ACCOUNT)
                .entryNo(bale != null ? bale.getEntryNo() : null)
                .build();
    }

    public static BaleProcessingErrorResult accountNotFound(Bale bale, String navReference) {
        String errorDescription = String.format(
            "Account with NAV Reference '%s' not found in BRAD",
            navReference
        );
        
        return BaleProcessingErrorResult.builder()
                .bale(bale)
                .error(new RuntimeException(errorDescription))
                .category(BaleErrorClassifier.ErrorCategory.RECOVERABLE)
                .operationContext("Account Lookup")
                .errorDescription(errorDescription)
                .navReference(bale != null && bale.getAccount() != null ?
                    bale.getAccount().getNavReference() : UNKNOWN_ACCOUNT)
                .entryNo(bale != null ? bale.getEntryNo() : null)
                .build();
    }

    public static BaleProcessingErrorResult currencyNotFound(Bale bale, String currencyCode) {
        String errorDescription = String.format(
            "Currency '%s' not found for bale with NAV Reference '%s'",
            currencyCode,
            bale != null && bale.getAccount() != null ? bale.getAccount().getNavReference() : UNKNOWN_ACCOUNT
        );
        
        return BaleProcessingErrorResult.builder()
                .bale(bale)
                .error(new RuntimeException(errorDescription))
                .category(BaleErrorClassifier.ErrorCategory.RECOVERABLE)
                .operationContext("Currency Lookup")
                .errorDescription(errorDescription)
                .navReference(bale != null && bale.getAccount() != null ?
                    bale.getAccount().getNavReference() : UNKNOWN_ACCOUNT)
                .entryNo(bale != null ? bale.getEntryNo() : null)
                .build();
    }

    public boolean isRecoverable() {
        return category == BaleErrorClassifier.ErrorCategory.RECOVERABLE;
    }

    public boolean isCritical() {
        return category == BaleErrorClassifier.ErrorCategory.CRITICAL;
    }

    public String getNavReference() {
        return navReference;
    }
    
    // Deprecated: Use getNavReference() instead
    @Deprecated
    public String getAccountNumber() {
        return navReference;
    }

    public String getLogMessage() {
        return String.format(
            "[%s] %s - Entry No: %s, NAV Reference: %s, Error: %s",
            category.name(),
            operationContext,
            entryNo,
            navReference,
            errorDescription
        );
    }
}
