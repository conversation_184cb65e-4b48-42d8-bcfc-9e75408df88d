package pt.jumia.services.brad.domain.usecases.accountstatement;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementRepository;

@Component
@RequiredArgsConstructor
@Slf4j
public class UpdateAccountStatementUseCase {

    private final AccountStatementRepository accountStatementRepository;

    public AccountStatement execute(AccountStatement accountStatement) {

        try {
           return  accountStatementRepository.upsert(accountStatement);
        } catch (Exception e) {
            log.error("Error updating statement: {}", e.getMessage());
            throw e;
        }
    }

}
