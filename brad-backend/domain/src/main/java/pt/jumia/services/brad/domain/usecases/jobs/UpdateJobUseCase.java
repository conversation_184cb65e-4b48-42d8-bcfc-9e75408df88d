package pt.jumia.services.brad.domain.usecases.jobs;

import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Jobs;
import pt.jumia.services.brad.domain.repository.brad.JobsRepository;

@Component
public class UpdateJobUseCase {

    @Autowired
    private JobsRepository jobsRepository;

    public Jobs execute(String jobName, Jobs job) throws SchedulerException {
        return jobsRepository.update(jobName, job);
    }

    public void toggleState(String jobName) throws SchedulerException {

        jobsRepository.toggleState(jobName);
    }
}
