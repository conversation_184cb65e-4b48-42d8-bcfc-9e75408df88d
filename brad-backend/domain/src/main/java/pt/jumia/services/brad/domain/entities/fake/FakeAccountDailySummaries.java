package pt.jumia.services.brad.domain.entities.fake;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.AccountDailySummary;

public interface FakeAccountDailySummaries {

    AccountDailySummary FAKE_ACCOUNT_DAILY_SUMMARY = AccountDailySummary.builder()
        .transactionDate(LocalDate.now())
        .account(FakeAccounts.FAKE_ACCOUNT)
        .totalCreditAmount(BigDecimal.TEN)
        .totalDebitAmount(BigDecimal.TEN)
        .totalCreditAmountUsd(BigDecimal.TEN)
        .totalDebitAmountUsd(BigDecimal.TEN)
        .netAmount(BigDecimal.TEN)
        .netAmountUsd(BigDecimal.TEN)
        .initialBalance(BigDecimal.ZERO)
        .finalBalance(BigDecimal.TEN)
        .initialBalanceUsd(BigDecimal.ZERO)
        .finalBalanceUsd(BigDecimal.TEN)
        .transactionsCount(2)
        .debitTransactionsCount(1)
        .creditTransactionsCount(1)
        .currency(FakeCurrencies.NGN)
        .fxRate(FakeFxRates.BASE_FX_RATE)
        .build();

    static List<AccountDailySummary> getFakeAccountSummaries(int amount) {

        List<Account> fakeAccounts = FakeAccounts.getFakeAccounts(amount, null);
        List<AccountDailySummary> fakeSummaries = new ArrayList<>();
        for (int i = 0; i < amount; i++) {
            AccountStatement statement = FakeAccountStatements.getFakeAccountStatements(1, fakeAccounts.get(i)).get(0);

            fakeSummaries.add(FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
                .transactionDate(LocalDate.now().minusDays(i))
                .account(fakeAccounts.get(i))
                .netAmount(statement.getFinalAmount().subtract(statement.getInitialAmount()))
                .netAmountUsd(statement.getFinalAmount().subtract(statement.getInitialAmount()))
                .initialBalance(statement.getInitialAmount())
                .finalBalance(statement.getFinalAmount())
                .initialBalanceUsd(statement.getAmountInUsd(statement.getInitialAmount()))
                .finalBalanceUsd(statement.getAmountInUsd(statement.getInitialAmount()))
                .currency(fakeAccounts.get(i).getCurrency())
                .build());
        }

        return fakeSummaries;
    }

}
