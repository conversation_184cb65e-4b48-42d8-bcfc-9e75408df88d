package pt.jumia.services.brad.domain.usecases.transactions;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.TransactionRepository;
import pt.jumia.services.brad.domain.usecases.accountstatement.RevalidateAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.fxrates.brad.ReadBradFxRateUseCase;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@RequiredArgsConstructor
public class CreateTransactionUseCase {

    private final TransactionRepository transactionRepository;
    private final ReadCurrenciesUseCase readCurrenciesUseCase;
    private final ReadBradFxRateUseCase readBradFxRateUseCase;

    private static final String USD = "USD";
    private final RevalidateAccountStatementUseCase revalidateAccountStatementUseCase;

    @Async
    public void execute(List<Transaction> transactions, String username, AccountStatement accountStatement)
        throws NotFoundException, EntityErrorsException, InterruptedException, DatabaseErrorsException {

        log.info("Creating transactions for account statement {}", accountStatement.getId());

        if (Objects.isNull(accountStatement.getAccount().getId())) {
            log.error("Creating transactions in account statement {}. Account not have an ID", accountStatement.getId());
            throw EntityErrorsException.createNullClassError(AccountStatement.class);
        }

        List<CompletableFuture<Transaction>> futures = transactions.stream()
            .map(transaction -> CompletableFuture.supplyAsync(() -> {
                try {
                    log.info("Creating transaction for account statement {}", accountStatement.getId());
                    if (Objects.isNull(transaction)) {
                        log.error("Creating transaction for account statement {}, Transactions is null", accountStatement.getId());
                        throw EntityErrorsException.createNullClassError(Transaction.class);
                    }

                    if (!accountStatement.getStatus().equals(AccountStatementStatus.OPEN)) {
                        log.error("Creating transaction for account statement {}, Statement is open", accountStatement.getId());
                        throw new IllegalArgumentException("Statement is not open");
                    }

                    if (transaction.getAmount().compareTo(BigDecimal.ZERO) < 0) {
                        log.error("Creating transaction for account statement {}. Transaction amount is lower than 0", accountStatement.getId());
                        throw new IllegalArgumentException("Transaction amount cannot be negative");
                    }

                    log.info("Creating transaction for account statement {}. Preparing it.", accountStatement.getId());
                    Transaction transactionToCreate = transaction.toBuilder()
                            .accountStatement(accountStatement)
                            .currency(readCurrenciesUseCase.execute(transaction.getCurrency().getCode()))
                            .reconcileStatus(ReconcileStatus.NOT_RECONCILED)
                            .build();

                    log.info("Creating transaction for account statement {}. Adding fxRates.", accountStatement.getId());
                    transactionToCreate = addFxRates(transactionToCreate);

                    log.info("Creating transaction for account statement {}. Saving transaction.", accountStatement.getId());
                    return transactionRepository.insert(transactionToCreate, username).join();

                } catch (Exception e) {
                    log.error("Error creating transaction {}. Error: {}", transaction, ExceptionUtils.getStackTrace(e));
                    return null;
                }
            })).toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        log.info("Transactions created for account statement with id {}", accountStatement.getId());
        revalidateAccountStatementUseCase.execute(accountStatement);
    }

    public Transaction addFxRates(Transaction transaction) {

        Set<FxRate> fxRates = new HashSet<>();

        String transactionCurrencyCode = transaction.getCurrency().getCode();

        this.readBradFxRateUseCase.addFxRate(fxRates, transactionCurrencyCode, USD, transaction.getValueDate());

        this.readBradFxRateUseCase.addFxRate(fxRates, transactionCurrencyCode, transaction.getAccountStatement().getAccount()
                .getCountry().getCurrency().getCode(), transaction.getValueDate());

        return transaction.toBuilder()
            .fxRates(fxRates)
            .build();
    }

}
