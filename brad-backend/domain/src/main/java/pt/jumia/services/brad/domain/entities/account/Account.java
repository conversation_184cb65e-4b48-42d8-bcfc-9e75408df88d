package pt.jumia.services.brad.domain.entities.account;

import lombok.*;
import org.jetbrains.annotations.Nullable;
import pt.aig.aigx.commons.exceptions.InvalidArgumentException;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;
import pt.jumia.services.brad.domain.enumerations.StatementSource;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * Business representation of Account
 */
@Value
@Builder(toBuilder = true)
@AllArgsConstructor
public class Account {

    public static final int HOURS_OUT_OF_SYC = 25;

    @Nullable
    Long id;

    String companyID;

    Country country;

    String partner;

    String navReference;

    String beneficiaryName;

    String beneficiaryAddress;

    String iban;

    String accountNumber;

    String accountName;

    String phoneNumber;

    String swiftCode;

    String bankRoutingCode;

    String sortCode;

    String branchCode;

    String rib;

    Type type;

    SubType subType;

    Status status;

    Currency currency;

    LocalDateTime createdAt;

    String createdBy;

    LocalDateTime updatedAt;

    String updatedBy;

    BigDecimal balance;

    BigDecimal balanceUSD;

    Currency localCurrency;

    BigDecimal balanceLocalCurrency;

    LocalDate lastStatementDate;

    LocalDate lastTransactionDate;

    LocalDate lastProcessedStatementDate;

    Boolean hasError;

    StatementSource statementSource;

    StatementPeriodicity statementPeriodicity;

    String isin;

    String contractId;

    BigDecimal amountDeposited;

    LocalDate maturityDate;

    BigDecimal nominalAmount;

    CouponPaymentPeriodicity couponPaymentPeriodicity;

    BigDecimal couponRate;

    BigDecimal interest;

    public Account withoutDbFields() {

        return this.toBuilder()
                .id(null)
                .country(country.withoutDbFields())
                .currency(currency.withoutDbFields())
                .updatedAt(null)
                .updatedBy(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

    public static Account.Status getStatus(final String accountStatus) {

        return Arrays.stream(Account.Status.values())
                .filter(s -> s.getValue().equalsIgnoreCase(accountStatus))
                .findFirst()
                .orElseThrow(() -> new InvalidArgumentException(String.format("Invalid account status: %s", accountStatus)));
    }

    @Getter
    public enum SelectFields implements BaseSelectFields {

        ID("id", "id"),
        COMPANY_ID("companyID", "companyID"),
        COUNTRY("country", "country"),
        NAV_REFERENCE("navReference", "navReference"),
        BENEFICIARY_NAME("beneficiaryName", "beneficiaryName"),
        BENEFICIARY_ADDRESS("beneficiaryAddress", "beneficiaryAddress"),
        IBAN("iban", "iban"),
        ACCOUNT_NUMBER("accountNumber", "accountNumber"),
        ACCOUNT_NAME("accountName", "accountName"),
        SWIFT_CODE("swiftCode", "swiftCode"),
        BANK_ROUTING_CODE("bankRoutingCode", "bankRoutingCode"),
        SORT_CODE("sortCode", "sortCode"),
        BRANCH_CODE("branchCode", "branchCode"),
        RIB("rib", "rib"),
        STATUS("status", "status"),
        CURRENCY("currency", "currency"),
        STATEMENT_SOURCE("statementSource", "statementSource"),
        STATEMENT_PERIODICITY("statementPeriodicity", "statementPeriodicity"),
        LAST_PROCESSED_STATEMENT_DATE("lastProcessedStatementDate", "lastProcessedStatementDate"),
        CREATED_AT("createdAt", "createdAt"),
        CREATED_BY("createdBy", "createdBy"),
        UPDATED_AT("updatedAt", "updatedAt"),
        UPDATED_BY("updatedBy", "updatedBy");

        public final String queryField;
        public final String selectCode;

        public final List<String> ignoredFields = List.of(
                "balance",
                "balanceUSD",
                "localCurrency",
                "balanceLocalCurrency",
                "lastStatementDate",
                "lastTransactionDate",
                "hasError"
        );


        SelectFields(String queryField, String selectCode) {
            this.queryField = queryField;
            this.selectCode = selectCode;
        }
    }

    public enum SortingFields {
        ID,
        COMPANY_ID,
        COUNTRY,
        NAV_REFERENCE,
        BENEFICIARY_NAME,
        BENEFICIARY_ADDRESS,
        IBAN,
        ACCOUNT_NUMBER,
        ACCOUNT_NAME,
        SWIFT_CODE,
        BANK_ROUTING_CODE,
        SORT_CODE,
        BRANCH_CODE,
        RIB,
        TYPE,
        SUB_TYPE,
        STATUS,
        STATEMENT_SOURCE,
        STATEMENT_PERIODICITY,
        LAST_PROCESSED_STATEMENT_DATE,
        CURRENCY,
        CREATED_AT,
        CREATED_BY,
        UPDATED_AT,
        UPDATED_BY

    }

    @Getter
    @RequiredArgsConstructor
    public enum Status {
        OPEN("OPEN"),
        CLOSED("CLOSED"),
        TO_BE_CLOSED("TO BE CLOSED"),
        DORMANT("DORMANT");

        private final String value;

        public static Status fromString(String value) {
            return Arrays.stream(Status.values())
                    .filter(s -> s.getValue().equalsIgnoreCase(value) || s.name().equalsIgnoreCase(value))
                    .findFirst()
                    .orElseThrow(() -> new InvalidArgumentException(String.format("Invalid account status: %s", value)));
        }
    }

    @Getter
    @RequiredArgsConstructor
    public enum TroubleShooting {
        STATEMENT_SYNC,
        STATEMENT_VALIDATION,
        MISSING_MANUAL_UPLOAD

    }

    @Getter
    @RequiredArgsConstructor
    public enum Type {
        BANK_ACCOUNT,
        PSP,
        MOBILE_MONEY,
        WALLET,
        INVESTMENTS
    }

    @Getter
    @RequiredArgsConstructor
    public enum SubType {
        TERM_DEPOSITS,
        TLF,
        BANK_GUARANTEES,
        BONDS
    }

    @Getter
    @RequiredArgsConstructor
    public enum StatementPeriodicity {
        DAILY(24),
        WEEKLY(168),
        BI_MONTHLY(360),
        MONTHLY(720),
        QUARTERLY(2160);

        private final Integer hours;
    }

    @Getter
    @RequiredArgsConstructor
    public enum CouponPaymentPeriodicity {
        SEMI_ANNUAL
    }
}

