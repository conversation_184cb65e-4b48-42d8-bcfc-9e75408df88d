package pt.jumia.services.brad.domain.usecases.acl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.domain.Permissions;
import pt.jumia.services.brad.domain.entities.User;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import com.neovisionaries.i18n.CountryCode;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ValidateUserAccessUseCase {

    private final GetAclUserUseCase getAclUserUseCase;


    public void checkCanAccess(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccess()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_CAN_ACCESS);
        }
    }

    public List<CountryCode> getCountriesCanViewAccountsOrThrow(RequestUser requestUser) throws UserForbiddenException {
        return this.getCountriesByPermissionOrThrow(requestUser, Permissions.BRAD_ACCESS_ACCOUNTS);
    }
    public List<CountryCode> getCountriesCanDownloadAccountsOrThrow(RequestUser requestUser) throws UserForbiddenException {
        return this.getCountriesByPermissionOrThrow(requestUser, Permissions.BRAD_DOWNLOAD_ACCOUNTS_CSV);
    }
    public List<CountryCode> getCountriesCanDownloadContactsOrThrow(RequestUser requestUser) throws UserForbiddenException {
        return this.getCountriesByPermissionOrThrow(requestUser, Permissions.BRAD_DOWNLOAD_CONTACTS_CSV);
    }
    public List<CountryCode> getCountriesCanDownloadUsersOrThrow(RequestUser requestUser) throws UserForbiddenException {
        return this.getCountriesByPermissionOrThrow(requestUser, Permissions.BRAD_DOWNLOAD_USERS_CSV);
    }
    public List<CountryCode> getCountriesCanAccessTroubleshootOrThrow(RequestUser requestUser) throws UserForbiddenException {
        return this.getCountriesByPermissionOrThrow(requestUser, Permissions.BRAD_ACCESS_TROUBLESHOOTING);
    }

    public void checkCanAccessAccounts(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_ACCESS_ACCOUNTS);
    }

    public void checkCanManageAccounts(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_MANAGE_ACCOUNTS);
    }

    public void checkCanDownloadAccountsCSV(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_DOWNLOAD_ACCOUNTS_CSV);
    }

    public void checkCanDeleteAccounts(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_DELETE_ACCOUNTS);
    }

    public void checkCanAccessCountries(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_ACCESS_COUNTRIES);
    }

    public void checkCanManageCountries(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageCountries()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_ADMIN_MANAGE_COUNTRIES);
        }
    }

    public void checkCanAccessStatements(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_ACCESS_STATEMENTS);
    }

    public void checkCanDiscardImportedStatements(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException{
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_DISCARD_IMPORTED_STATEMENTS);
    }

    public void checkCanAccessReconciliation(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessReconciliation()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_ACCESS_RECONCILIATION);
        }
    }

    public void checkCanManageReconciliation(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_MANAGE_RECONCILIATION);
    }

    public void checkCanAccessScheduler(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessScheduler()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_ACCESS_SCHEDULER);
        }
    }

    public void checkCanManageScheduler(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageScheduler()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_MANAGE_SCHEDULER);
        }
    }

    public void checkCanAccessApiLogs(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessApiLog()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_ACCESS_API_LOG);
        }
    }

    public void checkCanAccessCurrencies(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessCurrencies()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_ACCESS_CURRENCIES);
        }
    }

    public void checkCanManageCurrencies(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageCurrencies()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_ADMIN_MANAGE_CURRENCIES);
        }
    }

    public void checkCanUploadStatements(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_UPLOAD_STATEMENTS);
    }

    public void checkCanApproveReconciliations(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_APPROVE_RECONCILIATIONS);
    }

    public void checkCanUnmatchReconciliations(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_UNMATCH_RECONCILIATIONS);
    }

    public void checkCanExportReconciliations(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_EXPORT_RECONCILIATIONS);
    }

    public void checkCanExportStatements(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_EXPORT_STATEMENTS);
    }

    public void checkCanDiscardStatements(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_DISCARD_STATEMENTS);
    }

    public void checkCanAccessTroubleshooting(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_ACCESS_ACCOUNTS);
    }

    public void checkCanChangeStatements(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_CHANGE_STATEMENTS);
    }

    public void checkCanRetryStatement(RequestUser requestUser, CountryCode countryCode) throws UserForbiddenException {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.BRAD_RETRY_STATEMENT);
    }

    public void checkCanAccessFxRates(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessFxRates()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_ACCESS_FX_RATES);
        }
    }

    public void checkCanAccessExecutionLog(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessExecutionLog()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_ACCESS_EXECUTION_LOG);
        }
    }

    public void checkCanAccessExportLog(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessExportLog()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_ACCESS_EXPORT_LOG);
        }
    }

    public void checkCanManageViewEntities(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageViewEntities()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_MANAGE_VIEW_ENTITIES);
        }
    }

    public void checkCanManageThresholds(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageThresholds()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_MANAGE_THRESHOLDS);
        }
    }

    public void checkCanAccessStatementFiles(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessStatementFiles()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_STATEMENT_FILES_ACCESS);
        }
    }

    public void checkCanScanSftpFolder(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanScanSftpFolder()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_SCAN_SFTP_FOLDER);
        }
    }

    public void checkCanDownloadStatementFiles(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanDownloadStatementFiles()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.BRAD_DOWNLOAD_STATEMENT_FILE);
        }
    }

    public void checkCanAccessSettings(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessSettings()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.SETTING_ACCESS);
        }
    }

    public void checkCanEditSettings(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanEditSettings()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.SETTING_EDIT);
        }
    }

    private void checkUserHasPermissionByCountryOrThrow(RequestUser requestUser, CountryCode countryCode,
                                                        String permission) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.getCountriesPermissionsList().containsKey(countryCode) ||
                !aclUser.getCountriesPermissionsList().get(countryCode).contains(permission)) {
            throw UserForbiddenException.createDontHavePermission(aclUser.getUsername(), permission);
        }
    }

    private List<CountryCode> getCountriesByPermissionOrThrow(RequestUser requestUser, String permissions) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        List<CountryCode> countriesWithCanViewPermission = aclUser.getCountriesPermissionsList()
                .entrySet().stream()
                .filter(entry -> entry.getValue().contains(permissions))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (countriesWithCanViewPermission.size() == 0) {
            throw UserForbiddenException.createDontHavePermission(aclUser.getUsername(), permissions);
        }
        return countriesWithCanViewPermission;
    }
}
