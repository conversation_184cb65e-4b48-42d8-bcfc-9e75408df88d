package pt.jumia.services.brad.domain.usecases.accountstatement;

import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.Transaction.SortingFields;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionSortFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.AccountDailySummaryRepository;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementRepository;
import pt.jumia.services.brad.domain.repository.brad.TransactionRepository;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.UpdateAccountStatementFileUseCase;

@Slf4j
@Component
@AllArgsConstructor
public class DeleteStatementsAtomicallyHelper {

    private AccountStatementRepository accountStatementRepository;
    private TransactionRepository transactionRepository;
    private UpdateAccountStatementFileUseCase updateAccountStatementFileUseCase;
    private AccountDailySummaryRepository accountDailySummaryRepository;


    @Transactional
    public void deleteAtomically(final AccountStatement statement) throws EntityErrorsException {

        discardStatement(statement);
        deleteTransactions(statement);
    }

    private void discardStatement(AccountStatement accountStatement) {

        AccountStatement nextAccountStatement = accountStatementRepository.findByPreviousStatementId(accountStatement).orElse(null);

        if (nextAccountStatement != null) {
            nextAccountStatement = nextAccountStatement.toBuilder().previousStatement(accountStatement.getPreviousStatement()).build();
            accountStatementRepository.upsert(accountStatement.toBuilder()
                .previousStatement(null)
                .status(AccountStatementStatus.DISCARDED)
                .build()
            );
            accountStatementRepository.upsert(nextAccountStatement);
        } else {
            accountStatementRepository.upsert(accountStatement.toBuilder()
                .status(AccountStatementStatus.DISCARDED)
                .build()
            );
        }
    }

    private void deleteTransactions(AccountStatement statement) throws EntityErrorsException {

        LocalDate startDate = getTrxEdgeDate(statement.getId(), OrderDirection.ASC);
        LocalDate endDate = getTrxEdgeDate(statement.getId(), OrderDirection.DESC);

        try {
            accountDailySummaryRepository.deleteByDates(statement.getAccount().getId(), startDate, endDate);
            updateAccountStatementFileUseCase.detachFromStatement(statement.getId());
            transactionRepository.deleteByAccountStatementId(statement.getId());
            accountStatementRepository.deleteById(statement.getId());
        } catch (Exception e) {
            log.error("Error deleting account statement {}", statement.getId(), e);
        }
    }

    private LocalDate getTrxEdgeDate(Long statementId, OrderDirection direction)
        throws EntityErrorsException {

        TransactionFilters filters = TransactionFilters.builder()
            .accountStatementID(String.valueOf(statementId))
            .build();
        PageFilters pageFilters = PageFilters.builder()
            .page(1)
            .size(1)
            .build();
        TransactionSortFilters sortFilters = TransactionSortFilters.builder()
            .field(SortingFields.TRANSACTION_DATE)
            .direction(direction)
            .build();
        return transactionRepository.findAll(filters, sortFilters, pageFilters)
            .stream()
            .findFirst()
            .map(Transaction::getTransactionDate)
            .orElse(direction.equals(OrderDirection.ASC) ? LocalDate.now().minusYears(1000) : LocalDate.now().plusDays(1));
    }

}
