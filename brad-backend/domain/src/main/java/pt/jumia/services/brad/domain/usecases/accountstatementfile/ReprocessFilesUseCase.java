package pt.jumia.services.brad.domain.usecases.accountstatementfile;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.io.IOException;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class ReprocessFilesUseCase {

    private final ReadAccountStatementFileUseCase readAccountStatementFileUseCase;
    private final ConsumeAccountStatementFileUseCase consumeAccountStatementFileUseCase;

    @Async
    public void execute(AccountStatementFileFilters requestFilters)
            throws EntityErrorsException, IOException {

        int pageNumber = 1;
        int pageSize = 100;
        List<AccountStatementFile> files;
        do {
            AccountStatementFileFilters filters = requestFilters.toBuilder()
                    .processingStatus(AccountStatementFile.ProcessingStatus.NEW)
                    .build();
            PageFilters pageFilters = new PageFilters(pageNumber, pageSize);

            files = readAccountStatementFileUseCase.execute(filters, null, pageFilters);
            consumeAccountStatementFileUseCase.executeBatch(files);

        } while (!files.isEmpty() && files.size() >= pageSize);
    }

    @Async
    public void execute(Long accountStatementFileId, String nextStatementId) {

        try {
            consumeAccountStatementFileUseCase.execute(accountStatementFileId, nextStatementId);

            log.info("Reprocessing account statement file with id: {}", accountStatementFileId);
        } catch (IOException ioException) {
            log.error("Error while reprocessing account statement file with id: {} - {}", accountStatementFileId,
                ExceptionUtils.getStackTrace(ioException));
        }

    }

}
