package pt.jumia.services.brad.domain.usecases.acl;

import com.neovisionaries.i18n.CountryCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.domain.AccessController;
import pt.jumia.services.brad.domain.Permissions;
import pt.jumia.services.brad.domain.entities.User;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.properties.AclProperties;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Component
@Slf4j
public class GetAclUserUseCase {

    private static final String APPLICATION_KEY = "APPLICATION";
    private static final String COUNTRY_KEY = "COUNTRY";

    private final AccessController accessController;
    private final String appCode;

    @Autowired
    public GetAclUserUseCase(AccessController accessController,
                             AclProperties aclProperties) {
        this.accessController = accessController;
        this.appCode = aclProperties.getAppName();
    }

    public User execute(RequestUser requestUser) throws UserForbiddenException {
        return getUserOrThrow(requestUser);
    }

    private User getUserOrThrow(RequestUser requestUser) throws UserForbiddenException {
        try {
            log.info("Retrieving user: {}", requestUser.getUsername());
            return getUser(requestUser);
        } catch (AclErrorException e) {
            log.error("Error retrieving user: {}", ExceptionUtils.getStackTrace(e));
            throw UserForbiddenException.createFromAclErrorException(e);
        }
    }

    private User getUser(RequestUser requestUser) throws AclErrorException {
        Map<String, Map<String, List<String>>> permissionsInApp = accessController.getPermissions(requestUser);

        Map<CountryCode, List<String>> permissionsInCountries = getPermissionsInCountries(permissionsInApp);

        List<String> appPermissions = getAppPermissions(permissionsInApp);

        return User.builder()
                .username(requestUser.getUsername())
                .canAccess(appPermissions.contains(Permissions.BRAD_CAN_ACCESS))
                .canAccessAccounts(appPermissions.contains(Permissions.BRAD_ACCESS_ACCOUNTS))
                .canDownloadAccountsCsv(appPermissions.contains(Permissions.BRAD_DOWNLOAD_ACCOUNTS_CSV))
                .canManageAccounts(appPermissions.contains(Permissions.BRAD_MANAGE_ACCOUNTS))
                .canDeleteAccounts(appPermissions.contains(Permissions.BRAD_DELETE_ACCOUNTS))
                .canAccessCountries(appPermissions.contains(Permissions.BRAD_ACCESS_COUNTRIES))
                .canManageCountries(appPermissions.contains(Permissions.BRAD_ADMIN_MANAGE_COUNTRIES))
                .canAccessStatements(appPermissions.contains(Permissions.BRAD_ACCESS_STATEMENTS))
                .canAccessReconciliation(appPermissions.contains(Permissions.BRAD_ACCESS_RECONCILIATION))
                .canManageReconciliation(appPermissions.contains(Permissions.BRAD_MANAGE_RECONCILIATION))
                .canAccessScheduler(appPermissions.contains(Permissions.BRAD_ACCESS_SCHEDULER))
                .canManageScheduler(appPermissions.contains(Permissions.BRAD_MANAGE_SCHEDULER))
                .canAccessApiLog(appPermissions.contains(Permissions.BRAD_ACCESS_API_LOG))
                .canAccessCurrencies(appPermissions.contains(Permissions.BRAD_ACCESS_CURRENCIES))
                .canManageCurrencies(appPermissions.contains(Permissions.BRAD_ADMIN_MANAGE_CURRENCIES))
                .canUploadStatements(appPermissions.contains(Permissions.BRAD_UPLOAD_STATEMENTS))
                .canApproveReconciliations(appPermissions.contains(Permissions.BRAD_APPROVE_RECONCILIATIONS))
                .canUnmatchReconciliations(appPermissions.contains(Permissions.BRAD_UNMATCH_RECONCILIATIONS))
                .canExportReconciliations(appPermissions.contains(Permissions.BRAD_EXPORT_RECONCILIATIONS))
                .canExportStatements(appPermissions.contains(Permissions.BRAD_EXPORT_STATEMENTS))
                .canDiscardStatements(appPermissions.contains(Permissions.BRAD_DISCARD_STATEMENTS))
                .canRetryStatement(appPermissions.contains(Permissions.BRAD_RETRY_STATEMENT))
                .canAccessTroubleshooting(appPermissions.contains(Permissions.BRAD_ACCESS_TROUBLESHOOTING))
                .canAccessFxRates(appPermissions.contains(Permissions.BRAD_ACCESS_FX_RATES))
                .canAccessExecutionLog(appPermissions.contains(Permissions.BRAD_ACCESS_EXECUTION_LOG))
                .canAccessExportLog(appPermissions.contains(Permissions.BRAD_ACCESS_EXPORT_LOG))
                .canManageViewEntities(appPermissions.contains(Permissions.BRAD_MANAGE_VIEW_ENTITIES))
                .canManageThresholds(appPermissions.contains(Permissions.BRAD_MANAGE_THRESHOLDS))
                .canAccessStatementFiles(appPermissions.contains(Permissions.BRAD_STATEMENT_FILES_ACCESS))
                .canScanSftpFolder(appPermissions.contains(Permissions.BRAD_SCAN_SFTP_FOLDER))
                .canDownloadStatementFiles(appPermissions.contains(Permissions.BRAD_DOWNLOAD_STATEMENT_FILE))
                .canAccessSettings(appPermissions.contains(Permissions.SETTING_ACCESS))
                .canEditSettings(appPermissions.contains(Permissions.SETTING_EDIT))
                .countriesPermissionsList(permissionsInCountries)
                .build();
    }

    @NotNull
    private static Map<CountryCode, List<String>> getPermissionsInCountries(Map<String, Map<String, List<String>>> permissionsInApp) {
        Map<String, List<String>> permissionsInCountriesAsString = CollectionUtils.isEmpty(permissionsInApp)  ?
                Collections.emptyMap() : permissionsInApp.get(COUNTRY_KEY);
        Map<CountryCode, List<String>> permissionsInCountries = CollectionUtils.isEmpty(permissionsInCountriesAsString) ?
                Collections.emptyMap() :
                permissionsInCountriesAsString.entrySet().stream()
                        .filter(entry ->
                                EnumUtils.isValidEnum(CountryCode.class, entry.getKey()))
                        .collect(Collectors.toMap(e -> CountryCode.valueOf(e.getKey()), Map.Entry::getValue));
        return permissionsInCountries;
    }

    private List<String> getAppPermissions(Map<String, Map<String, List<String>>> permissionsInApp) {
        if (permissionsInApp != null && permissionsInApp.containsKey(APPLICATION_KEY)) {
            Map<String, List<String>> applications = permissionsInApp.get(APPLICATION_KEY);
            if (applications.containsKey(appCode)) {
                return applications.get(appCode);
            }
        }
        return List.of();
    }
}
