package pt.jumia.services.brad.domain.usecases.bale.brad;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.BaleAccountFetchResult;
import pt.jumia.services.brad.domain.entities.CurrencyUpdateResult;
import pt.jumia.services.brad.domain.entities.error.BaleProcessingErrorResult;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.UpdateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.fxrates.brad.ReadBradFxRateUseCase;

import pt.jumia.services.brad.domain.usecases.executionlogs.ReadExecutionLogsUseCase;
import pt.jumia.services.brad.domain.entities.error.BaleErrorClassifier;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Simplified Bale synchronization use case.
 *
 * This class now focuses on the core business logic of processing bales
 * without complex batch processing, which is handled by Spring Batch.
 */
@Slf4j
@Component
@AllArgsConstructor
public class SyncBradBaleUseCase {

    private final BradBaleRepository bradBaleRepository;
    private final ReadCurrenciesUseCase readCurrenciesUseCase;
    private final ReadAccountsUseCase readAccountsUseCase;
    private final UpdateExecutionLogsUseCase updateExecutionLogsUseCase;
    private final ReadBradFxRateUseCase readBradFxRateUseCase;
    private final ReadExecutionLogsUseCase readExecutionLogsUseCase;
    private static final String USD = "USD";

    public List<Bale> execute(List<Bale> baleList, ExecutionLog executionLog) throws DatabaseErrorsException {
        log.info("Bale sync: Execution log ID: {} - Processing {} bales", executionLog.getId(), baleList.size());

        List<BaleProcessingErrorResult> allErrors = new ArrayList<>();

        log.debug("Bale sync: Execution log ID {}: Fetching accounts for {} bales", executionLog.getId(), baleList.size());
        BaleAccountFetchResult accountResult = fetchAccountsWithPartialSuccess(baleList);

        allErrors.addAll(accountResult.getFailures());
        log.info("Bale sync: Execution log {}: Account fetch completed - {} successful, {} failed",
                executionLog.getId(), accountResult.getSuccessCount(), accountResult.getFailureCount());

        List<BaleProcessingErrorResult> criticalErrors = allErrors.stream()
                .filter(BaleProcessingErrorResult::isCritical)
                .collect(Collectors.toList());

        if (!criticalErrors.isEmpty()) {
            log.error("Bale sync: Execution log {}: Critical errors detected. Stopping processing.", executionLog.getId());
            List<ExecutionLog.SyncingError> syncingErrors = updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(allErrors);
            updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
            return List.of();
        }

        CurrencyUpdateResult currencyResult = updateCurrenciesWithPartialSuccess(accountResult.getSuccessfulMappings());

        allErrors.addAll(currencyResult.getFailures());
        log.info("Bale sync: Execution log {}: Currency update completed - {} successful, {} failed",
                executionLog.getId(), currencyResult.getSuccessCount(), currencyResult.getFailureCount());

        criticalErrors = allErrors.stream()
                .filter(BaleProcessingErrorResult::isCritical)
                .collect(Collectors.toList());

        if (!criticalErrors.isEmpty()) {
            log.error("Bale sync: Execution log {}: Critical errors detected during currency processing. Stopping.", executionLog.getId());
            List<ExecutionLog.SyncingError> syncingErrors = updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(allErrors);
            updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
            return List.of();
        }

        List<Bale> syncedBales = new ArrayList<>();
        List<Bale> successfullyProcessedBales = currencyResult.getSuccessfulUpdates();

        if (!successfullyProcessedBales.isEmpty()) {
            try {
                log.info("Bale sync: Execution log {}: Syncing {} successfully processed bales to repository",
                        executionLog.getId(), successfullyProcessedBales.size());
                syncedBales = bradBaleRepository.sync(successfullyProcessedBales);
                log.info("Bale sync: Execution log {}: Repository sync completed - {} bales synced",
                        executionLog.getId(), syncedBales.size());
            } catch (Exception e) {
                log.error("Bale sync: Execution log {}: Error during repository sync", executionLog.getId(), e);
                BaleProcessingErrorResult repositoryError = BaleProcessingErrorResult.fromException(null, e, "Repository Sync");
                allErrors.add(repositoryError);
                List<ExecutionLog.SyncingError> syncingErrors = updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(allErrors);
                updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
                return List.of();
            }
        }

        updateExecutionLogWithResults(executionLog, syncedBales.size(), allErrors);
        logProcessingSummary(executionLog.getId(), baleList.size(), syncedBales.size(), allErrors);

        return syncedBales;
    }


    public List<Bale> executeBatched(List<Bale> baleList, ExecutionLog executionLog) throws DatabaseErrorsException {
        log.info("Bale sync: Execution log ID: {} - Processing {} bales using optimized batch processing with batch size: {}",
                executionLog.getId(), baleList.size(), batchProcessingConfig.getBatch().getSize());
        
        List<Bale> allSyncedBales = new ArrayList<>();
        List<BaleProcessingErrorResult> allErrors = new ArrayList<>();
        int batchSize = batchProcessingConfig.getBatch().getSize();
        int totalBatches = (int) Math.ceil((double) baleList.size() / batchSize);
        int successfulBatches = 0;
        int consecutiveFailures = 0;
        
        for (int i = 0; i < totalBatches; i++) {
            int startIndex = i * batchSize;
            int endIndex = Math.min(startIndex + batchSize, baleList.size());
            List<Bale> batch = baleList.subList(startIndex, endIndex);
            
            log.info("Bale sync: Processing batch {}/{} with {} bales (total errors so far: {})",
                    i + 1, totalBatches, batch.size(), allErrors.size());
            
            try {
                List<Bale> batchResult = processBatchOptimized(batch, executionLog, i + 1);
                if (!batchResult.isEmpty()) {
                    allSyncedBales.addAll(batchResult);
                    successfulBatches++;
                    consecutiveFailures = 0;
                } else {
                    consecutiveFailures++;
                }
                
                // Check if we should stop due to consecutive failures
                if (consecutiveFailures >= batchProcessingConfig.getErrorHandling().getConsecutiveFailureLimit()) {
                    log.error("Bale sync: Stopping batch processing due to {} consecutive batch failures", consecutiveFailures);
                    break;
                }
                
                // Memory monitoring
                if (isMemoryThresholdExceeded()) {
                    log.warn("Bale sync: Memory threshold exceeded, stopping batch processing at batch {}/{}", i + 1, totalBatches);
                    break;
                }
                
            } catch (Exception e) {
                log.error("Bale sync: Critical error in batch {}/{}: {}", i + 1, totalBatches, e.getMessage());
                BaleProcessingErrorResult batchError = BaleProcessingErrorResult.fromException(null, e, "Batch Processing");
                allErrors.add(batchError);
                consecutiveFailures++;
                
                if (consecutiveFailures >= batchProcessingConfig.getErrorHandling().getConsecutiveFailureLimit()) {
                    log.error("Bale sync: Stopping batch processing due to critical error and consecutive failures");
                    break;
                }
            }
        }
        
        log.info("Bale sync: Optimized batch processing completed: {}/{} batches successful, {} total bales synced",
                successfulBatches, totalBatches, allSyncedBales.size());
        
        // Update execution log with final results
        updateExecutionLogWithResults(executionLog, allSyncedBales.size(), allErrors);
        
        return allSyncedBales;
    }

    /**
     * Processes all bales in a single operation without batching.
     * Uses optimized bulk operations for maximum performance on smaller datasets.
     * 
     * @param baleList the list of bales to process
     * @param executionLog the execution log for tracking
     * @return list of successfully synchronized bales
     * @throws DatabaseErrorsException if critical database errors occur
     */
    private List<Bale> executeAll(List<Bale> baleList, ExecutionLog executionLog) throws DatabaseErrorsException {
        log.info("Bale sync: Execution log ID: {} - Processing all {} bales with optimized bulk operations (NO batch processing)",
                executionLog.getId(), baleList.size());
        
        List<BaleProcessingErrorResult> allErrors = new ArrayList<>();
        
        // Step 1: Optimized bulk account fetching (eliminates N+1 queries)
        log.debug("Bale sync: Execution log ID {}: Performing bulk account fetch for {} bales", executionLog.getId(), baleList.size());
        BaleAccountFetchResult accountResult = fetchAccountsBulkOptimized(baleList);
        
        allErrors.addAll(accountResult.getFailures());
        log.info("Bale sync: Execution log {}: Bulk account fetch completed - {} successful, {} failed",
                executionLog.getId(), accountResult.getSuccessCount(), accountResult.getFailureCount());
        
        // Check for critical errors
        List<BaleProcessingErrorResult> criticalErrors = allErrors.stream()
                .filter(BaleProcessingErrorResult::isCritical)
                .collect(Collectors.toList());
        
        if (!criticalErrors.isEmpty()) {
            log.error("Bale sync: Execution log {}: Critical errors detected during account fetching. Stopping processing.", executionLog.getId());
            List<ExecutionLog.SyncingError> syncingErrors = updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(allErrors);
            updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
            return List.of();
        }
        
        // Step 2: Optimized bulk currency updating (eliminates N+1 queries)
        CurrencyUpdateResult currencyResult = updateCurrenciesBulkOptimized(accountResult.getSuccessfulMappings());
        
        allErrors.addAll(currencyResult.getFailures());
        log.info("Bale sync: Execution log {}: Bulk currency update completed - {} successful, {} failed",
                executionLog.getId(), currencyResult.getSuccessCount(), currencyResult.getFailureCount());
        
        // Check for critical errors again
        criticalErrors = allErrors.stream()
                .filter(BaleProcessingErrorResult::isCritical)
                .collect(Collectors.toList());
        
        if (!criticalErrors.isEmpty()) {
            log.error("Bale sync: Execution log {}: Critical errors detected during currency processing. Stopping.", executionLog.getId());
            List<ExecutionLog.SyncingError> syncingErrors = updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(allErrors);
            updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
            return List.of();
        }
        
        // Step 3: Repository synchronization
        List<Bale> successfullyProcessedBales = currencyResult.getSuccessfulUpdates();
        List<Bale> syncedBales = new ArrayList<>();
        
        if (!successfullyProcessedBales.isEmpty()) {
            try {
                log.info("Bale sync: Execution log {}: Syncing {} optimally processed bales to repository",
                        executionLog.getId(), successfullyProcessedBales.size());
                syncedBales = bradBaleRepository.sync(successfullyProcessedBales);
                log.info("Bale sync: Execution log {}: Repository sync completed - {} bales synced",
                        executionLog.getId(), syncedBales.size());
            } catch (Exception e) {
                log.error("Bale sync: Execution log {}: Error during repository sync", executionLog.getId(), e);
                BaleProcessingErrorResult repositoryError = BaleProcessingErrorResult.fromException(null, e, "Repository Sync");
                allErrors.add(repositoryError);
                List<ExecutionLog.SyncingError> syncingErrors = updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(allErrors);
                updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
                return List.of();
            }
        }
        
        updateExecutionLogWithResults(executionLog, syncedBales.size(), allErrors);
        logProcessingSummary(executionLog.getId(), baleList.size(), syncedBales.size(), allErrors);
        
        return syncedBales;
    }

    private List<Bale> processBatchOptimized(List<Bale> batch, ExecutionLog executionLog, int batchNumber) throws DatabaseErrorsException {
        log.debug("Bale sync: Processing optimized batch {} with {} bales", batchNumber, batch.size());
        
        List<BaleProcessingErrorResult> batchErrors = new ArrayList<>();

        BaleAccountFetchResult accountResult = fetchAccountsBulkOptimized(batch);
        batchErrors.addAll(accountResult.getFailures());
        
        boolean hasCriticalErrors = batchErrors.stream().anyMatch(BaleProcessingErrorResult::isCritical);
        if (hasCriticalErrors) {
            log.error("Bale sync: Critical errors detected in batch {}, skipping batch", batchNumber);
            return List.of();
        }
        
        CurrencyUpdateResult currencyResult = updateCurrenciesBulkOptimized(accountResult.getSuccessfulMappings());
        batchErrors.addAll(currencyResult.getFailures());
        
        hasCriticalErrors = batchErrors.stream().anyMatch(BaleProcessingErrorResult::isCritical);
        if (hasCriticalErrors) {
            log.error("Bale sync: Critical errors detected during currency processing in batch {}, skipping batch", batchNumber);
            return List.of();
        }
        
        List<Bale> syncedBales = new ArrayList<>();
        List<Bale> successfullyProcessedBales = currencyResult.getSuccessfulUpdates();
        
        if (!successfullyProcessedBales.isEmpty()) {
            try {
                syncedBales = bradBaleRepository.sync(successfullyProcessedBales);
                log.debug("Bale sync: Optimized batch {} sync completed: {} bales synced", batchNumber, syncedBales.size());
            } catch (Exception e) {
                log.error("Bale sync: Repository sync failed for batch {}: {}", batchNumber, e.getMessage());
                BaleProcessingErrorResult repositoryError = BaleProcessingErrorResult.fromException(null, e, "Repository Sync");
                batchErrors.add(repositoryError);
            }
        }
        
        if (!batchErrors.isEmpty()) {
            logBatchErrors(batchNumber, batchErrors);
        }
        
        return syncedBales;
    }


    private BaleAccountFetchResult fetchAccountsBulkOptimized(List<Bale> baleList) {
        Map<Bale, Account> successfulMappings = new LinkedHashMap<>();
        List<BaleProcessingErrorResult> failures = new ArrayList<>();
        
        List<String> navReferences = baleList.stream()
                .map(bale -> bale.getAccount() != null ? bale.getAccount().getNavReference() : null)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        
        if (navReferences.isEmpty()) {
            log.warn("Bale sync: No valid NAV references found in batch of {} bales", baleList.size());
            baleList.forEach(bale -> {
                BaleProcessingErrorResult error = BaleProcessingErrorResult.accountNotFound(bale, "MISSING");
                failures.add(error);
            });
            return BaleAccountFetchResult.builder()
                    .successfulMappings(successfulMappings)
                    .failures(failures)
                    .build();
        }
        
        log.debug("Bale sync: BULK OPTIMIZATION - Fetching {} unique accounts for {} bales with single query", 
                 navReferences.size(), baleList.size());
        
        Map<String, Account> accountLookup;
        try {
            accountLookup = readAccountsUseCase.executeByNavReferences(navReferences);
            log.debug("Bale sync: BULK SUCCESS - Retrieved {} accounts from database in single query", accountLookup.size());
        } catch (Exception e) {
            log.error("Bale sync: CRITICAL_ERROR: Bulk account lookup failed for {} NAV references. Error: {}",
                    navReferences.size(), e.getMessage());
            baleList.forEach(bale -> {
                String navRef = bale.getAccount() != null ? bale.getAccount().getNavReference() : "UNKNOWN";
                BaleProcessingErrorResult error = BaleProcessingErrorResult.fromException(bale, e, "Bulk Account Lookup");
                failures.add(error);
            });
            return BaleAccountFetchResult.builder()
                    .successfulMappings(successfulMappings)
                    .failures(failures)
                    .build();
        }
        
        for (Bale bale : baleList) {
            String navReference = bale.getAccount() != null ? bale.getAccount().getNavReference() : null;
            
            if (navReference == null) {
                BaleProcessingErrorResult error = BaleProcessingErrorResult.accountNotFound(bale, "NULL");
                failures.add(error);
                continue;
            }
            
            Account account = accountLookup.get(navReference);
            if (account != null) {
                successfulMappings.put(bale, account);
                log.debug("Bale sync: O(1) account mapping successful for Entry No: {}, NAV Reference: {}",
                         bale.getEntryNo(), navReference);
            } else {
                log.warn("Bale sync: RECOVERABLE_ERROR: Account not found for Entry No: {}, NAV Reference: {}",
                        bale.getEntryNo(), navReference);
                BaleProcessingErrorResult error = BaleProcessingErrorResult.accountNotFound(bale, navReference);
                failures.add(error);
            }
        }
        
        log.info("Bale sync: BULK OPTIMIZATION SUMMARY - {} successful account mappings, {} failed out of {} total ({}% success rate)",
                successfulMappings.size(), failures.size(), baleList.size(), 
                (successfulMappings.size() * 100) / baleList.size());
        
        return BaleAccountFetchResult.builder()
                .successfulMappings(successfulMappings)
                .failures(failures)
                .build();
    }

    private CurrencyUpdateResult updateCurrenciesBulkOptimized(Map<Bale, Account> baleAccountMap) {
        List<Bale> successfulUpdates = new ArrayList<>();
        List<BaleProcessingErrorResult> failures = new ArrayList<>();
        
        Set<String> requestedCurrencyCodes = new HashSet<>();
        for (Bale bale : baleAccountMap.keySet()) {
            String currencyCode = bale.getTransactionCurrency() != null ? bale.getTransactionCurrency().getCode() : null;
            if (currencyCode != null && !currencyCode.isEmpty()) {
                requestedCurrencyCodes.add(currencyCode);
            }
        }
        
        log.debug("Bale sync: BULK OPTIMIZATION - Found {} unique currency codes requested across {} bales", 
                 requestedCurrencyCodes.size(), baleAccountMap.size());
        
        Map<String, Currency> currencyLookup = Map.of();
        if (!requestedCurrencyCodes.isEmpty()) {
            try {
                currencyLookup = readCurrenciesUseCase.executeByCurrencyCodes(new ArrayList<>(requestedCurrencyCodes));
                log.debug("Bale sync: BULK SUCCESS - Retrieved {} currencies from database in single query", currencyLookup.size());
            } catch (Exception e) {
                log.error("Bale sync: Error during bulk currency lookup for {} currency codes. Error: {}",
                        requestedCurrencyCodes.size(), e.getMessage());
            }
        }
        
        for (Map.Entry<Bale, Account> entry : baleAccountMap.entrySet()) {
            Bale bale = entry.getKey();
            Account account = entry.getValue();
            
            try {
                String navReference = bale.getAccount().getNavReference();
                log.debug("Bale sync: O(1) currency lookup for Entry No: {}, NAV Reference: {}",
                         bale.getEntryNo(), navReference);
                
                Currency currency = determineCurrencyOptimized(bale, account, currencyLookup);

                Bale updatedBale = createOptimizedBale(bale, account, currency);
                
                successfulUpdates.add(updatedBale);
                
                log.debug("Bale sync: O(1) currency update successful for Entry No: {}, NAV Reference: {}, Final currency: {}",
                         bale.getEntryNo(), navReference, currency.getCode());
                
            } catch (Exception e) {
                String navReference = bale.getAccount() != null ? bale.getAccount().getNavReference() : "UNKNOWN";
                log.warn("Bale sync: RECOVERABLE_ERROR: Failed updating currency for Entry No: {}, NAV Reference: {}. Error: {}",
                        bale.getEntryNo(), navReference, e.getMessage());
                
                String currencyCode = bale.getTransactionCurrency() != null ? bale.getTransactionCurrency().getCode() : "UNKNOWN";
                BaleProcessingErrorResult error = BaleProcessingErrorResult.currencyNotFound(bale, currencyCode);
                failures.add(error);
            }
        }
        
        log.info("Bale sync: BULK OPTIMIZATION SUMMARY - {} successful currency updates, {} failed out of {} total ({}% success rate)",
                successfulUpdates.size(), failures.size(), baleAccountMap.size(),
                baleAccountMap.size() > 0 ? (successfulUpdates.size() * 100) / baleAccountMap.size() : 0);
        
        return CurrencyUpdateResult.builder()
                .successfulUpdates(successfulUpdates)
                .failures(failures)
                .build();
    }

    private Currency determineCurrencyOptimized(Bale bale, Account account, Map<String, Currency> currencyLookup) {
        String requestedCurrencyCode = bale.getTransactionCurrency() != null ? 
                bale.getTransactionCurrency().getCode() : null;
        
        if (requestedCurrencyCode != null && !requestedCurrencyCode.isEmpty()) {
            Currency requestedCurrency = currencyLookup.get(requestedCurrencyCode);
            
            if (requestedCurrency != null) {
                log.debug("Bale sync: Using requested currency '{}' for Entry No: {}, NAV Reference: {}",
                         requestedCurrencyCode, bale.getEntryNo(), bale.getAccount().getNavReference());
                return requestedCurrency;
            } else {
                log.debug("Bale sync: Currency '{}' not found, using account default '{}' for Entry No: {}, NAV Reference: {}",
                         requestedCurrencyCode, account.getCurrency().getCode(), 
                         bale.getEntryNo(), bale.getAccount().getNavReference());
            }
        }
        
        return account.getCurrency();
    }

    private Bale createOptimizedBale(Bale bale, Account account, Currency currency) {
        Set<FxRate> fxRates = new HashSet<>();
        String baleCurrencyCode = currency.getCode();
        
        readBradFxRateUseCase.addFxRate(fxRates, baleCurrencyCode, USD, bale.getPostingDate());
        readBradFxRateUseCase.addFxRate(fxRates, baleCurrencyCode, 
                account.getCountry().getCurrency().getCode(), bale.getPostingDate());
        
        Direction direction = bale.getAmount().compareTo(BigDecimal.ZERO) < 0 ?
                Direction.DEBIT : Direction.CREDIT;
        BigDecimal absoluteAmount = bale.getAmount().abs();
        
        return bale.toBuilder()
                .account(account)
                .transactionCurrency(currency)
                .fxRates(fxRates)
                .direction(direction)
                .amount(absoluteAmount)
                .reconcileStatus(ReconcileStatus.NOT_RECONCILED)
                .build();
    }

    public Bale addFxRates(Bale bale) {
        Set<FxRate> fxRates = new HashSet<>();

        String baleCurrencyCode = bale.getTransactionCurrency().getCode();

        this.readBradFxRateUseCase.addFxRate(fxRates, baleCurrencyCode, USD, bale.getPostingDate());

        this.readBradFxRateUseCase.addFxRate(fxRates, baleCurrencyCode, bale.getAccount().getCountry().getCurrency().getCode(),
                bale.getPostingDate());

        return bale.toBuilder().fxRates(fxRates).build();
    }

    private void logBatchErrors(int batchNumber, List<BaleProcessingErrorResult> errors) {
        log.warn("Bale sync: Batch {} completed with {} errors", batchNumber, errors.size());
        
        int maxErrorsToLog = Math.min(errors.size(), 10);
        for (int i = 0; i < maxErrorsToLog; i++) {
            BaleProcessingErrorResult error = errors.get(i);
            log.warn("Bale sync: Batch {} error {}: {}", batchNumber, i + 1, error.getLogMessage());
        }
        
        if (errors.size() > maxErrorsToLog) {
            log.warn("Bale sync: Batch {} had {} additional errors not logged to prevent memory issues",
                    batchNumber, errors.size() - maxErrorsToLog);
        }
    }

    public boolean isMemoryThresholdExceeded() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        double memoryUsage = (double) usedMemory / maxMemory;
        
        if (memoryUsage > batchProcessingConfig.getBatch().getMemoryThreshold()) {
            log.warn("Bale sync: Memory usage {}% exceeds threshold {}%",
                    memoryUsage * 100, batchProcessingConfig.getBatch().getMemoryThreshold() * 100);
            return true;
        }
        
        return false;
    }

    private void updateExecutionLogWithResults(ExecutionLog executionLog,
                                             int successfulCount,
                                             List<BaleProcessingErrorResult> allErrors) {
        List<ExecutionLog.SyncingError> syncingErrors = updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(allErrors);
        
        boolean hasCriticalErrors = allErrors.stream().anyMatch(BaleProcessingErrorResult::isCritical);
        ExecutionLog.ExecutionLogStatus status = updateExecutionLogsUseCase.determineExecutionLogStatus(
                successfulCount, allErrors.size(), hasCriticalErrors);
        
        switch (status) {
            case SYNCED:
                updateExecutionLogsUseCase.updateExecutionLogWithSuccess(executionLog);
                break;
            case PARTIAL_SUCCESS:
                updateExecutionLogsUseCase.updateExecutionLogWithPartialSuccess(executionLog, successfulCount, syncingErrors);
                break;
            case ERROR:
                updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
                break;
            case EMPTY:
                updateExecutionLogsUseCase.updateExecutionLogWithRecoverableErrors(executionLog, syncingErrors);
                break;
            default:
                updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
                break;
        }
    }

    private void logProcessingSummary(Long executionLogId, int totalBales, int successfulBales, List<BaleProcessingErrorResult> errors) {
        log.info("=== Bale sync: OPTIMIZED BALE JOB PROCESSING SUMMARY (Execution Log: {}) ===", executionLogId);
        log.info("Total bales processed: {}", totalBales);
        log.info("Successfully synced: {}", successfulBales);
        log.info("Failed with errors: {}", errors.size());
        log.info("Success rate: {}%", totalBales > 0 ? (successfulBales * 100) / totalBales : 0);
        log.info("Performance: {} database queries eliminated through bulk operations", 
                (totalBales * 2) - (successfulBales > 0 ? 2 : 0)); // Estimate query reduction
        
        if (!errors.isEmpty()) {
            Map<BaleErrorClassifier.ErrorCategory, Long> errorsByCategory = errors.stream()
                    .collect(Collectors.groupingBy(BaleProcessingErrorResult::getCategory, Collectors.counting()));
            
            errorsByCategory.forEach((category, count) ->
                log.info("  {} errors: {}", category.name(), count));
            
            log.info("=== ERROR DETAILS ===");
            errors.forEach(error -> log.warn("ERROR: {}", error.getLogMessage()));
        }
        
        log.info("=== END OPTIMIZED SUMMARY ===");
    }

    // TODO: Remove BaleProcessor interface methods - no longer needed with Spring Batch
    // These methods were part of the over-engineered streaming solution
}
