package pt.jumia.services.brad.domain.entities;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

import java.time.LocalDateTime;

/**
 * Business representation of Api Log
 */
@Value
@Builder(toBuilder = true)
@AllArgsConstructor
public class ApiLog {

    Long id;
    ApiLogType logType;
    String request;
    String response;
    String relatedEntityId;
    ApiLogStatus logStatus;
    LocalDateTime createdAt;
    String createdBy;
    LocalDateTime updatedAt;
    String updatedBy;
    String partitionKey;

    public ApiLog withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .updatedAt(null)
                .updatedBy(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

    public enum ApiLogType {
        BANK_STATEMENT_CREATION,
        SFTP_BANK_STATEMENT_IMPORT,
        API_BANK_STATEMENT_FETCH,
        COMMS_MODULE_SEND_NOTIFICATION

    }

    public enum ApiLogStatus {
        SUCCESS,
        FAILURE,
        FAILURE_ACKNOWLEDGED
    }

    @Getter
    public enum SelectFields implements BaseSelectFields {
        ID("id", "id"),
        LOG_TYPE("logType", "logType"),
        LOG_STATUS("logStatus", "logStatus"),
        RELATED_ENTITY_ID("relatedEntityId", "relatedEntityId"),
        CREATED_AT("createdAt", "createdAt"),
        CREATED_BY("createdBy", "createdBy"),
        UPDATED_AT("updatedAt", "updatedAt"),
        UPDATED_BY("updatedBy", "updatedBy");

        public final String queryField;
        public final String selectCode;

        SelectFields(String queryField, String selectCode) {
            this.queryField = queryField;
            this.selectCode = selectCode;
        }
    }

    public enum SortingFields {
        ID,
        LOG_TYPE,
        LOG_STATUS,
        RELATED_ENTITY_ID,
        CREATED_AT,
        CREATED_BY,
        UPDATED_AT,
        UPDATED_BY
    }

}
