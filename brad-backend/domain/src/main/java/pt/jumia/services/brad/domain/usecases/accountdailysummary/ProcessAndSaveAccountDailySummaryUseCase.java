package pt.jumia.services.brad.domain.usecases.accountdailysummary;


import static pt.jumia.services.brad.domain.enumerations.OrderDirection.ASC;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.Transaction.SortingFields;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.AccountDailySummary;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementSortFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionSortFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.AccountDailySummaryRepository;
import pt.jumia.services.brad.domain.usecases.accountstatement.ReadAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.transactions.ReadTransactionUseCase;

@Component
@RequiredArgsConstructor
@Slf4j
public class ProcessAndSaveAccountDailySummaryUseCase {

    public static final String USD = "USD";
    private final AccountDailySummaryRepository accountDailySummaryRepository;
    private final ReadAccountDailySummaryUseCase readAccountDailySummaryUseCase;
    private final ReadAccountStatementUseCase readAccountStatementUseCase;
    private final ReadTransactionUseCase readTransactionUseCase;

    public void processBatch(final List<Account> accounts, LocalDate startDate) {

        for (Account account : accounts) {
            try {
                final AccountDailySummary accountDailySummary = getLatestAccountDailySummary(account.getId(),
                    LocalDate.now(ZoneOffset.UTC).plusDays(1));

                if (Objects.nonNull(startDate) || needsRecalculation(account, accountDailySummary)) {
                    log.info("Calculating summaries for account {}", account.getId());

                    LocalDate dateToStartCalculationFrom = Objects.nonNull(startDate) ? startDate : determineStartDate(account);

                    AccountDailySummary latestSummary = getLatestAccountDailySummary(account.getId(),
                        dateToStartCalculationFrom.minusDays(1));

                    AccountStatement stmt = null;
                    if (Objects.isNull(latestSummary)) {
                        stmt = getAccountStatements(account, 1, 1, ASC, dateToStartCalculationFrom)
                            .stream().findFirst().orElse(null);

                        if (Objects.isNull(stmt)) {
                            continue;
                        }
                    }

                    AccountDailySummary summary = AccountDailySummary.builder().build();

                    List<Transaction> transactions;

                    BigDecimal initialBalanceUsd = determineInitialBalanceUsd(stmt, latestSummary);

                    BigDecimal initialBalanceLcy = getInitialBalanceLcy(stmt, latestSummary);

                    LocalDate dateBeingCalculated = Objects.nonNull(stmt) ? stmt.getInitialDate() : latestSummary.getTransactionDate();
                    LocalDate dateToStartCalculation = Objects.nonNull(stmt) ? stmt.getInitialDate() : latestSummary.getTransactionDate();
                    int trxPage = 1;
                    int trxPageSize = 1000;
                    do {
                        PageFilters pageFilters = PageFilters.builder().page(trxPage).size(trxPageSize).build();

                        transactions = getTransactions(account, pageFilters, dateToStartCalculation,
                            LocalDate.now(ZoneOffset.UTC).plusDays(1));

                        //remove the extra transaction added by pagination workaround logic
                        if (transactions.size() > trxPageSize) {
                            transactions.remove(transactions.size() - 1);
                        }

                        if (!CollectionUtils.isEmpty(transactions)
                            && !transactions.get(0).getTransactionDate().equals(dateBeingCalculated)
                            && trxPage != 1) {
                            dateBeingCalculated = transactions.get(0).getTransactionDate();
                            if (Objects.nonNull(summary.getTransactionDate())) {
                                accountDailySummaryRepository.upsert(summary);

                                initialBalanceUsd = summary.getFinalBalanceUsd();
                                initialBalanceLcy = summary.getFinalBalance();
                                summary = AccountDailySummary.builder().build();
                            }
                        }

                        Map<LocalDate, List<Transaction>> allTransactionsOnADate = transactions.stream()
                            .collect(Collectors.groupingBy(Transaction::getTransactionDate));

                        List<LocalDate> sortedKeys = allTransactionsOnADate.keySet().stream().sorted().toList();

                        for (int i = 0; i < sortedKeys.size(); i++) {
                            List<Transaction> transactionsOnADate = allTransactionsOnADate.get(sortedKeys.get(i));
                            LocalDate date = sortedKeys.get(i);
                            dateBeingCalculated = date;

                            final FxRate fxRate = getFxRate(transactionsOnADate, date);

                            BigDecimal netAmountUsd = getUsdSum(transactionsOnADate, Direction.CREDIT)
                                .subtract(getUsdSum(transactionsOnADate, Direction.DEBIT));

                            BigDecimal newFinalBalanceUsd = calculateNewFinalBalanceUsd(account, fxRate, initialBalanceUsd,
                                netAmountUsd, summary);

                            BigDecimal netAmountLcy = getLcySum(transactionsOnADate, Direction.CREDIT)
                                .subtract(getLcySum(transactionsOnADate, Direction.DEBIT));
                            BigDecimal newFinalBalanceLcy = addOrNull(initialBalanceLcy, add(netAmountLcy, summary.getNetAmount()));

                            summary = buildSummary(summary, account, date, transactionsOnADate, initialBalanceUsd,
                                newFinalBalanceUsd, fxRate, initialBalanceLcy, newFinalBalanceLcy);

                            if (i == sortedKeys.size() - 1 && !transactions.isEmpty() && transactions.size() >= trxPageSize) {
                                continue;
                            }
                            accountDailySummaryRepository.upsert(summary);

                            initialBalanceUsd = newFinalBalanceUsd;
                            initialBalanceLcy = newFinalBalanceLcy;
                            summary = AccountDailySummary.builder().build();
                        }
                        if (!CollectionUtils.isEmpty(transactions)) {
                            log.info("Processed transactions page {} with {} transactions from {} - {}", trxPage,
                                transactions.size(),
                                transactions.get(0).getTransactionDate(),
                                transactions.get(transactions.size() - 1).getTransactionDate());
                        }
                        trxPage++;
                    } while (!transactions.isEmpty() && transactions.size() >= trxPageSize);
                    if (Objects.nonNull(summary.getTransactionDate())) {
                        accountDailySummaryRepository.upsert(summary);
                    }
                }

            } catch (Exception e) {
                log.error("Could not calculate daily summaries for account id {} because => {}", account.getId(),
                    ExceptionUtils.getStackTrace(e));
            }

        }

    }

    private static BigDecimal getInitialBalanceLcy(final AccountStatement stmt, final AccountDailySummary latestSummary) {

        return Objects.nonNull(stmt) ? stmt.getInitialDirection().equals(Direction.CREDIT)
            ? stmt.getInitialAmount()
            : stmt.getInitialAmount().negate() : latestSummary.getFinalBalance();
    }

    private static BigDecimal determineInitialBalanceUsd(final AccountStatement stmt, final AccountDailySummary latestSummary) {

        return Objects.nonNull(stmt) ? stmt.getInitialDirection().equals(Direction.CREDIT)
            ? stmt.getAmountInUsdOrZero(stmt.getInitialAmount())
            : stmt.getAmountInUsdOrZero(stmt.getInitialAmount()).negate() : latestSummary.getFinalBalanceUsd();
    }


    private FxRate getFxRate(final List<Transaction> transactionsOnADate,
        final LocalDate date) {

        FxRate fxRate = null;

        for (Transaction transaction : transactionsOnADate) {
            final AccountStatement statement = transaction.getAccountStatement();
            fxRate = transaction.getFxRates().stream()
                .filter(fx -> fx.getBaseCurrency().getCode()
                    .equals(transaction.getCurrency().getCode())).findFirst()
                .orElse(
                    statement.getFxRates().stream()
                        .filter(stmtFx -> stmtFx.getBaseCurrency().getCode()
                            .equals(transaction.getCurrency().getCode())
                            && stmtFx.getQuoteCurrency().getCode().equals(USD) && date.equals(
                            statement.getFinalDate()))
                        .findFirst().orElse(null)
                );
            if (Objects.nonNull(fxRate)) {
                break;
            }
        }
        return fxRate;
    }

    private boolean needsRecalculation(final Account account, final AccountDailySummary accountDailySummary) {

        return Objects.isNull(accountDailySummary) ||
            (!equalsTo4Decimal(accountDailySummary.getFinalBalance(), account.getBalance()) &&
                !equalsTo4Decimal(accountDailySummary.getFinalBalanceUsd(), account.getBalanceUSD()));
    }

    private AccountDailySummary getLatestAccountDailySummary(final Long accountId, LocalDate toDate) {

        try {
            return readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(accountId, toDate);
        } catch (NotFoundException ex) {

            return null;
        }
    }

    private LocalDate determineStartDate(Account account) throws EntityErrorsException {

        List<AccountStatement> statements;
        int stmtPage = 1;
        int stmtPageSize = 10;
        LocalDate startDate = LocalDate.now().minusYears(1000);
        LocalDate minTrxDate = LocalDate.now();
        boolean found = false;

        do {

            statements = getAccountStatements(account, stmtPage, stmtPageSize, OrderDirection.DESC, startDate);

            for (int i = 0; i < statements.size(); i++) {
                AccountStatement statement = statements.get(i);

                LocalDate newMinDate = getTrxEdgeDate(statement.getId(), OrderDirection.ASC);
                if (newMinDate.isBefore(minTrxDate)) {
                    minTrxDate = newMinDate;
                }
                AccountDailySummary latestSummary = getLatestAccountDailySummary(account.getId(),
                    getTrxEdgeDate(statement.getId(), OrderDirection.DESC));

                if (Objects.isNull(latestSummary)) {
                    continue;
                }
                if (equalsTo4Decimal(latestSummary.getFinalBalance(), statement.getFinalAmount())
                    && equalsTo4Decimal(latestSummary.getFinalBalanceUsd(),
                    statement.getAmountInUsdOrZero(statement.getFinalAmount()))) {

                    startDate = minTrxDate.isAfter(statement.getInitialDate()) ? statement.getInitialDate() : minTrxDate;
                    found = true;
                    break;

                }

            }

            AccountDailySummary withMissingFxRate = readAccountDailySummaryUseCase.executeOldestMissingFxRate(account.getId());

            if (Objects.nonNull(withMissingFxRate)) {
                startDate = withMissingFxRate.getTransactionDate().isBefore(startDate) ? withMissingFxRate.getTransactionDate() : startDate;
            }

            if (found) {
                break;
            }

            stmtPage++;
        } while (!statements.isEmpty() && statements.size() >= stmtPageSize);
        return startDate;
    }


    private List<AccountStatement> getAccountStatements(final Account account, final int stmtPage, final int stmtPageSize,
        final OrderDirection orderDirection, final LocalDate initialStartDate)
        throws EntityErrorsException {

        List<AccountStatement> statements;
        AccountStatementFilters statementFilters = AccountStatementFilters.builder()
            .accountID(account.getId())
            .status(List.of(AccountStatementStatus.IMPORTED))
            .initialDateStart(initialStartDate)
            .initialDateEnd(LocalDate.now())
            .build();
        AccountStatementSortFilters statementSortFilters = AccountStatementSortFilters.builder()
            .field(AccountStatement.SortingFields.INITIAL_DATE)
            .direction(orderDirection)
            .build();
        PageFilters statementpageFilters = PageFilters.builder()
            .page(stmtPage)
            .size(stmtPageSize)
            .build();
        statements = readAccountStatementUseCase.execute(statementFilters, statementSortFilters, statementpageFilters);
        return statements;
    }

    private List<Transaction> getTransactions(final Account account, PageFilters pageFilters, LocalDate fromDate, LocalDate toDate)
        throws EntityErrorsException {

        TransactionFilters transactionFilters = TransactionFilters.builder()
            .accountId(account.getAccountNumber())
            .partitionKey(String.valueOf(account.getId()))
            .transactionDateStart(fromDate)
            .transactionDateEnd(toDate)
            .importedStatementOnly(true)
            .build();
        TransactionSortFilters transactionSortFilters = TransactionSortFilters.builder()
            .field(SortingFields.TRANSACTION_DATE)
            .secondaryField(SortingFields.ID)
            .direction(ASC)
            .build();

        return readTransactionUseCase.executeAll(transactionFilters, transactionSortFilters, pageFilters);
    }

    private LocalDate getTrxEdgeDate(Long statementId, OrderDirection direction)
        throws EntityErrorsException {

        TransactionFilters filters = TransactionFilters.builder()
            .accountStatementID(String.valueOf(statementId))
            .build();
        PageFilters pageFilters = PageFilters.builder()
            .page(1)
            .size(1)
            .build();
        TransactionSortFilters sortFilters = TransactionSortFilters.builder()
            .field(SortingFields.TRANSACTION_DATE)
            .direction(direction)
            .build();
        return readTransactionUseCase.executeAll(filters, sortFilters, pageFilters)
            .stream()
            .findFirst()
            .map(Transaction::getTransactionDate)
            .orElse(direction.equals(OrderDirection.ASC) ? LocalDate.now().minusYears(1000) : LocalDate.now().plusDays(1));
    }


    private AccountDailySummary buildSummary(final AccountDailySummary accountDailySummary, final Account account,
        final LocalDate date, final List<Transaction> transactionsOnADate,
        final BigDecimal initialBalanceUsd, final BigDecimal newFinalBalanceUsd, final FxRate fxRate, final BigDecimal initialBalanceLcy,
        final BigDecimal newFinalBalanceLcy) {

        return accountDailySummary.toBuilder()
            .transactionDate(date)
            .account(account)
            .totalCreditAmount(add(getLcySum(transactionsOnADate, Direction.CREDIT), accountDailySummary.getTotalCreditAmount()))
            .totalDebitAmount(add(getLcySum(transactionsOnADate, Direction.DEBIT), accountDailySummary.getTotalDebitAmount()))
            .totalCreditAmountUsd(add(getUsdSum(transactionsOnADate, Direction.CREDIT), accountDailySummary.getTotalCreditAmountUsd()))
            .totalDebitAmountUsd(add(getUsdSum(transactionsOnADate, Direction.DEBIT), accountDailySummary.getTotalDebitAmountUsd()))
            .netAmount(add(getLcySum(transactionsOnADate, Direction.CREDIT)
                .subtract(getLcySum(transactionsOnADate, Direction.DEBIT)), accountDailySummary.getNetAmount()))
            .netAmountUsd(add(getUsdSum(transactionsOnADate, Direction.CREDIT)
                .subtract(getUsdSum(transactionsOnADate, Direction.DEBIT)), accountDailySummary.getNetAmountUsd()))
            .initialBalance(initialBalanceLcy)
            .finalBalance(newFinalBalanceLcy)
            .initialBalanceUsd(initialBalanceUsd)
            .finalBalanceUsd(newFinalBalanceUsd)
            .transactionsCount(add(transactionsOnADate.size(), accountDailySummary.getTransactionsCount()))
            .creditTransactionsCount(
                add(countTransactions(transactionsOnADate, Direction.CREDIT), accountDailySummary.getCreditTransactionsCount()))
            .debitTransactionsCount(
                add(countTransactions(transactionsOnADate, Direction.DEBIT), accountDailySummary.getDebitTransactionsCount()))
            .currency(account.getCurrency())
            .fxRate(fxRate)
            .createdAt(LocalDateTime.now(ZoneOffset.UTC))
            .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
            .build();

    }

    private BigDecimal calculateNewFinalBalanceUsd(final Account account, final FxRate fxRate,
        final BigDecimal initialBalanceUsd, final BigDecimal netAmountUsd, final AccountDailySummary summary) {

        return Objects.isNull(fxRate) && !USD.equalsIgnoreCase(account.getCurrency().getCode()) ? null
            : addOrNull(initialBalanceUsd, add(netAmountUsd, summary.getNetAmountUsd()));
    }

    private int countTransactions(final List<Transaction> transactions, final Direction debit) {

        return (int) transactions.stream().filter(trx -> trx.getDirection().equals(debit)).count();
    }

    private BigDecimal getLcySum(final List<Transaction> transactions, final Direction direction) {

        return transactions.stream().filter(t -> t.getDirection().equals(direction))
            .map(Transaction::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getUsdSum(final List<Transaction> transactions, final Direction direction) {

        return transactions.stream().filter(t -> t.getDirection().equals(direction))
            .map(Transaction::getAmountUsdOrZero)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal add(BigDecimal value1, BigDecimal value2) {

        if (value1 == null) {
            value1 = BigDecimal.ZERO;
        }
        if (value2 == null) {
            value2 = BigDecimal.ZERO;
        }
        return value1.add(value2);
    }

    private BigDecimal addOrNull(BigDecimal value1, BigDecimal value2) {

        if (value1 == null || value2 == null) {
            return null;
        }
        return value1.add(value2);
    }

    private int add(Integer value, Integer value2) {

        if (value == null) {
            value = 0;
        }
        if (value2 == null) {
            value2 = 0;
        }
        return value + value2;
    }

    private boolean equalsTo4Decimal(BigDecimal value1, BigDecimal value2) {

        if (value1 == null) {
            value1 = BigDecimal.ZERO;
        }
        if (value2 == null) {
            value2 = BigDecimal.ZERO;
        }
        BigDecimal roundedValue1 = value1.setScale(4, RoundingMode.HALF_EVEN);
        BigDecimal roundedValue2 = value2.setScale(4, RoundingMode.HALF_EVEN);

        return roundedValue1.equals(roundedValue2);
    }

}
