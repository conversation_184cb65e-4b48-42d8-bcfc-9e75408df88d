package pt.jumia.services.brad.domain.entities.filter.accountdailysummary;

import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.account.Account.Type;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode
@NoArgsConstructor
public class CommonFilters {

    private String currency;
    private List<Long> countries;
    private String legalEntity;
    private Type accountType;
    @Builder.Default
    private GroupBy groupBy = GroupBy.COUNTRY;
    private GroupBy secondaryGroupBy;

    public enum GroupBy {
        ACCOUNT_TYPE,
        LEGAL_ENTITY,
        CURRENCY,
        COUNTRY,
        ACCOUNT
    }

}
