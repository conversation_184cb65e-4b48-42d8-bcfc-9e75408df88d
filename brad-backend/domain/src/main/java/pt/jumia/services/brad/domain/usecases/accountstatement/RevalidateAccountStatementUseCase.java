package pt.jumia.services.brad.domain.usecases.accountstatement;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;

@Slf4j
@Component
@AllArgsConstructor
public class RevalidateAccountStatementUseCase {

    private final ReadAccountStatementUseCase readAccountStatementUseCase;

    private final ValidateAccountStatementUseCase validateAccountStatementUseCase;

    @Async("retryStatementsExecutor")
    public void execute(AccountStatement accountStatement) throws NotFoundException, EntityErrorsException, DatabaseErrorsException {

        log.info("Revalidating statement with id {} and status {}", accountStatement.getId(), accountStatement.getStatus());

        boolean continueRetrying = true;

        accountStatement = accountStatement.toBuilder().status(AccountStatementStatus.OPEN).build();

        validateAccountStatementUseCase.execute(accountStatement);

        while (continueRetrying) {
            try {
                accountStatement = readAccountStatementUseCase.executeByPreviousStatement(accountStatement);

                if (!accountStatement.getPreviousStatement().getStatus().equals(AccountStatementStatus.IMPORTED)) {
                    break;
                }

                accountStatement = accountStatement.toBuilder()
                    .status(AccountStatementStatus.OPEN)
                    .build();

                validateAccountStatementUseCase.execute(accountStatement);
            } catch (NotFoundException e) {
                continueRetrying = false;
            } catch (Exception e) {
                continueRetrying = false;
                log.error("Error occurred when attempting to revalidate statement with identifier {} and it's children because {}",
                    accountStatement.getId(), ExceptionUtils.getStackTrace(e));
            }
        }
    }

}
