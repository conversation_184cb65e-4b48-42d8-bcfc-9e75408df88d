package pt.jumia.services.brad.domain.usecases.accounts;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Account.StatementPeriodicity;
import pt.jumia.services.brad.domain.enumerations.StatementSource;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.AccountRepository;
import pt.jumia.services.brad.domain.usecases.accountstatement.ReadAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class UpdateAccountsUseCase {

    private final AccountRepository accountRepository;
    private final ReadCountriesUseCase readCountriesUseCase;
    private final ReadCurrenciesUseCase readCurrenciesUseCase;
    private final ReadAccountsUseCase readAccountsUseCase;
    private final ReadAccountStatementUseCase readAccountStatementUseCase;

    public Account execute(Account toUpdateAccount)
            throws NotFoundException, EntityErrorsException, DatabaseErrorsException, AlreadyExistsException {

        if (Objects.isNull(toUpdateAccount)) {
            throw EntityErrorsException.createNullClassError(Account.class);
        }
        if (Objects.isNull(toUpdateAccount.getId())) {
            throw EntityErrorsException.createEmptyEntityError("accountID", "null");
        }

        Account existingAccount = accountRepository.findById(toUpdateAccount.getId()).orElseThrow(
                () -> NotFoundException.createNotFound(Account.class, toUpdateAccount.getId()));

        if (StringUtils.hasText(toUpdateAccount.getAccountNumber()) &&
            !Objects.equals(toUpdateAccount.getAccountNumber(), existingAccount.getAccountNumber())) {
            if (accountRepository.findByAccountNumber(toUpdateAccount.getAccountNumber()).isPresent()) {
                throw AlreadyExistsException
                        .createAlreadyExists(Account.class, "AccountNumber: " + toUpdateAccount.getAccountNumber());
            }
        }

        if (!existingAccount.getNavReference().equals(toUpdateAccount.getNavReference()) ||
                !existingAccount.getCompanyID().equals(toUpdateAccount.getCompanyID())) {
            if (accountRepository.findAccountNavReferenceAndCompanyID(toUpdateAccount.getNavReference(),
                    toUpdateAccount.getCompanyID()).isPresent()) {
                throw AlreadyExistsException
                        .createAlreadyExists(Account.class, "NavReference: " + toUpdateAccount.getNavReference() +
                                ", CompanyID: " + toUpdateAccount.getCompanyID());
            }
        }

        if (StringUtils.hasText(toUpdateAccount.getIsin()) &&
            !Objects.equals(toUpdateAccount.getIsin(), existingAccount.getIsin())) {
            if (accountRepository.countByIsin(toUpdateAccount.getIsin()) > 0) {
                throw AlreadyExistsException
                        .createAlreadyExists(Account.class, "ISIN: " + toUpdateAccount.getIsin());
            }
        }

        if (StringUtils.hasText(toUpdateAccount.getContractId()) &&
            !Objects.equals(toUpdateAccount.getContractId(), existingAccount.getContractId())) {
            if (accountRepository.countByContractId(toUpdateAccount.getContractId()) > 0) {
                throw AlreadyExistsException
                        .createAlreadyExists(Account.class, "ContractID: " + toUpdateAccount.getContractId());
            }
        }

        Account updatedAccount = buildUpdatedAccount(toUpdateAccount, existingAccount);

        return accountRepository.upsert(updatedAccount);
    }

    public void executeLastProcessedStatementDate(Long accountIdToUpdate, LocalDate lastProcessedStatementDate)
            throws NotFoundException, DatabaseErrorsException {

        Account account = readAccountsUseCase.execute(Math.toIntExact(accountIdToUpdate));

        accountRepository.upsert(account.toBuilder().lastProcessedStatementDate(lastProcessedStatementDate).build());
    }

    public void executeLastProcessedStatementDate(List<Long> accountIdsToUpdate)
            throws NotFoundException, DatabaseErrorsException {

        for (Long accountId : accountIdsToUpdate) {

            Optional<AccountStatement> accountStatement = readAccountStatementUseCase.executeLastImportedStatement(accountId);

            if (accountStatement.isPresent()) {
                executeLastProcessedStatementDate(accountId, LocalDate.from(accountStatement.get().getFinalDate()));
            } else {
                executeLastProcessedStatementDate(accountId, null);
            }

        }
    }

    private Account buildUpdatedAccount(final Account toUpdateAccount, final Account accountFromDb) {

        return accountFromDb.toBuilder()
                .companyID(toUpdateAccount.getCompanyID())
                .country(readCountriesUseCase.execute(toUpdateAccount.getCountry().getCode()))
                .partner(toUpdateAccount.getPartner())
                .currency(readCurrenciesUseCase.execute(toUpdateAccount.getCurrency().getCode()))
                .navReference(toUpdateAccount.getNavReference())
                .beneficiaryName(toUpdateAccount.getBeneficiaryName())
                .beneficiaryAddress(toUpdateAccount.getBeneficiaryAddress())
                .iban(toUpdateAccount.getIban())
                .accountNumber(toUpdateAccount.getAccountNumber())
                .accountName(toUpdateAccount.getAccountName())
                .phoneNumber(toUpdateAccount.getPhoneNumber())
                .swiftCode(toUpdateAccount.getSwiftCode())
                .bankRoutingCode(toUpdateAccount.getBankRoutingCode())
                .sortCode(toUpdateAccount.getSortCode())
                .branchCode(toUpdateAccount.getBranchCode())
                .rib(toUpdateAccount.getRib())
                .type(toUpdateAccount.getType())
                .subType(toUpdateAccount.getSubType())
                .status(toUpdateAccount.getStatus())
                .statementSource(StatementSource.valueOf(toUpdateAccount.getStatementSource().getValue()))
                .statementPeriodicity(StatementPeriodicity.valueOf(toUpdateAccount.getStatementPeriodicity().name()))
                .lastProcessedStatementDate(Objects.nonNull(toUpdateAccount.getLastProcessedStatementDate())
                        ? toUpdateAccount.getLastProcessedStatementDate()
                        : accountFromDb.getLastProcessedStatementDate())
                .contractId(toUpdateAccount.getContractId())
                .isin(toUpdateAccount.getIsin())
                .amountDeposited(toUpdateAccount.getAmountDeposited())
                .maturityDate(toUpdateAccount.getMaturityDate())
                .nominalAmount(toUpdateAccount.getNominalAmount())
                .couponPaymentPeriodicity(toUpdateAccount.getCouponPaymentPeriodicity())
                .couponRate(toUpdateAccount.getCouponRate())
                .interest(toUpdateAccount.getInterest())
                .updatedAt(LocalDateTime.now())
                .updatedBy(RequestContext.getUsername()).build();
    }

}
