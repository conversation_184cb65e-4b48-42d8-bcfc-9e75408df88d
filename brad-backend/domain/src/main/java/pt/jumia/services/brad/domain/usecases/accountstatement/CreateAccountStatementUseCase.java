package pt.jumia.services.brad.domain.usecases.accountstatement;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.exceptions.*;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementRepository;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.transactions.CreateTransactionUseCase;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
@AllArgsConstructor
public class CreateAccountStatementUseCase {

    private AccountStatementRepository accountStatementRepository;
    private CreateTransactionUseCase createTransactionUseCase;
    private ReadAccountsUseCase readAccountsUseCase;
    private SaveAndMaintainStatementsOrder saveAndMaintainStatementsOrder;
    private ValidateAccountStatementUseCase validateAccountStatementUseCase;

    public AccountStatement execute(AccountStatement accountStatement, String accountNumber, List<Transaction> transactionList)
            throws EntityErrorsException, AlreadyExistsException, NotFoundException,
            EntityMismatchException, NotPositiveException, DatabaseErrorsException {

        if (transactionList == null || transactionList.isEmpty()) {
            throw new EntityErrorsException("Transaction list is empty");
        }

        if (!validateAccountStatementUseCase.isClosingBalanceValid(accountStatement, transactionList)){
            throw new EntityErrorsException("The sum of the initial balance with the transactions inserted " +
                    "does not match the closing balance of the statement. Please correct it before uploading.");
        }

        AccountStatement accountStatementCreated = this.execute(accountStatement, accountNumber, "0");
        String username = RequestContext.getUsername();
        CompletableFuture.runAsync(() -> {
            try {
                createTransactionUseCase.execute(transactionList, username, accountStatementCreated);
            } catch (Exception e) {
                validateAccountStatementUseCase.moveToReview(accountStatementCreated,
                    AccountStatementStatus.Description.TRANSACTION_CREATION_ERROR);
                log.error("Error creating transactions {}", transactionList, e);
            }
        });

        return accountStatementCreated;

    }

    public AccountStatement execute(AccountStatement accountStatement, String accountNumber, List<Transaction> transactionList,
                                 String nextStatementId)
            throws EntityErrorsException, AlreadyExistsException, NotFoundException, EntityMismatchException,
            NotPositiveException, DatabaseErrorsException {

        if (transactionList == null || transactionList.isEmpty()) {
            log.error("Unable to create account statement. Transaction list is empty");
            throw new EntityErrorsException("Transaction list is empty");
        }

        if (nextStatementId == null || nextStatementId.isEmpty()) {
            log.error("Unable to create account statement. Next statement id is not valid");
            throw EntityErrorsException.createNullClassError(AccountStatement.class);
        }

        if (!validateAccountStatementUseCase.isClosingBalanceValid(accountStatement, transactionList)){
            log.error("Unable to create account statement. Closing balance is not valid");
            throw new EntityErrorsException("The sum of the initial balance with the transactions inserted " +
                    "does not match the closing balance of the statement. Please correct it before uploading.");
        }

        AccountStatement accountStatementCreated = this.execute(accountStatement, accountNumber, nextStatementId);
        log.info("Account statement created with id {}. Proceeding with transaction creation", accountStatementCreated.getId());
        String username = RequestContext.getUsername();
        CompletableFuture.runAsync(() -> {
            try {
                log.info("Creating transactions for account statement {}", accountStatementCreated.getId());
                createTransactionUseCase.execute(transactionList, username, accountStatementCreated);
                log.info("Transactions created for account statement with id {}", accountStatementCreated.getId());
            } catch (Exception e) {
                log.error("Processing statement {}. Error creating transactions {}. {}", accountStatementCreated.getId(),
                        transactionList, ExceptionUtils.getStackTrace(e));
                validateAccountStatementUseCase.moveToReview(accountStatementCreated,
                    AccountStatementStatus.Description.TRANSACTION_CREATION_ERROR);
            }
        });

        return accountStatementCreated;
    }



    private AccountStatement execute(AccountStatement statement, final String accountNumber, String nextStatementID) throws NotFoundException,
            EntityErrorsException,
            EntityMismatchException,
            NotPositiveException, DatabaseErrorsException {

        if (statement == null) {
            log.error("Unable to create account statement. Statement is empty");
            throw EntityErrorsException.createNullClassError(AccountStatement.class);
        }

        if (statement.getInitialAmount().compareTo(BigDecimal.ZERO) < 0) {
            log.error("Unable to create account statement. Initial amount lower than 0. Initial amount: {}", statement.getInitialAmount());
            throw NotPositiveException.createNotPositive(AccountStatement.class, "Initial Amount");
        }

        if (statement.getFinalAmount().compareTo(BigDecimal.ZERO) < 0) {
            log.error("Unable to create account statement. Final amount lower than 0. Final amount: {}", statement.getFinalAmount());
            throw NotPositiveException.createNotPositive(AccountStatement.class, "Final Amount");
        }

        Account account = readAccountsUseCase.execute(accountNumber);
        log.info("Account found with id {}. Proceeding with account statement creation", account.getId());

        if (!statementCurrencyMatchesAccountCurrency(statement, account)) {
            log.error("Unable to create account statement for account {}. Statement currency [{}] different than account currency: {}",
                    account.getId(), statement.getCurrency().getCode(), account.getCurrency().getCode());
            throw EntityMismatchException.createEntityMismatch(AccountStatement.class, "Currency and Account.Currency dont match");
        }


        log.info("Looking for account statements from the same account. {}", account.getId());
        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .accountID(account.getId())
                .build();
        List<AccountStatement> accountStatements = accountStatementRepository.findAll(accountStatementFilters, null, null);

        log.info("Found {} account statements in account id {}", accountStatements.size(), account.getId());

        AccountStatement nextStatement = null;
        Optional<AccountStatement> previousStatement = Optional.empty();
        if (accountStatements.isEmpty()) {
            statement = statement.toBuilder()
                    .previousStatement(null)
                    .build();
        } else {

            if (!Objects.equals(nextStatementID, "0")) {
                Optional<AccountStatement> optionalNextStatement = accountStatementRepository.findById(Long.valueOf(nextStatementID));
                if (optionalNextStatement.isPresent()) {
                    nextStatement = optionalNextStatement.get();
                    if (nextStatement.getStatus().equals(AccountStatementStatus.IMPORTED)) {
                        log.error("Unable to create account statement. Next statement is already imported");
                        throw new IllegalArgumentException("Next statement cannot be imported");
                    }
                }
            }

            log.info("Searching for previous statement  in account {}", account.getId());
            previousStatement = Objects.isNull(nextStatement) ?
                    accountStatementRepository.findLastStatementInList(account.getId()) :
                    Objects.isNull(nextStatement.getPreviousStatement()) ?
                            Optional.empty() :
                            Optional.of(nextStatement.getPreviousStatement());
            log.info("Previous statement found ? {}. Account {}", previousStatement.isEmpty(), account.getId());
        }

        log.info("Saving and maintaining statement order {}", account.getId());
        return saveAndMaintainStatementsOrder.execute(statement, accountNumber, previousStatement,
            nextStatement,
            account);


    }

    private boolean statementCurrencyMatchesAccountCurrency(AccountStatement accountStatement, Account account) {
        return accountStatement.getCurrency().getCode().equals(account.getCurrency().getCode());
    }

    public AccountStatement addFxRates(AccountStatement accountStatement) throws EntityErrorsException {

        return saveAndMaintainStatementsOrder.addFxRates(accountStatement);

    }
}
