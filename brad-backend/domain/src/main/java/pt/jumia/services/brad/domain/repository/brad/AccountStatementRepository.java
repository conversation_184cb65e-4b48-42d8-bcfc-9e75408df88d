package pt.jumia.services.brad.domain.repository.brad;

import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementSortFilters;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.util.List;
import java.util.Optional;

public interface AccountStatementRepository {

    AccountStatement upsert(AccountStatement accountStatement);

    void addFxRates(AccountStatement accountStatement);

    Optional<AccountStatement> findById(Long id);

    List<AccountStatement> findAll(AccountStatementFilters accountStatementFilters, AccountStatementSortFilters accountStatementSortFilters,
                                   PageFilters pageFilters) throws EntityErrorsException;

    Optional<AccountStatement> findLastStatementInList(Long accountId);

    Optional<AccountStatement> findLastImportedStatementInList(Long accountId);

    Optional<AccountStatement> findByPreviousStatementId(AccountStatement accountStatement);

    Optional<AccountStatement> findLastUpdatedStatementByUser(String username);

    List<AccountStatement> findAllStatementsOrdered(AccountStatementFilters accountStatementFilters);

    void deleteById(Long id);

    Integer count(AccountStatementFilters filters);

    //for testing purposes
    void deleteAllFxRatesAccountStatement();

    void save(AccountStatement accountStatement);
}
