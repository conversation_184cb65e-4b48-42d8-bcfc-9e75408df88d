package pt.jumia.services.brad.domain.usecases.accountstatement;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementRepository;
import pt.jumia.services.brad.domain.usecases.accountstatement.PostStatementWriteUseCase.Operation;
import pt.jumia.services.brad.domain.usecases.transactions.ReadTransactionUseCase;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ValidateAccountStatementUseCase {

    private final AccountStatementRepository accountStatementRepository;
    private final ReadTransactionUseCase readTransactionUseCase;
    private final PostStatementWriteUseCase postStatementWriteUseCase;

    public void execute(AccountStatement accountStatement) throws NotFoundException, EntityErrorsException, DatabaseErrorsException {

        log.info("Validating statement with id {} and status {}", accountStatement.getId(), accountStatement.getStatus());
        TransactionFilters transactionFilters = TransactionFilters.builder()
            .partitionKey(String.valueOf(accountStatement.getAccount().getId()))
            .accountStatementID(String.valueOf(accountStatement.getId()))
            .selectedFields(List.of(
                Transaction.SelectFields.TRANSACTION_DATE.getQueryField(),
                Transaction.SelectFields.AMOUNT.getQueryField(),
                Transaction.SelectFields.DIRECTION.getQueryField(),
                Transaction.SelectFields.CURRENCY.getQueryField()
            ))
            .build();
        List<Transaction> transactions = readTransactionUseCase.execute(transactionFilters, null, null);

        if (accountStatement.getPreviousStatement() != null) {
            accountStatement = validateStatement(accountStatement, transactions);
        } else {
            if (!isClosingBalanceValid(accountStatement, transactions)) {
                accountStatement = moveToReview(accountStatement, AccountStatementStatus.Description.ERROR_CLOSING_BALANCE);
            }
        }

        if (accountStatement.getStatus().equals(AccountStatementStatus.OPEN)) {
            log.info("Statement {} validated", accountStatement.getId());
            accountStatement = accountStatement.toBuilder()
                .status(AccountStatementStatus.IMPORTED)
                .statusDescription(isOpeningDateValid(accountStatement) ? AccountStatementStatus.Description.IMPORTED :
                    AccountStatementStatus.Description.ERROR_STATEMENT_GAP)
                .build();
        }

        accountStatementRepository.upsert(accountStatement);
        postStatementWriteUseCase.execute(accountStatement, Operation.IMPORTED);

    }


    private AccountStatement validateStatement(AccountStatement accountStatement, List<Transaction> transactions) {
        if (!isPreviousStatementValid(accountStatement)) {
            return moveToReview(accountStatement, AccountStatementStatus.Description.ERROR_PREVIOUS_STATEMENT);
        }
        if (!isOpeningBalanceValid(accountStatement)) {
            return moveToReview(accountStatement, AccountStatementStatus.Description.ERROR_OPENING_BALANCE);
        }
        if (!isClosingBalanceValid(accountStatement, transactions)) {
            return moveToReview(accountStatement, AccountStatementStatus.Description.ERROR_CLOSING_BALANCE);
        }
        if (isPeriodOverlapping(accountStatement, transactions)) {
            return moveToReview(accountStatement, AccountStatementStatus.Description.ERROR_PERIOD_OVERLAP);
        }
        return accountStatement;
    }


    private Boolean isOpeningBalanceValid(AccountStatement accountStatement) {
        AccountStatement previousStatement = accountStatement.getPreviousStatement();
        if (previousStatement.getFinalAmount().compareTo(accountStatement.getInitialAmount()) != 0) {
            return false;
        }
        return previousStatement.getFinalDirection().equals(accountStatement.getInitialDirection());
    }

    private Boolean isPreviousStatementValid(AccountStatement accountStatement) {
        return accountStatement.getPreviousStatement().getStatus().equals(AccountStatementStatus.IMPORTED);
    }

    public Boolean isClosingBalanceValid(AccountStatement accountStatement, List<Transaction> transactions) {

        BigDecimal statementInitialAmount = accountStatement.getInitialDirection().equals(Direction.DEBIT) ?
            accountStatement.getInitialAmount().negate() : accountStatement.getInitialAmount();

        BigDecimal statementFinalAmount = accountStatement.getFinalDirection().equals(Direction.DEBIT) ?
            accountStatement.getFinalAmount().negate() : accountStatement.getFinalAmount()
                .setScale(4, RoundingMode.HALF_UP);

        BigDecimal totalAmount = transactions.stream()
            .map(transaction -> transaction.getDirection().equals(Direction.DEBIT) ?
                transaction.getAmount().negate() : transaction.getAmount())
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal closingBalance = statementInitialAmount.add(totalAmount).setScale(4, RoundingMode.HALF_UP);

        if (closingBalance.compareTo(statementFinalAmount) != 0) {
            return false;
        }

        boolean doTransactionsHaveSameCurrency = transactions.stream()
            .allMatch(transaction -> transaction.getCurrency().equals(accountStatement.getCurrency()));

        if (!doTransactionsHaveSameCurrency) {
            log.error("Transactions have different currencies from the account statement");
            return false;
        }


        return closingBalance.compareTo(BigDecimal.ZERO) >= 0 ?
            accountStatement.getFinalDirection().equals(Direction.CREDIT) :
            accountStatement.getFinalDirection().equals(Direction.DEBIT);
    }

    private Boolean isPeriodOverlapping(AccountStatement accountStatement, List<Transaction> transactions) {
        return accountStatement.getPreviousStatement().getFinalDate().atTime(23, 59, 59)
            .isAfter(accountStatement.getInitialDate().atStartOfDay());
    }

    private Boolean areTransactionsDatesValid(AccountStatement accountStatement, List<Transaction> transactions) {
        return transactions.stream()
            .allMatch(transaction -> !transaction.getTransactionDate().isBefore(accountStatement.getInitialDate()) &&
                !transaction.getTransactionDate().isAfter(accountStatement.getFinalDate()));
    }

    public boolean isOpeningDateValid(AccountStatement accountStatement) {
        if (accountStatement.getPreviousStatement() == null) {
            return true;
        }
        return accountStatement.getInitialDate().equals(accountStatement.getPreviousStatement().getFinalDate().plusDays(1));
    }

    public AccountStatement moveToReview(AccountStatement accountStatement, AccountStatementStatus.Description error) {
        log.info("Statement {} moved to review with error {}", accountStatement.getId(), error);
        return accountStatement.toBuilder()
            .status(AccountStatementStatus.REVIEW)
            .statusDescription(error)
            .build();
    }
}
