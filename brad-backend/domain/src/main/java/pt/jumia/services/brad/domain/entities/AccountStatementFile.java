package pt.jumia.services.brad.domain.entities;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder(toBuilder = true)
@AllArgsConstructor
public class AccountStatementFile {

    private Long id;
    private String name;
    private String url;
    private ProcessingStatus processingStatus;
    private String statusDescription;
    private String checksum;
    private ExecutionLog executionLog;
    private AccountStatement statement;
    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime updatedAt;
    private String updatedBy;

    public enum SortingFields {
        ID, CREATED_AT, UPDATED_AT
    }

    @Getter
    public enum ProcessingStatus {
        NEW("New file found is int the queue to be processed"),
        PROCESSED("The file was downloaded and processed - A statement was created"),
        FAILED_PROCESSING("The file was not processed "),
        DUPLICATED("The file already exists and was already processed but has a different checksum"),
        DELETED_FROM_REMOTE("File was deleted from the remote system"),
        PROCESSING("File is being processed"),;

        private final String description;

        ProcessingStatus(String description) {

            this.description = description;
        }

    }

    public AccountStatementFile withoutDbFields() {

        return this.toBuilder()
            .id(null)
            .updatedAt(null)
            .updatedBy(null)
            .createdAt(null)
            .createdBy(null)
            .build();
    }

}
