package pt.jumia.services.brad.domain.usecases.currencies;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.filter.currency.CurrencyFilters;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.CurrencyRepository;

import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class ReadCurrenciesUseCase {

    private final CurrencyRepository currencyRepository;

    public Currency execute(Long id) throws NotFoundException {
        return currencyRepository.findById(id).orElseThrow(() ->
                NotFoundException.createNotFound(Currency.class, id));
    }

    public Currency execute(String code) throws NotFoundException {
        return currencyRepository.findByCode(code).orElseThrow(() ->
                NotFoundException.createNotFound(Currency.class, code));
    }

    public List<Currency> executeCurrencies(CurrencyFilters currencyFilters) {
        return currencyRepository.findAll(currencyFilters);
    }

    public Map<String, Currency> executeByCurrencyCodes(List<String> currencyCodes) {
        if (currencyCodes == null || currencyCodes.isEmpty()) {
            return Map.of();
        }
        
        return currencyRepository.findByCurrencyCodes(currencyCodes);
    }
}
