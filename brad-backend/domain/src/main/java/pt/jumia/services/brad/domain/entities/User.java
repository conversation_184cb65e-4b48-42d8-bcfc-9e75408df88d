package pt.jumia.services.brad.domain.entities;


import com.neovisionaries.i18n.CountryCode;
import lombok.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;


@Getter
@ToString
@EqualsAndHashCode
@AllArgsConstructor
@Builder(toBuilder = true)
public class User {

    private final String username;

    @Builder.Default
    private final boolean canAccess = false;
    @Builder.Default
    private final boolean canAccessAccounts = false;
    @Builder.Default
    private final boolean canDownloadAccountsCsv = false;
    @Builder.Default
    private final boolean canManageAccounts = false;
    @Builder.Default
    private final boolean canDeleteAccounts = false;
    @Builder.Default
    private final boolean canAccessCountries = false;
    @Builder.Default
    private final boolean canManageCountries = false;
    @Builder.Default
    private final boolean canAccessStatements = false;
    @Builder.Default
    private final boolean canAccessNavbi = false;
    @Builder.Default
    private final boolean canAccessReconcile = false;
    @Builder.Default
    private final boolean canManageReconcile = false;
    @Builder.Default
    private final boolean canAccessReconciliation = false;
    @Builder.Default
    private final boolean canManageReconciliation = false;
    @Builder.Default
    private final boolean canAccessScheduler = false;
    @Builder.Default
    private final boolean canManageScheduler = false;
    @Builder.Default
    private final boolean canAccessApiLog = false;
    @Builder.Default
    private final boolean canAccessCurrencies = false;
    @Builder.Default
    private final boolean canManageCurrencies = false;
    @Builder.Default
    private final boolean canUploadStatements = false;
    @Builder.Default
    private final boolean canApproveReconciliations = false;
    @Builder.Default
    private final boolean canUnmatchReconciliations = false;
    @Builder.Default
    private final boolean canExportReconciliations = false;
    @Builder.Default
    private final boolean canExportStatements = false;
    @Builder.Default
    private final boolean canDiscardStatements = false;
    @Builder.Default
    private final boolean canRetryStatement = false;
    @Builder.Default
    private final boolean canAccessTroubleshooting = false;
    @Builder.Default
    private final boolean canAccessFxRates = false;
    @Builder.Default
    private final boolean canAccessExecutionLog = false;
    @Builder.Default
    private final boolean canAccessExportLog = false;
    @Builder.Default
    private final boolean canManageViewEntities = false;
    @Builder.Default
    private final boolean canManageThresholds = false;
    @Builder.Default
    private final boolean canAccessStatementFiles = false;
    @Builder.Default
    private final boolean canScanSftpFolder = false;
    @Builder.Default
    private final boolean canDownloadStatementFiles = false;
    @Builder.Default
    private final boolean canAccessSettings  = false;
    @Builder.Default
    private final boolean canEditSettings  = false;
    @Builder.Default
    private final Map<CountryCode, List<String>> countriesPermissionsList = Collections.emptyMap();

}
