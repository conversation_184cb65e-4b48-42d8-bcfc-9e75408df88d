package pt.jumia.services.brad.domain.entities.fake;

import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Account.Type;
import pt.jumia.services.brad.domain.enumerations.StatementSource;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public interface FakeAccounts {

    Account FAKE_ACCOUNT = Account.builder()
            .id(1L)
            .companyID("fakeCompanyID")
            .country(FakeCountries.NIGERIA)
            .navReference("fakeNavReference")
            .beneficiaryName("fakeBeneficiaryName")
            .beneficiaryAddress("fakeBeneficiaryAddress")
            .iban("fakeIban")
            .accountNumber("fakeAccountNumber")
            .accountName("fakeBankName")
            .swiftCode("fakeSwiftCode")
            .bankRoutingCode("fakeBankRoutingCode")
            .sortCode("fakeSortCode")
            .branchCode("fakeBranchCode")
            .rib("fakeRib")
            .currency(FakeCurrencies.NGN)
            .type(Type.BANK_ACCOUNT)
            .subType(null)
            .status(Account.Status.OPEN)
            .createdAt(LocalDateTime.now())
            .createdBy("fakeUser")
            .updatedAt(LocalDateTime.now())
            .updatedBy("fakeUser")
            .statementSource(StatementSource.MANUAL_UPLOAD)
            .statementPeriodicity(Account.StatementPeriodicity.DAILY)
            .partner(null)
            .phoneNumber(null)
            .isin(null)
            .contractId(null)
            .amountDeposited(null)
            .maturityDate(null)
            .nominalAmount(null)
            .couponPaymentPeriodicity(null)
            .couponRate(null)
            .interest(null)
            .build();

    Account FAKE_ACCOUNT_A = Account.builder()
            .id(2L)
            .companyID("fakeCompanyIDA")
            .country(FakeCountries.EGYPT)
            .navReference("fakeNavReferenceA")
            .beneficiaryName("fakeBeneficiaryName")
            .beneficiaryAddress("fakeBeneficiaryAddress")
            .iban("fakeIban")
            .accountNumber("fakeAccountNumber")
            .accountName("fakeBankName")
            .swiftCode("fakeSwiftCode")
            .bankRoutingCode("fakeBankRoutingCode")
            .sortCode("fakeSortCode")
            .branchCode("fakeBranchCode")
            .rib("fakeRib")
            .currency(FakeCurrencies.EGP)
            .type(Type.BANK_ACCOUNT)
            .status(Account.Status.OPEN)
            .createdAt(LocalDateTime.now())
            .createdBy("fakeUserA")
            .updatedAt(LocalDateTime.now())
            .updatedBy("fakeUser")
            .statementSource(StatementSource.MANUAL_UPLOAD)
            .statementPeriodicity(Account.StatementPeriodicity.DAILY)
            .build();

    Account FAKE_ACCOUNT_MX = Account.builder()
            .id(1L)
            .companyID("MXfakeCompanyID")
            .country(FakeCountries.UGANDA)
            .navReference("MXfakeNavReference")
            .beneficiaryName("MXfakeBeneficiaryName")
            .beneficiaryAddress("MXfakeBeneficiaryAddress")
            .iban("MXfakeIban")
            .accountNumber("MXfakeAccountNumber")
            .accountName("MXfakeBankName")
            .swiftCode("MXfakeSwiftCode")
            .bankRoutingCode("MXfakeBankRoutingCode")
            .sortCode("MXfakeSortCode")
            .branchCode("MXfakeBranchCode")
            .rib("MXfakeRib")
            .currency(FakeCurrencies.UGX)
            .type(Type.BANK_ACCOUNT)
            .status(Account.Status.OPEN)
            .createdAt(LocalDateTime.now())
            .createdBy("MXfakeUser")
            .updatedAt(LocalDateTime.now())
            .updatedBy("MXfakeUser")
            .statementSource(StatementSource.MANUAL_UPLOAD)
            .statementPeriodicity(Account.StatementPeriodicity.DAILY)
            .build();

    static List<Account> getFakeAccounts(int amount, Type type) {

        Type selectedType = type != null ? type : Type.BANK_ACCOUNT;

        List<Account> fakeAccounts = new ArrayList<>();
        for (int i = 1; i <= amount; i++) {
            var fakeAccount = FAKE_ACCOUNT.toBuilder()
                    .id(null)
                    .companyID(FAKE_ACCOUNT.getCompanyID() + i)
                    .country(FakeCountries.NIGERIA)
                    .navReference(FAKE_ACCOUNT.getNavReference() + i)
                    .accountNumber(FAKE_ACCOUNT.getAccountNumber() + i)
                    .accountName(FAKE_ACCOUNT.getAccountName() + i)
                    .currency(FakeCurrencies.NGN)
                    .type(selectedType)
                    .status(Account.Status.OPEN)
                    .statementSource(StatementSource.MANUAL_UPLOAD)
                    .statementPeriodicity(Account.StatementPeriodicity.DAILY)
                    .createdAt(LocalDateTime.now())
                    .createdBy(FAKE_ACCOUNT.getCreatedBy() + i)
                    .updatedAt(LocalDateTime.now())
                    .updatedBy(FAKE_ACCOUNT.getUpdatedBy() + i)
                    .build();

            fakeAccount = switch (selectedType) {
                case BANK_ACCOUNT -> fakeAccount.toBuilder()
                        .beneficiaryName(FAKE_ACCOUNT.getBeneficiaryName() + i)
                        .beneficiaryAddress(FAKE_ACCOUNT.getBeneficiaryAddress() + i)
                        .iban(FAKE_ACCOUNT.getIban() + i)
                        .swiftCode(FAKE_ACCOUNT.getSwiftCode() + i)
                        .bankRoutingCode(FAKE_ACCOUNT.getBankRoutingCode() + i)
                        .sortCode(FAKE_ACCOUNT.getSortCode() + i)
                        .branchCode(FAKE_ACCOUNT.getBranchCode() + i)
                        .rib(FAKE_ACCOUNT.getRib() + i)
                        .build();
                case PSP, MOBILE_MONEY -> fakeAccount.toBuilder()
                        .beneficiaryName(null)
                        .beneficiaryAddress(null)
                        .iban(null)
                        .swiftCode(null)
                        .bankRoutingCode(null)
                        .sortCode(null)
                        .branchCode(null)
                        .rib(null)
                        .partner("fakePartner" + i)
                        .phoneNumber("fakePhoneNumber" + i)
                        .build();
                case WALLET -> fakeAccount.toBuilder()
                        .beneficiaryName(null)
                        .beneficiaryAddress(null)
                        .iban(null)
                        .swiftCode(null)
                        .bankRoutingCode(null)
                        .sortCode(null)
                        .branchCode(null)
                        .rib(null)
                        .partner("fakePartner" + i)
                        .build();
                case INVESTMENTS -> fakeAccount.toBuilder()
                        .beneficiaryName(null)
                        .beneficiaryAddress(null)
                        .iban(null)
                        .swiftCode(null)
                        .bankRoutingCode(null)
                        .sortCode(null)
                        .branchCode(null)
                        .rib(null)
                        .partner(null)
                        .phoneNumber(null)
                        .build();
            };

            fakeAccounts.add(fakeAccount);
        }

        return fakeAccounts;
    }
}
