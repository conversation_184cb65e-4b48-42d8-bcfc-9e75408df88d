package pt.jumia.services.brad.domain.repository.brad;

import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.filter.currency.CurrencyFilters;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface CurrencyRepository {

    Currency upsert(Currency currency);

    Optional<Currency> findById(long id);

    Optional<Currency> findByCode(String code);

    List<Currency> findAll(CurrencyFilters currencyFilters);

    void deleteById(long id);

    Map<String, Currency> findByCurrencyCodes(List<String> currencyCodes);
}
