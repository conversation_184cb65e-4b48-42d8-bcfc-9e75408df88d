package pt.jumia.services.brad.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import pt.jumia.services.brad.domain.entities.error.BaleProcessingErrorResult;

import java.util.List;

@Getter
@Builder
@AllArgsConstructor
public class CurrencyUpdateResult {
    private final List<Bale> successfulUpdates;
    private final List<BaleProcessingErrorResult> failures;

    public int getSuccessCount() {
        return successfulUpdates.size();
    }

    public int getFailureCount() {
        return failures.size();
    }

    public int getTotalCount() {
        return getSuccessCount() + getFailureCount();
    }

    public boolean hasFailures() {
        return !failures.isEmpty();
    }

    public boolean isCompleteSuccess() {
        return failures.isEmpty() && !successfulUpdates.isEmpty();
    }

    public boolean isPartialSuccess() {
        return !successfulUpdates.isEmpty() && !failures.isEmpty();
    }

    public boolean isCompleteFailure() {
        return successfulUpdates.isEmpty();
    }
}
