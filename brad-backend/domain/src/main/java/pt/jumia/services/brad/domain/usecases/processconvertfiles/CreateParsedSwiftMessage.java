package pt.jumia.services.brad.domain.usecases.processconvertfiles;

import static pt.jumia.services.brad.domain.enumerations.SftpRunMode.LIVE;

import com.prowidesoftware.swift.model.AbstractMessage;
import java.io.File;
import java.io.IOException;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.files.SwiftMessageFileResponse;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityMismatchException;
import pt.jumia.services.brad.domain.exceptions.NotPositiveException;
import pt.jumia.services.brad.domain.settings.OverallSettings;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.CreateAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.PostStatementWriteUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.PostStatementWriteUseCase.Operation;
import pt.jumia.services.brad.domain.usecases.apilog.CreateApiLogUseCase;
import pt.jumia.services.brad.domain.usecases.processconvertfiles.convertfile.ConvertFile;
import pt.jumia.services.brad.domain.utils.AccountStatementFilesUtils;

@Slf4j
@Component
@AllArgsConstructor
public class CreateParsedSwiftMessage {

    private CreateAccountStatementUseCase createAccountStatementUseCase;
    private OverallSettings overallSettings;
    private CreateApiLogUseCase createApiLogUseCase;
    private ReadAccountsUseCase readAccountsUseCase;
    private PostStatementWriteUseCase postStatementWriteUseCase;

    public SwiftMessageFileResponse parseSwiftMessage(File file, AccountStatementFile fileInDb, String nextStatementId)
        throws AlreadyExistsException, EntityErrorsException, IOException, EntityMismatchException, NotPositiveException {

        AbstractMessage message = AbstractMessageParser.getMessage(file);
        ConvertFile convertFile = AbstractMessageParser.getConvertFile(message);
        SwiftMessageFileResponse response = AbstractMessageParser.generateSwiftMessageFileResponse(convertFile, message);
        Account account = readAccountsUseCase.execute(Objects.requireNonNull(response).getAccountNumber());

        if (CollectionUtils.isEmpty(response.getTransactions()) && Objects.nonNull(response.getStatement())) {

            log.info("No transactions found in the file {} ", fileInDb.getName());
            postStatementWriteUseCase.execute(response.getStatement().toBuilder().account(account).build(), Operation.CREATE);

            return response.toBuilder()
                .statement(null)
                .processingStatus(ProcessingStatus.PROCESSED)
                .statusDescription("Processed file successfully with no transactions")
                .build();
        }

        if (LIVE.equals(overallSettings.getSftpSettings().getSftpRunMode(account.getId()))) {

            AccountStatement savedAccountStatement;
            if (StringUtils.isNotBlank(nextStatementId)) {
                savedAccountStatement = createAccountStatementUseCase.execute(response.getStatement(),
                    response.getAccountNumber(), response.getTransactions(), nextStatementId);
            } else {
                savedAccountStatement = createAccountStatementUseCase.execute(response.getStatement(),
                    response.getAccountNumber(),
                    response.getTransactions());
            }
            createApiLogUseCase.execute(AccountStatementFilesUtils.createApiLog(response, account));
            log.info("Successfully parsed swift message and created statements for account {} in LIVE mode", account.getId());
            return response.toBuilder()
                .processingStatus(ProcessingStatus.PROCESSED)
                .statusDescription(ProcessingStatus.PROCESSED.getDescription())
                .statement(savedAccountStatement).build();
        } else {
            createApiLogUseCase.execute(AccountStatementFilesUtils.createApiLog(response, account));
            log.info("Successfully parsed swift message for account {} in DRY_RUN mode", account.getId());
            return response.toBuilder()
                .processingStatus(ProcessingStatus.PROCESSED)
                .statusDescription("Processed in DRY_RUN mode")
                .statement(null)
                .build();
        }
    }

}
