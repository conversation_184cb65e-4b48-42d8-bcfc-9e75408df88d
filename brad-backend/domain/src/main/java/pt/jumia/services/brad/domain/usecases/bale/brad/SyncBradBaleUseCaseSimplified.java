package pt.jumia.services.brad.domain.usecases.bale.brad;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;

import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;



import java.util.List;

/**
 * Simplified Bale synchronization use case for Spring Batch.
 * 
 * This class focuses on the core business logic of processing bales
 * without complex batch processing, which is now handled by Spring Batch.
 * 
 * Key simplifications:
 * - No batch processing logic (handled by Spring Batch)
 * - No complex error aggregation (handled by <PERSON> Batch)
 * - No memory monitoring (handled by Spring Batch)
 * - Clear, focused responsibility
 */
@Slf4j
@Component
@AllArgsConstructor
public class SyncBradBaleUseCaseSimplified {

    private final BradBaleRepository bradBaleRepository;
    private final ReadCurrenciesUseCase readCurrenciesUseCase;
    private final ReadAccountsUseCase readAccountsUseCase;


    /**
     * Synchronizes a list of bales to the Brad database.
     * 
     * This method is designed to be called by Spring Batch ItemWriter
     * and focuses on the core business logic without complex error handling.
     */
    public List<Bale> execute(List<Bale> baleList) throws DatabaseErrorsException {
        if (baleList == null || baleList.isEmpty()) {
            log.debug("No bales to sync");
            return List.of();
        }

        log.info("Syncing {} bales to Brad database", baleList.size());
        
        try {
            // Use existing repository sync method
            List<Bale> syncedBales = bradBaleRepository.sync(baleList);
            
            log.info("Successfully synced {} bales to Brad database", syncedBales.size());
            return syncedBales;
            
        } catch (Exception e) {
            log.error("Failed to sync {} bales to Brad database: {}", baleList.size(), e.getMessage(), e);
            throw new DatabaseErrorsException("Failed to sync bales to Brad database: " + e.getMessage());
        }
    }

    /**
     * Processes a single bale for validation and enrichment.
     * 
     * This method is designed to be called by Spring Batch ItemProcessor
     * and focuses on individual bale processing logic.
     */
    public Bale processBale(Bale bale) throws Exception {
        if (bale == null) {
            throw new IllegalArgumentException("Bale cannot be null");
        }

        log.debug("Processing bale with entry number: {}", bale.getEntryNo());

        // Validate required fields
        validateBale(bale);

        // Fetch and validate account
        Account account = fetchAccount(bale);
        
        // Determine and validate currency
        Currency currency = determineCurrency(bale, account);
        
        // Build enriched bale
        Bale enrichedBale = bale.toBuilder()
                .account(account)
                .transactionCurrency(currency)
                .build();

        log.debug("Successfully processed bale with entry number: {}", bale.getEntryNo());
        return enrichedBale;
    }

    private void validateBale(Bale bale) {
        if (bale.getEntryNo() == null) {
            throw new IllegalArgumentException("Bale entry number is required");
        }

        if (bale.getAccount() == null || bale.getAccount().getAccountNumber() == null) {
            throw new IllegalArgumentException("Bale account information is required");
        }
    }

    private Account fetchAccount(Bale bale) throws Exception {
        String accountNumber = bale.getAccount().getAccountNumber();
        
        try {
            Account account = readAccountsUseCase.execute(accountNumber);
            if (account == null) {
                throw new IllegalArgumentException("Account not found: " + accountNumber);
            }
            return account;
        } catch (Exception e) {
            log.debug("Account lookup failed for account: {} - {}", accountNumber, e.getMessage());
            throw new IllegalArgumentException("Failed to fetch account: " + accountNumber, e);
        }
    }

    private Currency determineCurrency(Bale bale, Account account) throws Exception {
        // Try to use the bale's transaction currency first
        if (bale.getTransactionCurrency() != null && bale.getTransactionCurrency().getCode() != null) {
            String currencyCode = bale.getTransactionCurrency().getCode();
            try {
                return readCurrenciesUseCase.execute(currencyCode);
            } catch (Exception e) {
                log.debug("Transaction currency '{}' not found, falling back to account currency", currencyCode);
            }
        }

        // Fall back to account's default currency
        if (account.getCurrency() != null) {
            return account.getCurrency();
        }

        throw new IllegalArgumentException("No valid currency found for bale entry: " + bale.getEntryNo());
    }
}
