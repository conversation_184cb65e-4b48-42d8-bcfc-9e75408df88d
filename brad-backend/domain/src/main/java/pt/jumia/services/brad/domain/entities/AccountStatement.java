package pt.jumia.services.brad.domain.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;
import pt.jumia.services.brad.domain.enumerations.AccountStatementFlow;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;


@Value
@AllArgsConstructor
@Builder(toBuilder = true)
public class AccountStatement {

    Long id;
    Currency currency;
    String statementId;
    AccountStatement previousStatement;
    LocalDate initialDate;
    LocalDate finalDate;
    Direction initialDirection;
    Direction finalDirection;
    BigDecimal initialAmount;
    BigDecimal finalAmount;
    Set<FxRate> fxRates;
    AccountStatementStatus status;
    AccountStatementStatus.Description statusDescription;
    String description;
    Account account;
    String createdBy;
    LocalDateTime createdAt;
    String updatedBy;
    LocalDateTime updatedAt;

    AccountStatementFlow flow;

    public AccountStatement withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .updatedAt(null)
                .updatedBy(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

    public BigDecimal getAmountInLocalCurrency(BigDecimal amount) {
        return convertCurrency(amount, currency.getCode(), account.getCountry().getCurrency().getCode());
    }

    public BigDecimal getAmountInUsd(BigDecimal amount) {
        return convertCurrency(amount, currency.getCode(), "USD");
    }

    @JsonIgnore
    public BigDecimal getAmountInUsdOrZero(BigDecimal amount) {

        final BigDecimal usd = convertCurrency(amount, currency.getCode(), "USD");
        if (Objects.isNull(usd)) {
            return BigDecimal.ZERO;
        }
        return usd;
    }

    private BigDecimal convertCurrency(BigDecimal amount, String currency, String quoteCurrency) {

        if (this.currency.getCode().equals(quoteCurrency)) {
            return amount;
        }
        //this is here for code quality reasons
        BigDecimal nullValue = null;


        if (fxRates == null) {
            return nullValue;
        }

        FxRate fxRate = fxRates.stream().filter(fx ->
                        fx.getBaseCurrency().getCode().equals(currency) &&
                                fx.getQuoteCurrency().getCode().equals(quoteCurrency))
                .findFirst().orElse(null);

        return fxRate != null ? fxRate.getBid().multiply(amount) : currency.equals(quoteCurrency) ? amount : nullValue;
    }

    @Getter
    public enum SelectFields implements BaseSelectFields {
        ID("id", "id"),
        CURRENCY("currency.code", "currency"),
        STATEMENT_ID("statementId", "statementId"),
        PREVIOUS_STATEMENT_ID("previousStatement.id", "previousStatementId"),
        INITIAL_DATE("initialDate", "initialDate"),
        FINAL_DATE("finalDate", "finalDate"),
        INITIAL_DIRECTION("initialDirection", "initialDirection"),
        FINAL_DIRECTION("finalDirection", "finalDirection"),
        INITIAL_AMOUNT("initialAmount", "initialAmount"),
        FINAL_AMOUNT("finalAmount", "finalAmount"),
        STATUS("status", "status"),
        STATUS_DESCRIPTION("statusDescription", "statusDescription"),
        DESCRIPTION("description", "description"),
        FLOW("flow", "flow"),
        CREATED_AT("createdAt", "createdAt"),
        CREATED_BY("createdBy", "createdBy"),
        UPDATED_AT("updatedAt", "updatedAt"),
        UPDATED_BY("updatedBy", "updatedBy");

        public final String queryField;
        public final String selectCode;

        SelectFields(String queryField, String selectCode) {
            this.queryField = queryField;
            this.selectCode = selectCode;
        }
    }

    public enum SortingFields {
        ID,
        ACCOUNT_ID,
        CURRENCY,
        STATEMENT_ID,
        PREVIOUS_STATEMENT_ID,
        INITIAL_DATE,
        FINAL_DATE,
        INITIAL_DIRECTION,
        FINAL_DIRECTION,
        INITIAL_AMOUNT,
        FINAL_AMOUNT,
        STATUS,
        STATUS_DESCRIPTION,
        DESCRIPTION,
        FLOW,
        CREATED_AT,
        CREATED_BY,
        UPDATED_AT,
        UPDATED_BY
    }

}
