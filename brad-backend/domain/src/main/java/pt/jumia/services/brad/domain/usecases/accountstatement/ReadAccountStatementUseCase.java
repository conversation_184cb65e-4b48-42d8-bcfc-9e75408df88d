package pt.jumia.services.brad.domain.usecases.accountstatement;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementSortFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementRepository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

@Component
@RequiredArgsConstructor
public class ReadAccountStatementUseCase {

    private final AccountStatementRepository accountStatementRepository;


    public List<AccountStatement> execute(AccountStatementFilters accountStatementFilters, AccountStatementSortFilters accountStatementSortFilters,
                                       PageFilters pageFilters) throws EntityErrorsException {
        return accountStatementRepository.findAll(accountStatementFilters, accountStatementSortFilters, pageFilters);
    }

    public List<AccountStatement> executeAllStatementsOrdered(AccountStatementFilters accountStatementFilters) {
        return accountStatementRepository.findAllStatementsOrdered(accountStatementFilters);
    }

    public AccountStatement execute(final Long id) throws NotFoundException {

        return accountStatementRepository.findById(id)
                .orElseThrow(() -> NotFoundException.createNotFound(AccountStatement.class, id));
    }

    public AccountStatement executeByPreviousStatement(final AccountStatement accountStatement) throws NotFoundException {
        return accountStatementRepository.findByPreviousStatementId(accountStatement)
                .orElseThrow(() -> NotFoundException.createNotFoundByPreviousStatement(AccountStatement.class, accountStatement.getId()));
    }

    public AccountStatement executeFetchLastImportedStatement(final long accountId) throws EntityErrorsException {
        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .accountID(accountId)
                .status(List.of(AccountStatementStatus.IMPORTED))
                .build();

        AccountStatementSortFilters accountStatementSortFilters = AccountStatementSortFilters.builder()
                .field(AccountStatement.SortingFields.UPDATED_AT)
                .direction(OrderDirection.DESC)
                .build();

        PageFilters pageFilters = PageFilters.builder().size(1).build();

        return this.execute(accountStatementFilters, accountStatementSortFilters, pageFilters).stream()
                .findFirst().orElse(null);
    }

    public AccountStatement executeFirstStatementInError(final long accountId) throws NotFoundException, EntityErrorsException {

        AccountStatement lastImportedStatement = executeFetchLastImportedStatement(accountId);
        if (Objects.isNull(lastImportedStatement)) {

            return this.executeAllStatementsOrdered(AccountStatementFilters.builder()
                            .accountID(accountId)
                            .status(List.of(AccountStatementStatus.REVIEW))
                            .build())
                    .stream()
                    .findFirst()
                    .orElse(null);

        }
        return executeByPreviousStatement(lastImportedStatement);
    }

    public Optional<AccountStatement> executeLastStatement(final long accountId) {
        return accountStatementRepository.findLastStatementInList(accountId);
    }

    public Optional<AccountStatement> executeFetchLastUpdatedStatement(String username) {
        return accountStatementRepository.findLastUpdatedStatementByUser(username);
    }

    public Optional<AccountStatement> executeLastImportedStatement(final long accountId) {
        return accountStatementRepository.findLastImportedStatementInList(accountId);
    }

    public Integer executeCount(AccountStatementFilters filters) {
        return accountStatementRepository.count(filters);
    }

    public List<String> executeStatusTypes() {
        return Stream.of(AccountStatementStatus.values())
                .filter(status -> status != AccountStatementStatus.DISCARDED)
                .map(AccountStatementStatus::name).toList();
    }

    public List<String> executeErrorTypes() {
        return Stream.of(AccountStatementStatus.Description.values()).map(AccountStatementStatus.Description::getName).toList();
    }

    public List<String> executeDirections() {
        return Stream.of(Direction.values()).map(Direction::name).toList();
    }

}
