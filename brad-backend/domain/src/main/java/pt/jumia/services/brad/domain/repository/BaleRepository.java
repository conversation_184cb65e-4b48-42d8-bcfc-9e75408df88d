package pt.jumia.services.brad.domain.repository;

import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.text.ParseException;
import java.util.List;

public interface BaleRepository {

    List<Bale> findAll(Integer entryNo, ViewEntity baleViewEntity, boolean retrying, ExecutionLog executionLog)
            throws DatabaseErrorsException, EntityErrorsException, ParseException;

    List<Bale> findAllBatched(Integer entryNo, ViewEntity baleViewEntity, boolean retrying, ExecutionLog executionLog,
                             int offset, int limit) throws DatabaseErrorsException, EntityErrorsException, ParseException;

    long countBales(Integer entryNo, ViewEntity baleViewEntity, boolean retrying)
            throws DatabaseErrorsException, EntityErrorsException;

    String fetchCompanyId(ViewEntity baleViewEntity, boolean retrying) throws DatabaseErrorsException, EntityErrorsException;

    Integer findLastBaleWithOffset(ViewEntity baleViewEntity, Integer offset) throws EntityErrorsException;

    List<Bale> findAllLastBaleWithOffset(ViewEntity baleViewEntity, Integer offset) throws EntityErrorsException;

    void refresh();

}
