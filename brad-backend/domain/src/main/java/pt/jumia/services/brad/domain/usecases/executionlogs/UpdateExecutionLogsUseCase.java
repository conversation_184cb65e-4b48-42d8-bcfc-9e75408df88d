package pt.jumia.services.brad.domain.usecases.executionlogs;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.ExecutionLogRepository;
import pt.jumia.services.brad.domain.usecases.FindExceptionRootCauseUseCase;
import pt.jumia.services.brad.domain.entities.error.BaleProcessingErrorResult;
import pt.jumia.services.brad.domain.entities.error.BaleErrorClassifier;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateExecutionLogsUseCase {

    private static final int SAMPLE_ERRORS_PER_CATEGORY = 5;

    private final ExecutionLogRepository executionLogRepository;
    private final ReadExecutionLogsUseCase readExecutionLogsUseCase;
    public ExecutionLog execute(ExecutionLog toUpdateExecutionLog) throws EntityErrorsException, NotFoundException {

        if (Objects.isNull(toUpdateExecutionLog)) {
            throw EntityErrorsException.createNullClassError(ExecutionLog.class);
        }
        if (Objects.isNull(toUpdateExecutionLog.getId())){
            throw EntityErrorsException.createEmptyEntityError("ExecutionLogID", "null");
        }

        ExecutionLog executionLog = readExecutionLogsUseCase.execute(toUpdateExecutionLog.getId());

        ExecutionLog updatedExecutionLog = executionLog.toBuilder()
                .errors(toUpdateExecutionLog.getErrors())
                .logStatus(toUpdateExecutionLog.getLogStatus())
                .build();
        log.debug("Updating ExecutionLog with ID: {}. New status: {}, Errors: {}",
                updatedExecutionLog.getId(), updatedExecutionLog.getLogStatus(), updatedExecutionLog.getErrors());
        return executionLogRepository.upsert(updatedExecutionLog);
    }

    public void updateExecutionLogWithError(ExecutionLog executionLog, List<ExecutionLog.SyncingError> errors) {
        updateExecutionLog(executionLog.toBuilder().errors(errors).logStatus(ExecutionLog.ExecutionLogStatus.ERROR).build());
    }

    public void updateExecutionLogWithSuccess(ExecutionLog executionLog) {
        updateExecutionLog(executionLog.toBuilder().logStatus(ExecutionLog.ExecutionLogStatus.SYNCED).build());
    }

    public void updateExecutionLogWithPartialSuccess(ExecutionLog executionLog,
                                                   int successfulCount,
                                                   List<ExecutionLog.SyncingError> errors) {
        log.info("Updating execution log {} with partial success: {} successful, {} errors",
                executionLog.getId(), successfulCount, errors.size());
        
        ExecutionLog updatedLog = executionLog.toBuilder()
                .errors(errors)
                .logStatus(ExecutionLog.ExecutionLogStatus.PARTIAL_SUCCESS)
                .recordsAmount(successfulCount)
                .build();
        updateExecutionLog(updatedLog);
    }

    public void updateExecutionLogWithRecoverableErrors(ExecutionLog executionLog,
                                                       List<ExecutionLog.SyncingError> errors) {
        log.info("Updating execution log {} with recoverable errors only: {} errors",
                executionLog.getId(), errors.size());
        
        updateExecutionLogWithError(executionLog, errors);
    }

    public List<ExecutionLog.SyncingError> convertBaleErrorsToSyncingErrors(List<BaleProcessingErrorResult> baleErrors) {
        if (baleErrors.isEmpty()) {
            return new ArrayList<>();
        }
        
        log.debug("Converting {} bale errors to syncing errors using error summary approach", baleErrors.size());
        return createErrorSummary(baleErrors);
    }

    private List<ExecutionLog.SyncingError> createErrorSummary(List<BaleProcessingErrorResult> baleErrors) {
        Map<String, List<BaleProcessingErrorResult>> errorsByCategory = 
            baleErrors.stream().collect(Collectors.groupingBy(e -> e.getCategory().name()));
        
        List<ExecutionLog.SyncingError> summary = new ArrayList<>();
        
        summary.add(createSummaryError("SUMMARY",
            String.format("Total errors: %d across %d categories", baleErrors.size(), errorsByCategory.size()),
            baleErrors.size()));
        
        for (Map.Entry<String, List<BaleProcessingErrorResult>> entry : errorsByCategory.entrySet()) {
            String category = entry.getKey();
            List<BaleProcessingErrorResult> categoryErrors = entry.getValue();
            
            summary.add(createSummaryError(category + "_SUMMARY",
                String.format("%s errors: %d", category, categoryErrors.size()),
                categoryErrors.size()));
            
            categoryErrors.stream()
                    .limit(SAMPLE_ERRORS_PER_CATEGORY)
                    .map(this::convertBaleErrorToSyncingError)
                    .forEach(summary::add);
        }
        
        log.info("Created error summary: {} total errors reduced to {} summary entries", 
                baleErrors.size(), summary.size());
        
        return summary;
    }

    private ExecutionLog.SyncingError createSummaryError(String category, String description, int count) {
        return ExecutionLog.SyncingError.builder()
                .errorDescription(description)
                .errorCategory(category)
                .operationContext("ERROR_SUMMARY")
                .affectedRecordId(String.valueOf(count))
                .build();
    }

    public ExecutionLog.SyncingError convertBaleErrorToSyncingError(BaleProcessingErrorResult baleError) {
        return ExecutionLog.SyncingError.builder()
                .errorDescription(baleError.getErrorDescription())
                .errorCategory(baleError.getCategory().name())
                .affectedRecordId(baleError.getBale() != null ? String.valueOf(baleError.getBale().getId()) : null)
                .operationContext(baleError.getOperationContext())
                .accountNumber(baleError.getNavReference())
                .entryNo(baleError.getEntryNo())
                .build();
    }

    public void updateExecutionLog(ExecutionLog executionLog) {
        try {
            this.execute(executionLog);
        } catch (Exception e) {
            Throwable rootCause = FindExceptionRootCauseUseCase.findRootCause(e);
            log.error("Update ExecutionLog failed. Exception {}", ExceptionUtils.getStackTrace(rootCause));
        }
    }

    public void handleException(Throwable e, List<ExecutionLog.SyncingError> syncingErrors) {
        handleException(e, syncingErrors, "Unknown Operation");
    }

    public void handleException(Throwable e, List<ExecutionLog.SyncingError> syncingErrors, String operationContext) {
        Throwable rootCause = FindExceptionRootCauseUseCase.findRootCause(e);
        log.error("Exception during {}: {}", operationContext, ExceptionUtils.getStackTrace(rootCause));
        
        String rootCauseMessage = rootCause.getMessage();
        BaleErrorClassifier.ErrorCategory category = BaleErrorClassifier.categorizeError(rootCause);
        
        boolean errorExists = syncingErrors.stream()
                .anyMatch(syncingError -> syncingError.getErrorDescription().equals(rootCauseMessage) &&
                         operationContext.equals(syncingError.getOperationContext()));
        
        if (!errorExists) {
            ExecutionLog.SyncingError syncingError = ExecutionLog.SyncingError.builder()
                    .errorDescription(rootCauseMessage)
                    .errorCategory(category.name())
                    .operationContext(operationContext)
                    .build();
            syncingErrors.add(syncingError);
        }
    }

    public ExecutionLog.ExecutionLogStatus determineExecutionLogStatus(int successCount,
                                                                      int errorCount,
                                                                      boolean hasCriticalErrors) {
        if (hasCriticalErrors) {
            return ExecutionLog.ExecutionLogStatus.ERROR;
        }
        
        if (successCount == 0 && errorCount == 0) {
            return ExecutionLog.ExecutionLogStatus.EMPTY;
        }
        
        if (successCount > 0 && errorCount == 0) {
            return ExecutionLog.ExecutionLogStatus.SYNCED;
        }
        
        if (successCount > 0 && errorCount > 0) {
            return ExecutionLog.ExecutionLogStatus.PARTIAL_SUCCESS;
        }
        
        if (successCount == 0 && errorCount > 0) {
            return ExecutionLog.ExecutionLogStatus.ERROR;
        }
        
        return ExecutionLog.ExecutionLogStatus.ERROR;
    }


}
