package pt.jumia.services.brad.domain.usecases.accountstatement;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementRepository;
import pt.jumia.services.brad.domain.usecases.accountstatement.PostStatementWriteUseCase.Operation;

@Slf4j
@Component
@AllArgsConstructor
public class DiscardAccountStatementUseCase {

    private RevalidateAccountStatementUseCase revalidateAccountStatementUseCase;
    private AccountStatementRepository accountStatementRepository;
    private DeleteStatementsAtomicallyHelper deleteStatementsAtomicallyHelper;
    private PostStatementWriteUseCase postStatementWriteUseCase;

    public void execute(Long id) throws NotFoundException, EntityErrorsException {

        AccountStatement accountStatement = accountStatementRepository.findById(id)
            .orElseThrow(() -> new NotFoundException("Account statement not found"));

        if (!accountStatement.getStatus().equals(AccountStatementStatus.REVIEW)) {
            throw new IllegalArgumentException("Account statement is not in review");
        }

        deleteStatementsAtomicallyHelper.deleteAtomically(accountStatement);
        postStatementWriteUseCase.execute(accountStatement, Operation.DELETE);

    }

    public void executeDiscardLastImportedStatement(Long id) throws EntityErrorsException {

        AccountStatement accountStatement = accountStatementRepository.findById(id)
            .orElseThrow(() -> new NotFoundException("Account statement not found"));

        AccountStatement lastImportedStatement = accountStatementRepository
            .findLastImportedStatementInList(accountStatement.getAccount().getId())
            .orElseThrow(() -> new NotFoundException("Account statement not found"));

        if (!accountStatement.getId().equals(lastImportedStatement.getId())) {
            throw new IllegalArgumentException("Account statement is not the last imported");
        }

        deleteStatementsAtomicallyHelper.deleteAtomically(lastImportedStatement);
        retryStatement(accountStatement, id);
        postStatementWriteUseCase.execute(accountStatement, Operation.DELETE);

    }

    public void retryStatement(AccountStatement accountStatement, Long id) {

        CompletableFuture.runAsync(() -> {
            AccountStatementFilters filters = AccountStatementFilters.builder()
                .accountID(accountStatement.getAccount().getId())
                .status(List.of(AccountStatementStatus.REVIEW)).build();

            List<AccountStatement> statementsInReview = null;

            try {
                statementsInReview = accountStatementRepository.findAll(filters, null, null);
            } catch (EntityErrorsException e) {
                log.error("Error fetching statements in review for retry", e);
            }

            log.info("Retrying statement with identifier {} for user with identifier {}",
                id, RequestContext.getUsername());

            statementsInReview.forEach(statement -> {
                try {
                    revalidateAccountStatementUseCase.execute(statement);
                } catch (EntityErrorsException e) {
                    log.error("Error retrying statement {}", statement, e);
                }
            });
        });
    }

}
