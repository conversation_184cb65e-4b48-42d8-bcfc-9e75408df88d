package pt.jumia.services.brad.domain.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "network")
public class NetworkProperties {

    private Jokes jokes = new Jokes();

    @Data
    public static class Jokes {
        private String url = "http://api.icndb.com";
    }
}
