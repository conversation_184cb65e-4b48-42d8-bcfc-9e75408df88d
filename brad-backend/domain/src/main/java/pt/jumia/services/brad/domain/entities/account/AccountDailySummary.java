package pt.jumia.services.brad.domain.entities.account;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor
@ToString
public class AccountDailySummary {

    private LocalDate transactionDate;

    private Account account;

    private BigDecimal totalCreditAmount;

    private BigDecimal totalDebitAmount;

    private BigDecimal totalCreditAmountUsd;

    private BigDecimal totalDebitAmountUsd;

    private BigDecimal netAmount;

    private BigDecimal netAmountUsd;

    private BigDecimal initialBalance;

    private BigDecimal finalBalance;

    private BigDecimal initialBalanceUsd;

    private BigDecimal finalBalanceUsd;

    private Integer transactionsCount;

    private Integer debitTransactionsCount;

    private Integer creditTransactionsCount;

    private Currency currency;

    private FxRate fxRate;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    @Getter
    public enum SelectFields implements BaseSelectFields {
        TRANSACTION_DATE("transactionDate", "transactionDate"),
        ACCOUNT("account", "account"),
        TOTAL_CREDIT_AMOUNT("totalCreditAmount", "totalCreditAmount"),
        TOTAL_DEBIT_AMOUNT("totalDebitAmount", "totalDebitAmount"),
        TOTAL_CREDIT_AMOUNT_USD("totalCreditAmountUsd", "totalCreditAmountUsd"),
        TOTAL_DEBIT_AMOUNT_USD("totalDebitAmountUsd", "totalDebitAmountUsd"),
        NET_AMOUNT("netAmount", "netAmount"),
        NET_AMOUNT_USD("netAmountUsd", "netAmountUsd"),
        CURRENCY("currency", "currency"),
        FX_RATE("fxRate", "fxRate"),
        INITIAL_BALANCE("initialBalance", "initialBalance"),
        FINAL_BALANCE("finalBalance", "finalBalance"),
        INITIAL_BALANCE_USD("initialBalanceUsd", "initialBalanceUsd"),
        FINAL_BALANCE_USD("finalBalanceUsd", "finalBalanceUsd"),
        CREATED_AT("createdAt", "createdAt"),
        UPDATED_AT("updatedAt", "updatedAt");

        public final String queryField;
        public final String selectCode;

        SelectFields(String queryField, String selectCode) {

            this.queryField = queryField;
            this.selectCode = selectCode;
        }
    }

    public enum SortingFields {
        TRANSACTION_DATE,
        ACCOUNT,
        TOTAL_CREDIT_AMOUNT,
        TOTAL_DEBIT_AMOUNT,
        TOTAL_CREDIT_AMOUNT_USD,
        TOTAL_DEBIT_AMOUNT_USD,
        NET_AMOUNT,
        NET_AMOUNT_USD,
        TRANSACTIONS_COUNT,
        DEBIT_TRANSACTIONS_COUNT,
        CREDIT_TRANSACTIONS_COUNT,
        INITIAL_BALANCE,
        FINAL_BALANCE,
        INITIAL_BALANCE_USD,
        FINAL_BALANCE_USD,
        CREATED_AT,
        UPDATED_AT
    }


}
