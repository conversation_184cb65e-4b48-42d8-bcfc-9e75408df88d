package pt.jumia.services.brad.domain.repository.brad;

import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.dtos.AccountTroubleshootingDto;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountSortFilters;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface AccountRepository {

    Account upsert(Account account) throws DatabaseErrorsException;

    Optional<Account> findById(long id);

    Optional<Account> findByAccountNumber(String accountNumber);

    List<Account> findAll(AccountFilters accountFilters, AccountSortFilters accountSortFilters,
                              PageFilters pageFilters) throws EntityErrorsException;

    List<AccountTroubleshootingDto> findAllAccountsInTroubleShooting(AccountFilters accountFilters,
        AccountSortFilters accountSortFilters,
        PageFilters pageFilters) throws EntityErrorsException;

    void deleteById(long id);

    Integer count(AccountFilters filters);

    Integer countTroubleShooting(AccountFilters filters);

    Optional<Account> findAccountNavReference(String navReference);

    Optional<Account> findAccountNavReferenceAndCompanyID(String navReference, String companyID);

    List<Account> findAccountNavReferences(AccountFilters filters);

    Integer countByIsin(String isin);

    Integer countByContractId(String contractId);

    Map<String, Account> findByNavReferences(List<String> navReferences);
}
