package pt.jumia.services.brad.domain.enumerations;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public enum AccountStatementStatus {

    OPEN,
    IMPORTED,
    DISCARDED,
    REVIEW;

    public static AccountStatementStatus fromString(String value) {
        if (value != null) {
            for (AccountStatementStatus status : AccountStatementStatus.values()) {
                if (value.equalsIgnoreCase(status.toString())) {
                    return status;
                }
            }
        }
        throw new IllegalArgumentException("Invalid AccountStatementStatus value: " + value);
    }

    public static List<AccountStatementStatus> getValues() {
        List<AccountStatementStatus> values = new ArrayList<>(AccountStatementStatus.values().length);
        Collections.addAll(values, AccountStatementStatus.values());
        return values;
    }

    @Getter
    @AllArgsConstructor
    public enum Description {

        ERROR_OPENING_BALANCE("Opening Balance"),
        ERROR_PREVIOUS_STATEMENT("Previous Statement"),
        ERROR_CLOSING_BALANCE("Closing Balance"),
        ERROR_PERIOD_OVERLAP("Period Overlap"),
        ERROR_STATEMENT_GAP("Statement Gap"),
        IMPORTED("Imported"),
        TRANSACTION_CREATION_ERROR("Transaction creation error");
        private final String name;

        public static Description fromString(String value) {
            if (value != null) {
                for (Description description : Description.values()) {
                    if (value.equalsIgnoreCase(description.toString()) ||
                        value.equalsIgnoreCase(description.getName())) {
                        return description;
                    }
                }
            }
            throw new IllegalArgumentException("Invalid AccountStatementStatus.Description value: " + value);
        }
    }
}
