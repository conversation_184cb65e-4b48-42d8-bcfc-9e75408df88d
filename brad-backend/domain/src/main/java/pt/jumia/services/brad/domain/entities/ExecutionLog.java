package pt.jumia.services.brad.domain.entities;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Value;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;
import net.minidev.json.JSONObject;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Value
@AllArgsConstructor
@Builder(toBuilder = true)
public class ExecutionLog {

    Long id;
    ExecutionLogType logType;
    ExecutionLogStatus logStatus;
    Integer recordsAmount;
    LocalDateTime executionStartTime;
    LocalDateTime executionEndTime;
    String appliedFilters;
    String query;
    List<SyncingError> errors;

    public ExecutionLog withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .build();
    }

    public enum ExecutionLogType {
        BALE, FINREC_STATEMENTS, FX_RATES, BANK_STATEMENTS_FILE_SCAN,
        FCMB_BANK_STATEMENTS_FETCH
    }

    public enum ExecutionLogStatus {
        STARTED, FETCHED, SYNCED, ERROR, EMPTY, PARTIAL_SUCCESS
    }


    @Value
    @AllArgsConstructor
    @Builder(toBuilder = true)
    public static class SyncingError {
        String errorDescription;
        String errorCategory;
        String affectedRecordId;
        String operationContext;
        String accountNumber;
        Integer entryNo;
        
        // Backward compatibility constructor
        public SyncingError(String errorDescription) {
            this.errorDescription = errorDescription;
            this.errorCategory = null;
            this.affectedRecordId = null;
            this.operationContext = null;
            this.accountNumber = null;
            this.entryNo = null;
        }
        public static String toString(List<SyncingError> errors) {
            JSONObject json = new JSONObject();
            errors.forEach(error -> {
                String index = String.valueOf(errors.indexOf(error) + 1);
                JSONObject errorJson = new JSONObject();
                errorJson.put("description", error.getErrorDescription());
                if (error.getErrorCategory() != null) {
                    errorJson.put("category", error.getErrorCategory());
                }
                if (error.getAffectedRecordId() != null) {
                    errorJson.put("recordId", error.getAffectedRecordId());
                }
                if (error.getOperationContext() != null) {
                    errorJson.put("operation", error.getOperationContext());
                }
                if (error.getAccountNumber() != null) {
                    errorJson.put("accountNumber", error.getAccountNumber());
                }
                if (error.getEntryNo() != null) {
                    errorJson.put("entryNo", error.getEntryNo());
                }
                json.put(index, errorJson);
            });
            return json.toJSONString();
        }

        public static List<SyncingError> fromString(String errors) {
            List<SyncingError> errorList = new ArrayList<>();
            ObjectMapper mapper = new ObjectMapper();
            try {
                JSONObject json = mapper.readValue(errors, JSONObject.class);
                json.forEach((key, value) -> {
                    if (value instanceof String) {
                        // Backward compatibility
                        errorList.add(new SyncingError(value.toString()));
                    } else if (value instanceof JSONObject) {
                        // New enhanced format
                        JSONObject errorJson = (JSONObject) value;
                        SyncingError.SyncingErrorBuilder builder = SyncingError.builder()
                                .errorDescription(errorJson.getAsString("description"));
                        
                        if (errorJson.containsKey("category")) {
                            builder.errorCategory(errorJson.getAsString("category"));
                        }
                        if (errorJson.containsKey("recordId")) {
                            builder.affectedRecordId(errorJson.getAsString("recordId"));
                        }
                        if (errorJson.containsKey("operation")) {
                            builder.operationContext(errorJson.getAsString("operation"));
                        }
                        if (errorJson.containsKey("accountNumber")) {
                            builder.accountNumber(errorJson.getAsString("accountNumber"));
                        }
                        // Backward compatibility for old field name
                        if (errorJson.containsKey("navRef")) {
                            builder.accountNumber(errorJson.getAsString("navRef"));
                        }
                        if (errorJson.containsKey("entryNo")) {
                            Object entryNoObj = errorJson.get("entryNo");
                            if (entryNoObj instanceof Number) {
                                builder.entryNo(((Number) entryNoObj).intValue());
                            }
                        }
                        errorList.add(builder.build());
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
            return errorList;
        }
    }
    @Getter
    public enum SelectFields implements BaseSelectFields {
        ID("id", "id"),
        LOG_TYPE("logType", "type"),
        LOG_STATUS("logStatus", "status"),
        RECORDS_AMOUNT("recordsAmount", "recordsAmount"),
        EXECUTION_START_TIME("executionStartTime", "executionStartTime"),
        EXECUTION_END_TIME("executionEndTime", "executionEndTime");

        public final String queryField;
        public final String selectCode;

        SelectFields(String queryField, String selectCode) {
            this.queryField = queryField;
            this.selectCode = selectCode;
        }
    }

    public enum SortingFields {
        ID,
        LOG_TYPE,
        LOG_STATUS,
        RECORDS_AMOUNT,
        EXECUTION_START_TIME,
        EXECUTION_END_TIME
    }

}
