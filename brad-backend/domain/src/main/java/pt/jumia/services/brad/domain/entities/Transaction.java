package pt.jumia.services.brad.domain.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.entities.shared.BaseSelectFields;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.ReconcileStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;

@Value
@Builder(toBuilder = true)
@AllArgsConstructor
@Slf4j
public class Transaction {

    Long id;
    String type;
    Currency currency;
    LocalDate valueDate;
    LocalDate transactionDate;
    LocalDate statementDate;
    Direction direction;
    BigDecimal amount;
    Set<FxRate> fxRates;
    String reference;
    String description;
    AccountStatement accountStatement;
    LocalDateTime createdAt;
    String createdBy;
    LocalDateTime updatedAt;
    String updatedBy;
    Integer skAudInsert;
    LocalDateTime timestampRunAt;
    ReconcileStatus reconcileStatus;
    Reconciliation reconciliation;
    String remittanceInformation;
    String orderingPartyName;

    boolean statementIsInError;

    public Transaction withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .updatedAt(null)
                .updatedBy(null)
                .build();
    }

    public BigDecimal getAmountLocalCurrency() {
        if (this.accountStatement == null || this.currency == null) {
            return null;
        }
        return convertCurrency(this.amount, this.currency.getCode(),
                this.accountStatement.getAccount().getCountry().getCurrency().getCode());
    }

    public BigDecimal getAmountUsd() {
        if (this.accountStatement == null || this.currency == null) {
            return null;
        }
        return convertCurrency(this.amount, this.currency.getCode(), "USD");
    }

    @JsonIgnore
    public BigDecimal getAmountUsdOrZero() {

        if (this.accountStatement == null || this.currency == null) {
            throw new NotFoundException("Statement or currency could not be found for transaction " + id);
        }
        final BigDecimal usd = convertCurrency(this.amount, this.currency.getCode(), "USD");
        if (Objects.isNull(usd)) {
            return BigDecimal.ZERO;
        }
        return usd;
    }

    private BigDecimal convertCurrency(BigDecimal amount, String currency, String quoteCurrency) {

        FxRate fxRate;

        if (this.currency.getCode().equals(quoteCurrency)) {
            return amount;
        }

        if (this.valueDate.equals(this.accountStatement.getFinalDate())) {
            fxRate = this.accountStatement.getFxRates().stream()
                .filter(fx -> fx.getBaseCurrency().getCode().equals(currency) && fx.getQuoteCurrency().getCode().equals(quoteCurrency))
                .findFirst().orElse(null);
        } else {
            fxRate = fxRates.stream()
                .filter(fx -> fx.getBaseCurrency().getCode().equals(currency) && fx.getQuoteCurrency().getCode().equals(quoteCurrency))
                .findFirst().orElse(null);
        }

        //this is here for code quality reasons
        BigDecimal nullValue = null;

        final BigDecimal amountOrNull = currency.equals(quoteCurrency) ? amount : nullValue;
        return fxRate != null ? fxRate.getBid().multiply(amount) : amountOrNull;
    }

    @Getter
    public enum GroupingFields {
        TYPE("type", false, true),
        CURRENCY("currency.code", false, true),
        VALUE_DATE("valueDate", true, true),
        TRANSACTION_DATE("transactionDate", true, true),
        STATEMENT_DATE("statementDate", false, true),
        DIRECTION("direction", true, true),
        AMOUNT("amount", true, true),
        REFERENCE("reference", false, true),
        DESCRIPTION("description", true, true),
        BANK_STATEMENT("accountStatement.statementId", false, true),
        RECONCILIATION_CREATOR("creator", true, false),
        RECONCILIATION_CREATION_DATE("creationDate", true, false),
        REMITTANCE_INFORMATION("remittanceInformation", true, true),
        ORDERING_PARTY_NAME("orderingPartyName", true, true);

        public final String groupField;
        public final boolean active;
        public final boolean belongsToMainEntity;

        GroupingFields(String groupField, boolean active, boolean belongsToMainEntity) {
            this.groupField = groupField;
            this.active = active;
            this.belongsToMainEntity = belongsToMainEntity;
        }

        public static GroupingFields fromValue(String value) {
            for (GroupingFields groupingFields : GroupingFields.values()) {
                if (groupingFields.name().equalsIgnoreCase(value)) {
                    return groupingFields;
                }
            }
            throw new IllegalArgumentException("Invalid GroupingFields value: " + value);
        }

        public static List<GroupingFields> fromValues(List<String> values) {
            return values.stream()
                    .map(GroupingFields::fromValue)
                    .collect(Collectors.toList());
        }
    }

    @Getter
    public enum SelectFields implements BaseSelectFields {

        ID("id", "id"),
        TYPE("type", "type"),
        CURRENCY("currency", "currency"),
        VALUE_DATE("valueDate", "valueDate"),
        TRANSACTION_DATE("transactionDate", "transactionDate"),
        STATEMENT_DATE("statementDate", "statementDate"),
        DIRECTION("direction", "direction"),
        AMOUNT("amount", "amount"),
        REFERENCE("reference", "reference"),
        DESCRIPTION("description", "description"),
        ACCOUNT_STATEMENT_ID("accountStatement", "accountStatementID"),
        RECONCILE_STATUS("reconcileStatus", "reconcileStatus"),
        REMITTANCE_INFORMATION("remittanceInformation", "remittanceInformation"),
        ORDERING_PARTY_NAME("orderingPartyName", "orderingPartyName"),
        CREATED_AT("createdAt", "createdAt"),
        CREATED_BY("createdBy", "createdBy"),
        UPDATED_AT("updatedAt", "updatedAt"),
        UPDATED_BY("updatedBy", "updatedBy");

        public final String queryField;
        public final String selectCode;

        SelectFields(String queryField, String selectCode) {
            this.queryField = queryField;
            this.selectCode = selectCode;
        }

    }

    public enum SortingFields {
        ID,
        TYPE,
        CURRENCY,
        VALUE_DATE,
        TRANSACTION_DATE,
        STATEMENT_DATE,
        STATEMENT_ID,
        DIRECTION,
        AMOUNT,
        REFERENCE,
        DESCRIPTION,
        REMITTANCE_INFORMATION,
        ORDERING_PARTY_NAME,
        CREATED_AT,
        CREATED_BY,
        UPDATED_AT,
        UPDATED_BY
    }
}
