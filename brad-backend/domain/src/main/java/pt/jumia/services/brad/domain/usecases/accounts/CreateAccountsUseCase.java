package pt.jumia.services.brad.domain.usecases.accounts;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.enumerations.StatementSource;
import pt.jumia.services.brad.domain.exceptions.AlreadyExistsException;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.monitoring.AccountMonitoring;
import pt.jumia.services.brad.domain.repository.brad.AccountRepository;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;

import java.util.Objects;

@Component
@RequiredArgsConstructor
public class CreateAccountsUseCase {

    private final AccountRepository accountRepository;

    private final AccountMonitoring accountMonitoring;

    private final ReadCountriesUseCase readCountriesUseCase;

    private final ReadCurrenciesUseCase readCurrenciesUseCase;

    private final ReadAccountsUseCase readAccountsUseCase;

    public Account execute(Account account) throws AlreadyExistsException, NotFoundException,
            EntityErrorsException, DatabaseErrorsException {

        if (Objects.isNull(account)) {
            throw EntityErrorsException.createNullClassError(Account.class);
        }
        if (StringUtils.hasText(account.getAccountNumber()) && accountRepository.findByAccountNumber(account.getAccountNumber()).isPresent()){
            throw AlreadyExistsException
                    .createAlreadyExists(Account.class, account.getAccountNumber());
        }
        if (readAccountsUseCase.executeByNavReferenceAndCompanyID(account.getNavReference(),
                account.getCompanyID()).isPresent()){
            throw AlreadyExistsException
                    .createAlreadyExists(Account.class, "NavReference: " + account.getNavReference() +
                            ", CompanyID: " + account.getCompanyID());
        }

        if (StringUtils.hasText(account.getIsin()) && accountRepository.countByIsin(account.getIsin()) > 0) {
            throw AlreadyExistsException
                    .createAlreadyExists(Account.class, "ISIN: " + account.getIsin());
        }

        if (StringUtils.hasText(account.getContractId()) && accountRepository.countByContractId(account.getContractId()) > 0) {
            throw AlreadyExistsException
                    .createAlreadyExists(Account.class, "ContractID: " + account.getContractId());
        }

        Account createdAccount = accountRepository.upsert(
                account.toBuilder()
                .country(readCountriesUseCase.execute(account.getCountry().getCode()))
                .currency(readCurrenciesUseCase.execute(account.getCurrency().getCode()))
                .statementSource(StatementSource.valueOf(account.getStatementSource().getValue()))
                .build()
        );
        this.accountMonitoring.recordAccountCreation("ng");
        return createdAccount;
    }
}
