package pt.jumia.services.brad.domain.usecases.bale;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ViewEntity;

import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCase;

import pt.jumia.services.brad.domain.usecases.executionlogs.CreateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.ReadExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.UpdateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class ReadBaleUseCase {

    private final BaleRepository baleRepository;
    private final BradBaleRepository bradBaleRepository;

    private final SyncBradBaleUseCase syncBradBaleUseCase;
    private final CreateExecutionLogsUseCase createExecutionLogsUseCase;
    private final UpdateExecutionLogsUseCase updateExecutionLogsUseCase;
    private final ReadExecutionLogsUseCase readExecutionLogsUseCase;
    private final ReadViewEntityUseCase readViewEntityUseCase;
    // TODO: Remove complex batch processing logic - now handled by Spring Batch
    // private final BatchProcessingConfig batchProcessingConfig;
    // private final BaleStreamingOrchestrator streamingOrchestrator;

    public List<Bale> execute(List<ViewEntity> baleViewEntities, boolean useStreaming) throws DatabaseErrorsException, EntityErrorsException {
        log.info("Starting consolidated bale processing - streaming mode: {}", useStreaming);
        
        try {
            // Diagnostic logging for execution flow and potential redundancy
            log.debug("Input validation - baleViewEntities provided: {}, size: {}",
                    !baleViewEntities.isEmpty(), baleViewEntities.size());
            
            List<ViewEntity> viewsToProcess = baleViewEntities.isEmpty() ?
                readViewEntityUseCase.execute(ViewEntity.EntityType.BALE) : baleViewEntities;
                
            log.debug("Processing path decision - useStreaming: {}, viewCount: {}", useStreaming, viewsToProcess.size());

            if (useStreaming) {
                log.info("Streaming mode requested but streaming orchestrator not available, falling back to traditional processing");
            }
            log.info("Using traditional processing for {} view entities", viewsToProcess.size());
            return fetchAllBales(viewsToProcess, null);
            
        } catch (Exception e) {
            log.error("Error in consolidated bale processing: {}", e.getMessage(), e);
            throw new RuntimeException("Consolidated bale processing failed", e);
        }
    }

    public Integer executeLastBaleOfOffset(Long baleViewEntityId, Integer offset)
            throws NotFoundException, EntityErrorsException {
        ViewEntity baleViewEntity = readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE);
        return baleRepository.findLastBaleWithOffset(baleViewEntity, offset);
    }

    public List<Bale> executeAllLastBaleOfOffset(Long baleViewEntityId, Integer offset)
            throws NotFoundException, EntityErrorsException {
        ViewEntity baleViewEntity = readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE);
        return baleRepository.findAllLastBaleWithOffset(baleViewEntity, offset);
    }

    public void executeByEntryNo(Integer entryNo) {
        CompletableFuture.runAsync(() -> {
            try {
                this.fetchAllBales(List.of(), entryNo);
            } catch (EntityErrorsException e) {
                log.error("Bale sync: Error fetching bales by entry no: {} Exception: {}", entryNo, ExceptionUtils.getStackTrace(e));
            }
        });
    }

    public void executeWithEntryNoInBaleView(Integer entryNo, Long baleViewEntityId) {
        CompletableFuture.runAsync(() -> {
            try {
                ViewEntity baleViewEntity = readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE);
                this.fetchAllBales(List.of(baleViewEntity), entryNo);
            } catch (EntityErrorsException | NotFoundException e) {
                log.error("Bale sync: Error fetching bales by entry no in bale view: {}", ExceptionUtils.getStackTrace(e));
            }
            });
    }

    public void executeWithBaleViewIds(List<Integer> baleViewIds) {
        CompletableFuture.runAsync(() -> {
            try {
                List<ViewEntity> baleViewEntities = baleViewIds.stream().map(id -> {
                    try {
                        return readViewEntityUseCase.execute(id, ViewEntity.EntityType.BALE);
                    } catch (NotFoundException e) {
                        log.error("Bale sync: Bale view entity with id {} not found. Exception: {}", id, ExceptionUtils.getStackTrace(e));
                        throw new RuntimeException(e);
                    }
                }).collect(Collectors.toList());

                if (baleViewEntities.isEmpty()) {
                    log.info("Bale sync: No bale view entities found for the provided bale view ids: {}", baleViewIds);
                    return;
                }

                this.fetchAllBales(baleViewEntities, null);
            } catch (EntityErrorsException e) {
                log.error("Bale sync: Error fetching bales by bale view ids. Exception: {}", ExceptionUtils.getStackTrace(e));
            }
        });
    }

    public void executeStreamingByEntryNo(Integer entryNo) {
        log.info("Starting consolidated streaming bale processing for entry number: {}", entryNo);
        
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                List<ViewEntity> allBaleViews = readViewEntityUseCase.execute(ViewEntity.EntityType.BALE);

                log.info("Streaming orchestrator not available, using traditional processing");
                fetchAllBales(allBaleViews, entryNo);

            } catch (Exception e) {
                log.error("Error in consolidated streaming bale processing for entry no: {}", entryNo, e);
                throw new RuntimeException("Streaming processing failed", e);
            }
        });

        try {
            future.get();
            log.info("Consolidated streaming bale processing completed for entry number: {}", entryNo);
        } catch (Exception e) {
            log.error("Consolidated streaming bale processing failed for entry number: {}", entryNo, e);
            throw new RuntimeException("Async processing failed", e);
        }
    }

    public void executeStreamingWithEntryNoInBaleView(Long baleViewEntityId, Integer entryNo) {
        log.info("Starting consolidated streaming processing for bale view: {}, entry: {}", baleViewEntityId, entryNo);
        
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                ViewEntity baleViewEntity = readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE);

                log.info("Streaming orchestrator not available, using traditional processing");
                fetchAllBales(List.of(baleViewEntity), entryNo);

            } catch (Exception e) {
                log.error("Error in consolidated streaming processing for bale view: {}", baleViewEntityId, e);
                throw new RuntimeException("Streaming processing failed", e);
            }
        });

        try {
            future.get();
            log.info("Consolidated streaming processing completed for bale view: {}", baleViewEntityId);
        } catch (Exception e) {
            log.error("Consolidated streaming processing failed for bale view: {}", baleViewEntityId, e);
            throw new RuntimeException("Async processing failed", e);
        }
    }

    public void executeStreamingWithBaleViewIds(List<Long> baleViewIds) {
        if (baleViewIds == null || baleViewIds.isEmpty()) {
            log.warn("No bale view IDs provided for consolidated streaming processing");
            return;
        }

        log.info("Starting consolidated streaming processing for {} bale view IDs", baleViewIds.size());
        
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                List<ViewEntity> baleViewEntities = baleViewIds.stream()
                        .map(id -> {
                            try {
                                return readViewEntityUseCase.execute(id, ViewEntity.EntityType.BALE);
                            } catch (Exception e) {
                                log.error("Bale view entity with id {} not found", id, e);
                                return null;
                            }
                        })
                        .filter(entity -> entity != null)
                        .toList();

                if (baleViewEntities.isEmpty()) {
                    log.warn("No valid bale view entities found for provided IDs: {}", baleViewIds);
                    return;
                }

                // TODO: Remove streaming orchestrator logic
                // if (streamingOrchestrator != null) {
                //     processWithStreamingOrchestrator(baleViewEntities, null);
                // } else {
                    log.info("Streaming orchestrator not available, using traditional processing");
                    fetchAllBales(baleViewEntities, null);
                // }
                
            } catch (Exception e) {
                log.error("Error in consolidated streaming processing for bale view IDs: {}", baleViewIds, e);
                throw new RuntimeException("Streaming processing failed", e);
            }
        });

        try {
            future.get();
            log.info("Consolidated streaming processing completed for bale view IDs: {}", baleViewIds);
        } catch (Exception e) {
            log.error("Consolidated streaming processing failed for bale view IDs: {}", baleViewIds, e);
            throw new RuntimeException("Async processing failed", e);
        }
    }

    private List<Bale> fetchAllBales(List<ViewEntity> baleViewEntities, Integer startEntryNo) throws EntityErrorsException {
        log.debug("Bale sync: Starting bale synchronization process for Bale view entities provided: {}", baleViewEntities);

        if (baleViewEntities.isEmpty()) {
            log.info("Bale sync: No bale view entities provided, fetching all bale view entities.");
            baleViewEntities = readViewEntityUseCase.execute(ViewEntity.EntityType.BALE);
        }

        log.info("Bale sync: {} bale view entities found. Details: {}", baleViewEntities.size(), baleViewEntities);
        List<Bale> totalBales = new ArrayList<>();

        baleViewEntities.forEach(baleViewEntity -> {
            String companyId = null;
            try {
                log.info("Bale sync: Fetching company for the Bale view: {}", baleViewEntity.getViewName());
                companyId = baleRepository.fetchCompanyId(baleViewEntity, false);
                log.debug("Bale sync: Creating partition for company: {}, for bale view entity: {}", companyId, baleViewEntity);
                this.bradBaleRepository.createPartition(companyId);
            } catch (Exception ignored) {
                log.warn("Bale sync: Ignoring company creation");
            }
            try {
                ExecutionLog newExecutionLog = ExecutionLog.builder()
                        .logType(ExecutionLog.ExecutionLogType.BALE)
                        .logStatus(ExecutionLog.ExecutionLogStatus.STARTED)
                        .build();
                ExecutionLog createdExecutionLog = createExecutionLogsUseCase.execute(newExecutionLog);
                log.info("Bale sync: Execution log created: {}", createdExecutionLog.getId());
                Optional<Bale> lastBale = bradBaleRepository.findLastBaleInBradOfCompanyId(companyId);
                Integer entryNo = Objects.isNull(startEntryNo) ?
                        lastBale.map(Bale::getEntryNo).orElse(null) :
                        startEntryNo;
                log.info("Bale sync: execution id {} : First entry number: {}. Fetching bale list.",
                        createdExecutionLog.getId(), entryNo);

                List<Bale> baleList = new ArrayList<>(baleRepository.findAll(entryNo,
                        baleViewEntity,
                        false,
                        createdExecutionLog));

                if (baleList.isEmpty()) {
                    log.info("Bale sync: execution id {}: Bale list is empty.", createdExecutionLog.getId());
                    try {
                        ExecutionLog executionLog = readExecutionLogsUseCase.execute(createdExecutionLog.getId());
                        updateExecutionLogsUseCase.execute(executionLog.toBuilder()
                                .logStatus(ExecutionLog.ExecutionLogStatus.EMPTY)
                                .build());
                    } catch (Exception e) {
                        log.info("Bale sync: Error updating execution log: {}", ExceptionUtils.getStackTrace(e));
                    }
                } else {
                    log.info("Bale sync: execution id {}: Processing Bale List - total items: {} Details: {}",
                            createdExecutionLog.getId(), baleList.size(), baleList);
                    ExecutionLog updatedExecutionLog = readExecutionLogsUseCase.execute(createdExecutionLog.getId());
                    totalBales.addAll(syncBradBaleUseCase.execute(baleList, updatedExecutionLog));
                }

            } catch (DatabaseErrorsException | EntityErrorsException | ParseException e) {
                log.error("Bale sync: Error fetching bales: {}", ExceptionUtils.getStackTrace(e));
                throw new RuntimeException(e);
            } catch (NotFoundException e) {
                log.error("Bale sync: Error creating execution log: {}", ExceptionUtils.getStackTrace(e));
            }
        });
        log.info("Bale sync: Total bales processed: {}. Details: {}", totalBales.size(), totalBales);
        return totalBales;
    }

    // TODO: Batch processing method removed - now handled by Spring Batch

    // TODO: Memory threshold method removed - now handled by Spring Batch

    // TODO: Streaming orchestrator method removed - now handled by Spring Batch

    // TODO: Remove streaming methods - replaced by Spring Batch
    /*
    private BaleProcessor createSimpleBradSyncProcessor() {
        return (bales, executionLog) -> {
            try {
                List<Bale> processedBales = syncBradBaleUseCase.execute(bales, executionLog);
                log.debug("Successfully processed {} bales in streaming batch", processedBales.size());
                
                return BaleProcessingResult.success(processedBales.size());
            } catch (Exception e) {
                log.error("Error processing bale batch in streaming mode: {}", e.getMessage(), e);
                
                return BaleProcessingResult.withErrors(0, bales.size(), true, e.getMessage());
            }
        };
    }


    public int processBalesInStreams(List<ViewEntity> baleViewEntities, BaleProcessor processor, StreamingConfig config) 
            throws EntityErrorsException {
        return processBalesInStreams(baleViewEntities, null, processor, config);
    }

    public int processBalesInStreams(List<ViewEntity> baleViewEntities, Integer startEntryNo, 
                                   BaleProcessor processor, StreamingConfig config) throws EntityErrorsException {
        log.debug("Bale sync: Starting streaming bale synchronization process for {} view entities", baleViewEntities.size());

        if (baleViewEntities.isEmpty()) {
            log.info("Bale sync: No bale view entities provided, fetching all bale view entities.");
            baleViewEntities = readViewEntityUseCase.execute(ViewEntity.EntityType.BALE);
        }

        log.info("Bale sync: {} bale view entities found for streaming processing. Details: {}", 
                baleViewEntities.size(), baleViewEntities);
        
        int totalProcessed = 0;

        for (ViewEntity baleViewEntity : baleViewEntities) {
            try {
                List<Bale> bales = fetchAllBales(List.of(baleViewEntity), startEntryNo);
                totalProcessed += bales.size();
                
                log.info("Processed {} bales for view entity: {}", bales.size(), baleViewEntity.getViewName());
            } catch (Exception e) {
                log.error("Bale sync: Error processing view entity {} in streaming mode: {}", 
                         baleViewEntity.getViewName(), e.getMessage(), e);
            }
        }

        log.info("Bale sync: Streaming processing completed. Total bales processed: {}", totalProcessed);
        return totalProcessed;
    }
    */
}
