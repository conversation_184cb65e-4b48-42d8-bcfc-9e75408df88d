package pt.jumia.services.brad.domain.usecases.processconvertfiles.convertfile;

import com.prowidesoftware.swift.model.field.Field60F;
import com.prowidesoftware.swift.model.field.Field61;
import com.prowidesoftware.swift.model.field.Field86;
import com.prowidesoftware.swift.model.mt.mt9xx.MT940;
import java.time.LocalDate;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.enumerations.AccountStatementFlow;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.utils.DateParser;

import java.text.ParseException;
import java.time.Year;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.List;


public class ConvertMT940File implements ConvertFile<MT940> {

    public static final String SFTP_SYSTEM_SCAN = "SFTP System Scan";

    DateTimeFormatter swiftTransactionDateFormatter = new DateTimeFormatterBuilder()
            .appendPattern("MMdd")
            .parseDefaulting(ChronoField.YEAR, Year.now().getValue())
            .toFormatter();

    @Override
    public AccountStatement buildStatementAccountInfo(MT940 file) throws ParseException {
        return AccountStatement.builder()
                .currency(Currency.builder().code(file.getField60F().getCurrency()).build())
                .statementId(file.getField28C().getStatementNumber())
                .initialDate(DateParser.parseToLocalDate(file.getField60F().getDate()))
                .initialDirection(Direction.fromString(file.getField60F().getDCMark()))
                .initialAmount(file.getField60F().getAmountAsBigDecimal())
                .finalDate(DateParser.parseToLocalDate(file.getField62F().getDate()))
                .finalDirection(Direction.fromString(file.getField62F().getDCMark()))
                .finalAmount(file.getField62F().getAmountAsBigDecimal())
                .status(AccountStatementStatus.OPEN)
                .flow(AccountStatementFlow.AUTOMATIC)
                .createdBy(SFTP_SYSTEM_SCAN)
                .updatedBy(SFTP_SYSTEM_SCAN)
                .build();
    }

    @Override
    public List<Transaction> buildTransactionListAccountInfo(MT940 file) throws ParseException {
        List<Transaction> transactions = new ArrayList<>();

        List<Field61> field61List = file.getField61();
        Field60F field60 = file.getField60F();
        List<Field86> field86List = file.getField86();

        for (int i = 0; i < field61List.size(); i++) {
            Field61 field61 = field61List.get(i);
            Field86 field86 = field86List.get(i);
            Transaction transaction = buildTransactionAccountInfo(field61, field60, field86);
            transactions.add(transaction);
        }

        return transactions;
    }

    @Override
    public String buildAccountNumber(MT940 file) {
        return file.getField25().getAccount();
    }


    private Transaction buildTransactionAccountInfo(Field61 field61, Field60F field60, Field86 field86) throws ParseException {
        Currency currency = Currency.builder().code(field60.getCurrency()).build();
        return Transaction.builder()
                .type("TR")// TODO this filed is temporary will be removed but must be populated by now
                .currency(currency)
                .valueDate(DateParser.parseToLocalDate(field61.getValueDate()))
                .statementDate(DateParser.parseToLocalDate(field61.getDate()))
                .transactionDate(LocalDate.parse(field61.getEntryDate(), swiftTransactionDateFormatter))
                .direction(Direction.fromString(field61.getDebitCreditMark()))
                .amount(field61.getAmountAsBigDecimal())
                .reference(field61.getReferenceForTheAccountOwner())
                .description(field86.getNarrative())
                .createdBy(SFTP_SYSTEM_SCAN)
                .updatedBy(SFTP_SYSTEM_SCAN)
                .build();
    }
}
