package pt.jumia.services.brad.domain.usecases.viewentity;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.ViewEntityRepository;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class ReadViewEntityUseCase {

    private final ViewEntityRepository viewEntityRepository;

    public List<ViewEntity> execute(ViewEntity.EntityType entityType) throws EntityErrorsException {
        return viewEntityRepository.findAll(entityType);
    }

    public ViewEntity execute(long id, ViewEntity.EntityType entityType) throws NotFoundException {
        ViewEntity viewEntity = viewEntityRepository.findById(id).orElseThrow(() ->
                NotFoundException.createNotFound(ViewEntity.class, id));

        if (viewEntity.getEntityType() != entityType) {
            throw NotFoundException.createNotFound(ViewEntity.class, id);
        }
        log.debug("Successfully Fetched ViewEntity with ID: {}. Details: {}", id, viewEntity);
        return viewEntity;
    }
}
