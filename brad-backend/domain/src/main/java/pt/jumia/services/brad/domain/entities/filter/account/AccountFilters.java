package pt.jumia.services.brad.domain.entities.filter.account;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Account.StatementPeriodicity;
import pt.jumia.services.brad.domain.entities.account.Account.TroubleShooting;
import pt.jumia.services.brad.domain.entities.filter.shared.BaseFilters;
import pt.jumia.services.brad.domain.enumerations.StatementSource;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountFilters extends BaseFilters {

    private String companyID;
    private List<Long> countryCodes;
    private String navReference;
    private String beneficiaryName;
    private String beneficiaryAddress;
    private String iban;
    private String accountNumber;
    private String accountName;
    private String swiftCode;
    private String bankRoutingCode;
    private String sortCode;
    private String branchCode;
    private String rib;
    private String partner;
    private String phoneNumber;
    private TroubleShooting troubleshooting;
    private List<String> currencyCodes;
    private List<Account.Type> types;
    private List<Account.SubType> subTypes;
    private List<Account.Status> status;
    private StatementSource statementSource;
    private List<StatementPeriodicity> statementPeriodicity;
    private LocalDate lastProcessedStatementDateStart;
    private LocalDate lastProcessedStatementDateEnd;
    private LocalDateTime createdAtStart;
    private LocalDateTime createdAtEnd;
    private String investmentId;
    private boolean isNegativeBalance;

    public Map<String, ?> getAsMap() {
        HashMap<String, Object> map = new HashMap<>();

        if (!Objects.isNull(this.companyID)) {
            map.put("companyID", this.companyID);
        }
        if (!Objects.isNull(this.countryCodes)) {
            map.put("country", this.countryCodes);
        }
        if (!Objects.isNull(this.navReference)) {
            map.put("navReference", this.navReference);
        }
        if (!Objects.isNull(this.beneficiaryName)) {
            map.put("beneficiaryName", this.beneficiaryName);
        }
        if (!Objects.isNull(this.beneficiaryAddress)) {
            map.put("beneficiaryAddress", this.beneficiaryAddress);
        }
        if (!Objects.isNull(this.iban)) {
            map.put("iban", this.iban);
        }
        if (!Objects.isNull(this.accountNumber)) {
            map.put("accountNumber", this.accountNumber);
        }
        if (!Objects.isNull(this.accountName)) {
            map.put("accountName", this.accountName);
        }
        if (!Objects.isNull(this.swiftCode)) {
            map.put("swiftCode", this.swiftCode);
        }
        if (!Objects.isNull(this.bankRoutingCode)) {
            map.put("bankRoutingCode", this.bankRoutingCode);
        }
        if (!Objects.isNull(this.sortCode)) {
            map.put("sortCode", this.sortCode);
        }
        if (!Objects.isNull(this.branchCode)) {
            map.put("branchCode", this.branchCode);
        }
        if (!Objects.isNull(this.rib)) {
            map.put("rib", this.rib);
        }
        if (!Objects.isNull(this.partner)) {
            map.put("partner", this.partner);
        }
        if (!Objects.isNull(this.phoneNumber)) {
            map.put("phoneNumber", this.phoneNumber);
        }
        if (!Objects.isNull(this.troubleshooting)) {
            map.put("troubleshooting", this.troubleshooting);
        }
        if (!Objects.isNull(this.types)) {
            map.put("types", this.types);
        }
        if (!Objects.isNull(this.status)) {
            map.put("status", this.status);
        }
        if (!Objects.isNull(this.currencyCodes)) {
            map.put("currency", this.currencyCodes);
        }
        if (!Objects.isNull(this.statementSource)) {
            map.put("statementSource", this.statementSource);
        }
        if (!Objects.isNull(this.statementPeriodicity)) {
            map.put("statementPeriodicity", this.statementPeriodicity);
        }
        if (!Objects.isNull(this.lastProcessedStatementDateStart)) {
            map.put("lastProcessedStatementDateStart", this.lastProcessedStatementDateStart);
        }
        if (!Objects.isNull(this.lastProcessedStatementDateEnd)) {
            map.put("lastProcessedStatementDateEnd", this.lastProcessedStatementDateEnd);
        }
        if (!Objects.isNull(this.createdAtStart)) {
            map.put("createdAtStart", this.createdAtStart);
        }
        if (!Objects.isNull(this.createdAtEnd)) {
            map.put("createdAtEnd", this.createdAtEnd);
        }
        if (!Objects.isNull(this.investmentId)) {
            map.put("investmentId", this.investmentId);
        }

        map.put("isNegativeBalance", this.isNegativeBalance);

        return map;
    }
}
