package pt.jumia.services.brad.domain;


public final class Permissions {

    private Permissions() {
    }

    public static final String BRAD_CAN_ACCESS = "brad_can_access";

    //Accounts
    public static final String BRAD_ACCESS_ACCOUNTS = "brad_access_bank_accounts";
    public static final String BRAD_DOWNLOAD_ACCOUNTS_CSV = "brad_download_bank_accounts_csv";
    public static final String BRAD_MANAGE_ACCOUNTS = "brad_manage_bank_accounts";
    public static final String BRAD_DELETE_ACCOUNTS = "brad_delete_bank_accounts";

    //Countries
    public static final String BRAD_ACCESS_COUNTRIES = "brad_access_countries";
    public static final String BRAD_ADMIN_MANAGE_COUNTRIES = "brad_admin_manage_countries";


    //Reconciliation list
    public static final String BRAD_ACCESS_RECONCILIATION = "brad_access_reconciliation";
    public static final String BRAD_MANAGE_RECONCILIATION = "brad_manage_reconciliation";
    public static final String BRAD_APPROVE_RECONCILIATIONS = "brad_approve_reconciliations";
    public static final String BRAD_UNMATCH_RECONCILIATIONS = "brad_unmatch_reconciliations";
    public static final String BRAD_EXPORT_RECONCILIATIONS = "brad_export_reconciliations";

    //Scheduler
    public static final String BRAD_ACCESS_SCHEDULER = "brad_access_scheduler";
    public static final String BRAD_MANAGE_SCHEDULER = "brad_manage_scheduler";

    //Api Log
    public static final String BRAD_ACCESS_API_LOG = "brad_access_api_log";

    //Currencies
    public static final String BRAD_ACCESS_CURRENCIES = "brad_access_currencies";
    public static final String BRAD_ADMIN_MANAGE_CURRENCIES = "brad_admin_manage_currencies";

    // Statements
    public static final String BRAD_UPLOAD_STATEMENTS = "brad_upload_statements";
    public static final String BRAD_ACCESS_STATEMENTS = "brad_access_statements";
    public static final String BRAD_EXPORT_STATEMENTS = "brad_export_statements";
    public static final String BRAD_DISCARD_STATEMENTS = "brad_discard_statements";
    public static final String BRAD_DISCARD_IMPORTED_STATEMENTS = "brad_can_discard_imported_statements";
    public static final String BRAD_CHANGE_STATEMENTS = "brad_can_change_statement_status";
    public static final String BRAD_RETRY_STATEMENT = "brad_retry_statement";

    // Troubleshooting
    public static final String BRAD_ACCESS_TROUBLESHOOTING = "brad_access_troubleshooting";

    // FX Rates
    public static final String BRAD_ACCESS_FX_RATES = "brad_access_fx_rates";

    // Execution Log
    public static final String BRAD_ACCESS_EXECUTION_LOG = "brad_access_execution_log";

    // Execution Log
    public static final String BRAD_ACCESS_EXPORT_LOG = "brad_access_export_log";

    //Bale View Entity
    public static final String BRAD_MANAGE_VIEW_ENTITIES = "brad_manage_view_entities";

    //Threshold
    public static final String BRAD_MANAGE_THRESHOLDS = "brad_manage_thresholds";

    // Statement files
    public static final String BRAD_STATEMENT_FILES_ACCESS = "brad_bank_statement_files_access";
    public static final String BRAD_SCAN_SFTP_FOLDER = "brad_scan_sftp_folder";
    public static final String BRAD_DOWNLOAD_STATEMENT_FILE = "brad_download_bank_statement_file";

    //Setting resources
    public static final String SETTING_EDIT = "brad_setting_edit";
    public static final String SETTING_ACCESS = "brad_setting_access";

    //Contacts
    public static final String BRAD_DOWNLOAD_CONTACTS_CSV = "brad_download_contacts_csv";

    //Users
    public static final String BRAD_DOWNLOAD_USERS_CSV = "brad_download_users_csv";
}
