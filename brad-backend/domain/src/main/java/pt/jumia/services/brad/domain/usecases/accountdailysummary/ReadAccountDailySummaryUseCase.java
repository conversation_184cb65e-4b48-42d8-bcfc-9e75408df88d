package pt.jumia.services.brad.domain.usecases.accountdailysummary;

import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.account.AccountDailySummary;
import pt.jumia.services.brad.domain.entities.dtos.GroupedAccountDailySummaryDto;
import pt.jumia.services.brad.domain.entities.dtos.StackedCashPositionDto;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashEvolutionFilters;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashPositionFilters;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.AccountDailySummaryRepository;

@Component
@RequiredArgsConstructor
public class ReadAccountDailySummaryUseCase {

    private final AccountDailySummaryRepository accountDailySummaryRepository;

    public AccountDailySummary executeOldestMissingFxRate(Long accountId) {

        return accountDailySummaryRepository.findOldestMissingFxRate(accountId).orElse(null);

    }

    public AccountDailySummary executeLatestByAccountIdAndDate(Long accountId, LocalDate toDate) {

        return accountDailySummaryRepository.findLatestByAccountIdAndDate(accountId, toDate)
            .orElseThrow(() -> new NotFoundException("No summary exists for accountId: " + accountId));

    }

    public List<GroupedAccountDailySummaryDto> executeCashEvolution(CashEvolutionFilters evolutionFilters) {

        return accountDailySummaryRepository.findCashEvolution(evolutionFilters);

    }

    public List<GroupedAccountDailySummaryDto> executeCashPosition(CashPositionFilters positionFilters) {

        return accountDailySummaryRepository.findCashPosition(positionFilters);

    }

    public List<StackedCashPositionDto> executeCashPositionStacked(CashPositionFilters positionFilters) {

        return accountDailySummaryRepository.findCashPositionStacked(positionFilters);

    }

}
