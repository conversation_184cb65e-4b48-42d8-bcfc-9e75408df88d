package pt.jumia.services.brad.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.error.BaleProcessingErrorResult;

import java.util.List;
import java.util.Map;

@Getter
@Builder
@AllArgsConstructor
public class BaleAccountFetchResult {
    private final Map<Bale, Account> successfulMappings;
    private final List<BaleProcessingErrorResult> failures;

    public int getSuccessCount() {
        return successfulMappings.size();
    }

    public int getFailureCount() {
        return failures.size();
    }

    public int getTotalCount() {
        return getSuccessCount() + getFailureCount();
    }

    public boolean hasFailures() {
        return !failures.isEmpty();
    }

    public boolean isCompleteSuccess() {
        return failures.isEmpty() && !successfulMappings.isEmpty();
    }

    public boolean isPartialSuccess() {
        return !successfulMappings.isEmpty() && !failures.isEmpty();
    }
}
