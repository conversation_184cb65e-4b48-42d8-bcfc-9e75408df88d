package pt.jumia.services.brad.domain;

import pt.jumia.services.brad.domain.entities.dtos.AccountTroubleshootingDto;

import java.io.IOException;
import java.util.List;

/**
 * Represents all the network requests that the system may need to do to external services
 */
public interface NetworkRequester {

    void sendEmailForAccountsInTroubleshooting(List<AccountTroubleshootingDto> accounts, String emailAddress) throws IOException;

}
