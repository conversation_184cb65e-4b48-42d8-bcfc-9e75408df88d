package pt.jumia.services.brad.domain.entities.error;

import lombok.extern.slf4j.Slf4j;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;

import java.sql.SQLException;
import java.sql.SQLTimeoutException;
import java.sql.SQLTransientException;
import java.util.Locale;

@Slf4j
public class BaleErrorClassifier {
    
    private static final String ERROR_NOT_FOUND = "not found";
    
    public enum ErrorCategory {
        RECOVERABLE,
        CRITICAL,
        DATA_ERROR
    }

    public static boolean isRecoverableError(Throwable error) {
        return categorizeError(error) == ErrorCategory.RECOVERABLE;
    }

    public static boolean isCriticalError(Throwable error) {
        return categorizeError(error) == ErrorCategory.CRITICAL;
    }

    public static ErrorCategory categorizeError(Throwable error) {
        if (error == null) {
            return ErrorCategory.DATA_ERROR;
        }

        Throwable rootCause = getRootCause(error);
        String errorMessage = rootCause.getMessage() != null ? rootCause.getMessage().toLowerCase(Locale.ROOT) : "";

        if (isCriticalErrorType(rootCause, errorMessage)) {
            log.debug("Classified error as CRITICAL: {}", rootCause.getClass().getSimpleName());
            return ErrorCategory.CRITICAL;
        }

        if (isRecoverableErrorType(rootCause, errorMessage)) {
            log.debug("Classified error as RECOVERABLE: {}", rootCause.getClass().getSimpleName());
            return ErrorCategory.RECOVERABLE;
        }

        log.debug("Classified error as DATA_ERROR (default): {}", rootCause.getClass().getSimpleName());
        return ErrorCategory.DATA_ERROR;
    }

    private static boolean isCriticalErrorType(Throwable error, String errorMessage) {
        if (error instanceof SQLException) {
            SQLException sqlEx = (SQLException) error;

            return sqlEx instanceof SQLTimeoutException 
                   || sqlEx instanceof SQLTransientException
                   || errorMessage.contains("connection")
                   || errorMessage.contains("timeout")
                   || errorMessage.contains("deadlock")
                   || errorMessage.contains("lock wait timeout");
        }

        if (error instanceof DatabaseErrorsException) {
            return errorMessage.contains("connection")
                   || errorMessage.contains("timeout")
                   || errorMessage.contains("database")
                   || errorMessage.contains("schema");
        }

        if (error instanceof OutOfMemoryError
            || error instanceof StackOverflowError
            || errorMessage.contains("out of memory")
            || errorMessage.contains("heap space")) {
            return true;
        }

        if (errorMessage.contains("authentication")
            || errorMessage.contains("authorization")
            || errorMessage.contains("access denied")
            || errorMessage.contains("permission")) {
            return true;
        }

        if (error instanceof RuntimeException) {
            if (errorMessage.contains("connection")
                || errorMessage.contains("timeout")
                || errorMessage.contains("deadlock")) {
                return true;
            }
        }

        return false;
    }

    private static boolean isRecoverableErrorType(Throwable error, String errorMessage) {
        if (error instanceof NotFoundException) {
            return errorMessage.contains("account")
                   || errorMessage.contains("currency")
                   || errorMessage.contains("fx")
                   || errorMessage.contains("rate");
        }

        if (error instanceof EntityErrorsException) {
            return errorMessage.contains(ERROR_NOT_FOUND)
                   || errorMessage.contains("account")
                   || errorMessage.contains("currency");
        }

        if (errorMessage.contains("account") && errorMessage.contains(ERROR_NOT_FOUND)) {
            return true;
        }
        
        if (errorMessage.contains("currency") && errorMessage.contains(ERROR_NOT_FOUND)) {
            return true;
        }

        if (errorMessage.contains("fx rate") && errorMessage.contains(ERROR_NOT_FOUND)) {
            return true;
        }

        if (errorMessage.contains("invalid amount")
            || errorMessage.contains("invalid date")
            || errorMessage.contains("parsing")) {
            return true;
        }

        return false;
    }

    @SuppressWarnings("PMD.CompareObjectsWithEquals") // Reference comparison is intentional to detect circular references
    private static Throwable getRootCause(Throwable error) {
        Throwable cause = error;
        while (cause.getCause() != null && cause.getCause() != cause) {
            cause = cause.getCause();
        }
        return cause;
    }

    public static String createErrorContext(Throwable error, String context) {
        ErrorCategory category = categorizeError(error);
        Throwable rootCause = getRootCause(error);
        
        return String.format("[%s] %s - %s: %s", 
            category.name(),
            context,
            rootCause.getClass().getSimpleName(),
            rootCause.getMessage());
    }
}
