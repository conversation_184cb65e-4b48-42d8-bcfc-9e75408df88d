package pt.jumia.services.brad.domain.usecases.processconvertfiles.abstractmxadapter;

import com.prowidesoftware.swift.model.mx.AbstractMX;
import com.prowidesoftware.swift.model.mx.MxCamt05300109;
import com.prowidesoftware.swift.model.mx.dic.CashBalance8;
import com.prowidesoftware.swift.model.mx.dic.ReportEntry11;
import pt.jumia.services.brad.domain.enumerations.Direction;
import java.math.BigDecimal;
import java.util.List;

import static com.prowidesoftware.swift.model.mx.dic.BalanceType12Code.CLBD;
import static com.prowidesoftware.swift.model.mx.dic.BalanceType12Code.OPBD;

public class MxCamt05300109File implements AbstractMXFile {


    private MxCamt05300109 mxCamt05300109;
    private List<ReportEntry11> reportEntryList;
    private final int initialBalanceIndex;
    private final int finalBalanceIndex;

    int index = -1;

    public MxCamt05300109File(AbstractMX abstractMX) {
        if (abstractMX instanceof MxCamt05300109) {
            mxCamt05300109 = new MxCamt05300109(abstractMX.message());
        } else {
            throw new IllegalArgumentException("wrong file format");
        }
        reportEntryList = mxCamt05300109.getBkToCstmrStmt().getStmt().get(0).getNtry();
        initialBalanceIndex = getInitialBalanceIndex();
        finalBalanceIndex = getFinalBalanceIndex();
    }

    @Override
    public String getCurrency() {
        return mxCamt05300109.getBkToCstmrStmt().getStmt().get(0).getAcct().getCcy();
    }

    @Override
    public String getStatementId() {
        return mxCamt05300109.getBkToCstmrStmt().getStmt().get(0).getId();
    }

    @Override
    public String getInitialDate() {
        return mxCamt05300109.getBkToCstmrStmt().getStmt().get(0).getBal().get(initialBalanceIndex).getDt().getDt().toString();
    }

    @Override
    public Direction getInitialDirection() {
        return Direction.getTranslatedDirectionForMXFile(mxCamt05300109.getBkToCstmrStmt().getStmt().get(0)
                .getBal().get(initialBalanceIndex).getCdtDbtInd());
    }

    @Override
    public BigDecimal getInitialAmount() {
        return mxCamt05300109.getBkToCstmrStmt().getStmt().get(0).getBal().get(initialBalanceIndex).getAmt().getValue();
    }

    @Override
    public String getFinalDate() {
        return mxCamt05300109.getBkToCstmrStmt().getStmt().get(0).getBal().get(finalBalanceIndex).getDt().getDt().toString();
    }

    @Override
    public Direction getFinalDirection() {
        return Direction.getTranslatedDirectionForMXFile(mxCamt05300109.getBkToCstmrStmt().getStmt().get(0).getBal()
                .get(finalBalanceIndex).getCdtDbtInd());
    }

    @Override
    public BigDecimal getFinalAmount() {
        return mxCamt05300109.getBkToCstmrStmt().getStmt().get(0).getBal().get(finalBalanceIndex).getAmt().getValue();
    }

    @Override
    public String getNtryValueDate() {
        return reportEntryList.get(index).getValDt().getDt().toString();
    }

    @Override
    public String getNtryStatementDate() {
        return reportEntryList.get(index).getBookgDt().getDt().toString();
    }

    @Override
    public String getNtryTransactionDate() {
        return reportEntryList.get(index).getValDt().getDt().toString();
    }

    @Override
    public Direction getNtryDirection() {
        return Direction.getTranslatedDirectionForMXFile(reportEntryList.get(index).getCdtDbtInd());
    }

    @Override
    public BigDecimal getNtryAmount() {
        return reportEntryList.get(index).getAmt().getValue();
    }

    @Override
    public String getNtryRefrence() {
        return reportEntryList.get(index).getAcctSvcrRef();
    }

    @Override
    public String getNtryDescription() {
        return reportEntryList.get(index).getBkTxCd().getPrtry().getCd();
    }

    @Override
    public String getNtryRemittanceInformation() {
        return null;
    }

    @Override
    public String getOrderingPartyName() {
        return null;
    }

    @Override
    public String getAccountNumber() {
        return mxCamt05300109.getBkToCstmrStmt().getStmt().get(0).getAcct().getId().getOthr().getId();
    }

    @Override
    public boolean hasNextNtry() {
        return ++index < reportEntryList.size();
    }

    private int getInitialBalanceIndex() {
        List<CashBalance8> cashBalance8s = mxCamt05300109.getBkToCstmrStmt().getStmt().get(0).getBal();
        for (int index = 0; index < cashBalance8s.size(); index++) {
            if (cashBalance8s.get(index).getTp().getCdOrPrtry().getCd().equals(OPBD.value())) {
                return index;
            }
        }
        throw new IllegalArgumentException("No initial balance index found");
    }


    private int getFinalBalanceIndex() {
        List<CashBalance8> cashBalance8s = mxCamt05300109.getBkToCstmrStmt().getStmt().get(0).getBal();
        for (int index = 0; index < cashBalance8s.size(); index++) {
            if (cashBalance8s.get(index).getTp().getCdOrPrtry().getCd().equals(CLBD.value())) {
                return index;
            }
        }
        throw new IllegalArgumentException("No final balance index found");
    }


}
