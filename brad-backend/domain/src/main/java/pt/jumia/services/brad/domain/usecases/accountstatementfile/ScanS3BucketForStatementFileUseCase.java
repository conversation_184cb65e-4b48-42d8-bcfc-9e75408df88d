package pt.jumia.services.brad.domain.usecases.accountstatementfile;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.brad.domain.FileStorageClient;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.SortingFields;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ExecutionLog.ExecutionLogStatus;
import pt.jumia.services.brad.domain.entities.ExecutionLog.SyncingError;
import pt.jumia.services.brad.domain.entities.dtos.AccountStatementFilesScanResponseDto;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementFileRepository;
import pt.jumia.services.brad.domain.repository.brad.ExecutionLogRepository;
import pt.jumia.services.brad.domain.usecases.FindExceptionRootCauseUseCase;
import pt.jumia.services.brad.domain.usecases.jobs.RunJobsUsecase;

@Component
@RequiredArgsConstructor
@Slf4j
public class ScanS3BucketForStatementFileUseCase {

    public static final int BATCH_SIZE = 1000;
    public static final String ACCOUNT_STATEMENTS_FILES_SCAN = "BankStatementFileScan";

    private final AccountStatementFileRepository accountStatementFileRepository;
    private final FileStorageClient fileStorageClient;
    private final RunJobsUsecase runJobsUsecase;
    private final ExecutionLogRepository executionLogRepository;
    private final ConsumeAccountStatementFileUseCase consumeAccountStatementFileUseCase;

    public void start() throws SchedulerException {

        runJobsUsecase.execute(ACCOUNT_STATEMENTS_FILES_SCAN);
    }

    public void execute(ExecutionLog executionLog) throws UserForbiddenException {
        log.info("Starting to scan for account statement files by execution log {}", executionLog.getId());

        int allFilesFound = 0;
        int modified = 0;
        int page = 0;
        List<SyncingError> syncingErrors = new ArrayList<>();

        AccountStatementFilesScanResponseDto response;
        String continuationToken = null;

        try {
            do {
                page++;
                log.info("Execution log: {}. Scanning page {}", executionLog.getId(), page);

                response = fileStorageClient.scanBucketPaginated(BATCH_SIZE, continuationToken);
                continuationToken = response.getNextPageToken();
                allFilesFound += response.getAccountStatementFiles().size();

                log.info("Execution log: {}. Page {}. Processing files.", executionLog.getId(), page);
                modified = processFiles(executionLog, response, modified, syncingErrors);
            }
            while (Objects.nonNull(continuationToken));
            if (syncingErrors.isEmpty()) {
                executionLogRepository.upsert(updateSuccessLog(executionLog, allFilesFound));
            } else {
                executionLogRepository.upsert(updateFailedLog(executionLog, syncingErrors, allFilesFound));
            }

            ScanS3BucketForStatementFileUseCase.log.info("Successfully scanned S3 bucket and got total of {} files with {} modified files",
                allFilesFound, modified);
        } catch (Exception e) {
            executionLogRepository.upsert(updateFailedLog(executionLog, syncingErrors, allFilesFound));
            throw e;
        }
    }

    private int processFiles(final ExecutionLog executionLog, final AccountStatementFilesScanResponseDto response, int modified,
        List<SyncingError> syncingErrors) {

        for (AccountStatementFile accountStatementFile : response.getAccountStatementFiles()) {
            try {
                log.info("Execution log: {}. Processing file {}", executionLog.getId(), accountStatementFile.getName());
                AccountStatementFile accountStatementFileToSave = accountStatementFile.toBuilder().executionLog(executionLog).build();

                List<AccountStatementFile> duplicateStatementFiles = getDuplicateStatementFiles(accountStatementFileToSave);
                log.info("Execution log: {}. File {}. Found {} duplicates",
                        executionLog.getId(), accountStatementFileToSave.getName(), duplicateStatementFiles.size());

                updateStatusAndSave(accountStatementFileToSave, duplicateStatementFiles);

                if (isModified(accountStatementFileToSave, duplicateStatementFiles)) {
                    log.info("Execution log: {}. File {}. Duplicates files found with modifications.",
                            executionLog.getId(), accountStatementFileToSave.getName());
                    modified++;
                }

            } catch (Exception exception) {
                log.info("Execution log: {}. Exception occurred while processing file {}. {}", executionLog.getId(),
                        accountStatementFile.getName(), ExceptionUtils.getStackTrace(exception));

                final SyncingError error = SyncingError.builder()
                    .errorDescription(FindExceptionRootCauseUseCase.findRootCause(exception).getMessage())
                    .build();
                syncingErrors.add(error);
            }
        }
        return modified;
    }

    private void updateStatusAndSave(final AccountStatementFile accountStatementFileToSave,
        final List<AccountStatementFile> duplicateStatementFiles) throws Exception {

        if (duplicateStatementFiles.isEmpty()) {

            AccountStatementFile savedFile = accountStatementFileRepository.upsert(accountStatementFileToSave);
            log.info("File {} saved with id {}. Processing it.", accountStatementFileToSave.getName(), savedFile.getId());
            consumeAccountStatementFileUseCase.execute(savedFile.getId(), null); // todo replace with a line to publish to kafka for async

        } else if (isModified(accountStatementFileToSave, duplicateStatementFiles)) {

            AccountStatementFile fileToSave = accountStatementFileToSave.toBuilder()
                .processingStatus(ProcessingStatus.DUPLICATED)
                .statusDescription(ProcessingStatus.DUPLICATED.getDescription())
                .build();
            log.info("File {} saved with differences. Changing its status to duplicated.",
                    accountStatementFileToSave.getName());
            accountStatementFileRepository.upsert(fileToSave);
        }
    }

    private List<AccountStatementFile> getDuplicateStatementFiles(final AccountStatementFile accountStatementFileToSave)
        throws EntityErrorsException {

        AccountStatementFileFilters filters = AccountStatementFileFilters.builder()
            .name(accountStatementFileToSave.getName())
            .build();
        AccountStatementFileSortFilters sortFilters = AccountStatementFileSortFilters.builder()
            .field(SortingFields.ID)
            .direction(OrderDirection.DESC)
            .build();

        return accountStatementFileRepository.findAll(filters, sortFilters, null);
    }

    private boolean isModified(final AccountStatementFile accountStatementFile, final List<AccountStatementFile> duplicateStatementFiles) {

        return !CollectionUtils.isEmpty(duplicateStatementFiles) && duplicateStatementFiles.stream()
            .noneMatch(v -> Objects.equals(v.getChecksum(), accountStatementFile.getChecksum()));
    }

    private static ExecutionLog updateFailedLog(final ExecutionLog executionLog, final List<SyncingError> syncingErrors,
        final int allFilesFound) {

        return executionLog.toBuilder()
            .logStatus(ExecutionLogStatus.ERROR)
            .errors(syncingErrors)
            .recordsAmount(allFilesFound)
            .executionEndTime(LocalDateTime.now(ZoneOffset.UTC))
            .build();
    }

    private static ExecutionLog updateSuccessLog(final ExecutionLog executionLog, final int allFilesFound) {

        return executionLog.toBuilder()
            .logStatus(ExecutionLogStatus.SYNCED)
            .recordsAmount(allFilesFound)
            .executionEndTime(LocalDateTime.now(ZoneOffset.UTC))
            .build();
    }

}
