package pt.jumia.services.brad.domain.usecases.accountstatement;

import java.text.MessageFormat;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.InvalidEntityException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.ReadAccountStatementFileUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.ReprocessFilesUseCase;

@Slf4j
@Component
@RequiredArgsConstructor
public class RetryAccountStatementUseCase {

    private final ReadAccountStatementUseCase readAccountStatementUseCase;

    private final RevalidateAccountStatementUseCase revalidateAccountStatementUseCase;

    private final ReadAccountStatementFileUseCase readAccountStatementFileUseCase;

    private final UpdateAccountStatementUseCase updateAccountStatementUseCase;

    private final DiscardAccountStatementUseCase discardAccountStatementUseCase;

    private final ReprocessFilesUseCase reprocessFilesUseCase;

    public String execute(final long statementId) throws NotFoundException, EntityErrorsException, DatabaseErrorsException {

        AccountStatement accountStatement = readAccountStatementUseCase.execute(statementId);

        if (AccountStatementStatus.IMPORTED.equals(accountStatement.getStatus())) {

            return String.format("Statement with in status - %s cannot be retried", accountStatement.getStatus().name());
        }

        log.info("Retrying statement with id {} and status {} ", accountStatement.getId(), accountStatement.getStatus());

        if (Objects.nonNull(accountStatement.getPreviousStatement()) &&
            accountStatement.getPreviousStatement().getStatus().equals(AccountStatementStatus.REVIEW)) {

            throw InvalidEntityException.createInvalidEntity(
                AccountStatement.class,
                MessageFormat.format("Parent of statement with identifier {0} has invalid status", statementId));
        }

        AccountStatement nextAccountStatement = getNextAccountStatementOrNull(accountStatement);

        AccountStatementFile statementFile = readAccountStatementFileUseCase.execute(
                AccountStatementFileFilters.builder().statementId(accountStatement.getId()).build(), null, null).stream().findFirst()
            .orElse(null);

        if (Objects.nonNull(statementFile)) {
            accountStatement = updateAccountStatementUseCase.execute(
                accountStatement.toBuilder()
                    .status(AccountStatementStatus.REVIEW)
                    .build());
            discardAccountStatementUseCase.execute(accountStatement.getId());

            reprocessFilesUseCase.execute(statementFile.getId(),
                Objects.isNull(nextAccountStatement) ? null : String.valueOf(nextAccountStatement.getId()));

            return String.format("Statement file with with id - %s is being reprocessed", accountStatement.getId());
        }

        revalidateAccountStatementUseCase.execute(accountStatement);

        return String.format("Statement with identifier - %s is being retried", statementId);
    }

    private AccountStatement getNextAccountStatementOrNull(final AccountStatement accountStatement) {

        try {
            return readAccountStatementUseCase.executeByPreviousStatement(accountStatement);
        } catch (NotFoundException e) {
            return null;
        }
    }

}
