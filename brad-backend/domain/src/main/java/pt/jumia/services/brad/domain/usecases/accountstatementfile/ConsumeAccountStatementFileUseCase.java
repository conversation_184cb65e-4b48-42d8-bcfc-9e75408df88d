package pt.jumia.services.brad.domain.usecases.accountstatementfile;


import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.FileStorageClient;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;
import pt.jumia.services.brad.domain.entities.files.SwiftMessageFileResponse;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementFileRepository;
import pt.jumia.services.brad.domain.usecases.apilog.CreateApiLogUseCase;
import pt.jumia.services.brad.domain.usecases.processconvertfiles.CreateParsedSwiftMessage;
import pt.jumia.services.brad.domain.utils.AccountStatementFilesUtils;

@Component
@Slf4j
@RequiredArgsConstructor
public class ConsumeAccountStatementFileUseCase {

    private final AccountStatementFileRepository accountStatementFileRepository;
    private final ReadAccountStatementFileUseCase readAccountStatementFileUseCase;
    private final FileStorageClient fileStorageClient;
    private final CreateParsedSwiftMessage createParsedSwiftMessage;
    private final CreateApiLogUseCase createApiLogUseCase;

    @Nullable
    public void execute(Long accountStatementFileId, String nextStatementId) throws IOException {

        log.info(String.format("Processing account statement file with id -> %s", accountStatementFileId));

        AccountStatementFile accountStatementFile = readAccountStatementFileUseCase.execute(accountStatementFileId);
        SwiftMessageFileResponse swiftMessageFileResponse = null;

        accountStatementFile = accountStatementFileRepository.upsert(accountStatementFile.toBuilder()
            .processingStatus(ProcessingStatus.PROCESSING)
            .statusDescription(ProcessingStatus.PROCESSING.getDescription())
            .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
            .build());

        AccountStatementFile statementFileToUpdate = accountStatementFile;

        File file = null;
        try {
            file = fileStorageClient.getaccountStatementFile(accountStatementFile.getName());

            swiftMessageFileResponse = createParsedSwiftMessage.parseSwiftMessage(file, accountStatementFile, nextStatementId);

            statementFileToUpdate = accountStatementFile.toBuilder()
                .statement(swiftMessageFileResponse.getStatement())
                .processingStatus(swiftMessageFileResponse.getProcessingStatus())
                .statusDescription(swiftMessageFileResponse.getStatusDescription())
                .build();

        } catch (Exception ex) {
            log.error(ExceptionUtils.getStackTrace(ex));
            statementFileToUpdate = handleFailure(ex, swiftMessageFileResponse, accountStatementFile);
        } finally {
            AccountStatementFile updatedAccountStatementFile = accountStatementFileRepository.upsert(statementFileToUpdate);
            log.info(
                String.format("Done Processing account statement file id -> %s new status -> %s",
                    updatedAccountStatementFile.getId(), updatedAccountStatementFile.getProcessingStatus().name()));
            if (file != null) {
                Files.delete(file.toPath());
            }
        }
    }


    public void executeBatch(final List<AccountStatementFile> files) throws IOException {

        for (AccountStatementFile file : files) {
            execute(file.getId(), null);
        }
    }

    private AccountStatementFile handleFailure(final Exception ex, final SwiftMessageFileResponse swiftMessageFileResponse,
        final AccountStatementFile accountStatementFile) {

        if (swiftMessageFileResponse != null && swiftMessageFileResponse.getStatement() != null
            && swiftMessageFileResponse.getStatement().getAccount() != null) {
            createApiLogUseCase.execute(AccountStatementFilesUtils.createFailureApiLog(swiftMessageFileResponse,
                swiftMessageFileResponse.getStatement().getAccount()));
        } else {
            createApiLogUseCase.execute(AccountStatementFilesUtils.createFailureApiLog(swiftMessageFileResponse, null));
        }
        return accountStatementFile.toBuilder()
            .processingStatus(ProcessingStatus.FAILED_PROCESSING)
            .statusDescription(
                ProcessingStatus.FAILED_PROCESSING.getDescription() + "because => " + ExceptionUtils.getMessage(ex) + "\n \t"
                    + ExceptionUtils.getStackTrace(ex))
            .build();
    }

}
