package pt.jumia.services.brad.domain.repository.brad;

import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.AccountDailySummary;
import pt.jumia.services.brad.domain.entities.dtos.GroupedAccountDailySummaryDto;
import pt.jumia.services.brad.domain.entities.dtos.StackedCashPositionDto;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashEvolutionFilters;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashPositionFilters;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface AccountDailySummaryRepository {

    AccountDailySummary upsert(AccountDailySummary summary);

    Optional<AccountDailySummary> findById(LocalDate transactionDate, Account account);

    Optional<AccountDailySummary> findLatestByAccountIdAndDate(Long accountId, LocalDate toDate);

    Optional<AccountDailySummary> findOldestMissingFxRate(Long accountId);

    void deleteById(LocalDate accountDate, Account account);

    void deleteByDates(Long accountId, LocalDate startDate, LocalDate endDate);

    List<GroupedAccountDailySummaryDto> findCashEvolution(CashEvolutionFilters evolutionFilters);

    List<GroupedAccountDailySummaryDto> findCashPosition(CashPositionFilters cashPositionFilters);

    List<StackedCashPositionDto> findCashPositionStacked(CashPositionFilters positionFilters);
}
