package pt.jumia.services.brad.domain.usecases.accounts;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.AccountRepository;
import pt.jumia.services.brad.domain.usecases.accountstatement.ReadAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.countries.ReadCountriesUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class UpdateAccountsUseCaseTest {

    private static final Account BANK_ACCOUNT = FakeAccounts.getFakeAccounts(1, null).get(0)
        .toBuilder().id(1L).build();

    @Mock
    private AccountRepository accountRepository;

    @Mock
    private ReadCountriesUseCase readCountriesUseCase;

    @Mock
    private ReadCurrenciesUseCase readCurrenciesUseCase;

    @Mock
    private ReadAccountsUseCase readAccountsUseCase;

    @Mock
    private ReadAccountStatementUseCase readAccountStatementUseCase;

    @InjectMocks
    private UpdateAccountsUseCase updateAccountsUseCase;

    @Test
    public void updateAccount_nullable() throws Exception {

        assertThrows(EntityErrorsException.class, () -> {
            updateAccountsUseCase.execute(null);
        });
    }

    @Test
    public void updateAccount_notFound() throws Exception {

        when(accountRepository.findById(1)).thenReturn(Optional.empty());

        assertThrows(NotFoundException.class, () -> {
            updateAccountsUseCase.execute(BANK_ACCOUNT);
        });
    }

    @Test
    public void updateAccount_IdCannotBeNull() throws Exception {

        assertThrows(EntityErrorsException.class, () -> {
            updateAccountsUseCase.execute(BANK_ACCOUNT.toBuilder().id(null).build());
        });
    }


    @Test
    public void updateAccount_success() throws Exception {

        when(accountRepository.findById(1)).thenReturn(Optional.of(BANK_ACCOUNT));

        Account toUpdateAccount = BANK_ACCOUNT.toBuilder().accountName("newBankName").build();

        when(accountRepository.upsert(any(Account.class)))
            .thenReturn(toUpdateAccount);
        when(readCountriesUseCase.execute("NG")).thenReturn(FakeCountries.NIGERIA);

        Account updated = updateAccountsUseCase.execute(toUpdateAccount);

        assertEquals(toUpdateAccount.getAccountName(), updated.getAccountName());

        verify(accountRepository).upsert(any(Account.class));

    }

    @Test
    public void executeLastProcessedStatementDate_withoutDate_success() throws Exception {

        AccountStatement statement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder().createdAt(LocalDateTime.now()).build();
        when(readAccountStatementUseCase.executeLastImportedStatement(anyLong())).thenReturn(Optional.of(statement));
        when(readAccountsUseCase.execute(anyInt())).thenReturn(BANK_ACCOUNT);
        when(accountRepository.upsert(any(Account.class))).thenReturn(BANK_ACCOUNT);

        updateAccountsUseCase.executeLastProcessedStatementDate(Collections.singletonList(1L));

        ArgumentCaptor<Account> accountCaptor = ArgumentCaptor.forClass(Account.class);
        verify(accountRepository).upsert(accountCaptor.capture());
        assertEquals(statement.getFinalDate(), accountCaptor.getValue().getLastProcessedStatementDate());
    }

    @Test
    public void executeLastProcessedStatementDate_withoutDate_savedWithNull() throws Exception {

        when(readAccountStatementUseCase.executeLastImportedStatement(1L)).thenReturn(Optional.empty());
        when(readAccountsUseCase.execute(anyInt())).thenReturn(BANK_ACCOUNT);
        when(accountRepository.upsert(any(Account.class))).thenReturn(BANK_ACCOUNT);

        updateAccountsUseCase.executeLastProcessedStatementDate(Collections.singletonList(1L));

        ArgumentCaptor<Account> accountCaptor = ArgumentCaptor.forClass(Account.class);
        verify(accountRepository).upsert(accountCaptor.capture());
        assertNull(accountCaptor.getValue().getLastProcessedStatementDate());
    }

    @Test
    public void updateAccount_psp_success() throws Exception {
        Account original = FakeAccounts.getFakeAccounts(1, Account.Type.PSP).get(0).toBuilder().id(2L).build();
        when(accountRepository.findById(2L)).thenReturn(Optional.of(original));
        Account toUpdate = original.toBuilder()
            .accountName("newPspName")
            .partner("newPartner")
            .phoneNumber("newPhone")
            .status(Account.Status.CLOSED)
            .build();
        when(accountRepository.upsert(any(Account.class))).thenReturn(toUpdate);
        when(readCountriesUseCase.execute("NG")).thenReturn(FakeCountries.NIGERIA);
        updateAccountsUseCase.execute(toUpdate);
        ArgumentCaptor<Account> accountCaptor = ArgumentCaptor.forClass(Account.class);
        verify(accountRepository).upsert(accountCaptor.capture());
        Account captured = accountCaptor.getValue();
        assertEquals("newPspName", captured.getAccountName());
        assertEquals("newPartner", captured.getPartner());
        assertEquals("newPhone", captured.getPhoneNumber());
        assertEquals(Account.Status.CLOSED, captured.getStatus());
    }

    @Test
    public void updateAccount_mobileMoney_success() throws Exception {
        Account original = FakeAccounts.getFakeAccounts(1, Account.Type.MOBILE_MONEY).get(0).toBuilder().id(3L).build();
        when(accountRepository.findById(3L)).thenReturn(Optional.of(original));
        Account toUpdate = original.toBuilder()
            .accountName("newMobileMoneyName")
            .partner("newPartnerMM")
            .phoneNumber("newPhoneMM")
            .status(Account.Status.TO_BE_CLOSED)
            .build();
        when(accountRepository.upsert(any(Account.class))).thenReturn(toUpdate);
        when(readCountriesUseCase.execute("NG")).thenReturn(FakeCountries.NIGERIA);
        updateAccountsUseCase.execute(toUpdate);
        ArgumentCaptor<Account> accountCaptor = ArgumentCaptor.forClass(Account.class);
        verify(accountRepository).upsert(accountCaptor.capture());
        Account captured = accountCaptor.getValue();
        assertEquals("newMobileMoneyName", captured.getAccountName());
        assertEquals("newPartnerMM", captured.getPartner());
        assertEquals("newPhoneMM", captured.getPhoneNumber());
        assertEquals(Account.Status.TO_BE_CLOSED, captured.getStatus());
    }

    @Test
    public void updateAccount_wallet_success() throws Exception {
        Account original = FakeAccounts.getFakeAccounts(1, Account.Type.WALLET).get(0).toBuilder().id(4L).build();
        when(accountRepository.findById(4L)).thenReturn(Optional.of(original));
        Account toUpdate = original.toBuilder()
            .accountName("newWalletName")
            .partner("newPartnerWallet")
            .status(Account.Status.OPEN)
            .build();
        when(accountRepository.upsert(any(Account.class))).thenReturn(toUpdate);
        when(readCountriesUseCase.execute("NG")).thenReturn(FakeCountries.NIGERIA);
        updateAccountsUseCase.execute(toUpdate);
        ArgumentCaptor<Account> accountCaptor = ArgumentCaptor.forClass(Account.class);
        verify(accountRepository).upsert(accountCaptor.capture());
        Account captured = accountCaptor.getValue();
        assertEquals("newWalletName", captured.getAccountName());
        assertEquals("newPartnerWallet", captured.getPartner());
        assertEquals(Account.Status.OPEN, captured.getStatus());
    }

    @Test
    public void updateAccount_investments_termDeposits_success() throws Exception {
        Account original = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0).toBuilder()
            .id(5L)
            .subType(Account.SubType.TERM_DEPOSITS)
            .build();
        when(accountRepository.findById(5L)).thenReturn(Optional.of(original));
        Account toUpdate = original.toBuilder()
            .accountName("newTermDepositName")
            .subType(Account.SubType.TERM_DEPOSITS)
            .contractId("newContractId")
            .amountDeposited(java.math.BigDecimal.TEN)
            .maturityDate(java.time.LocalDate.now().plusDays(10))
            .interest(java.math.BigDecimal.valueOf(2.5))
            .status(Account.Status.OPEN)
            .build();
        when(accountRepository.upsert(any(Account.class))).thenReturn(toUpdate);
        when(readCountriesUseCase.execute("NG")).thenReturn(FakeCountries.NIGERIA);
        updateAccountsUseCase.execute(toUpdate);
        ArgumentCaptor<Account> accountCaptor = ArgumentCaptor.forClass(Account.class);
        verify(accountRepository).upsert(accountCaptor.capture());
        Account captured = accountCaptor.getValue();
        assertEquals("newTermDepositName", captured.getAccountName());
        assertEquals(Account.SubType.TERM_DEPOSITS, captured.getSubType());
        assertEquals("newContractId", captured.getContractId());
        assertEquals(java.math.BigDecimal.TEN, captured.getAmountDeposited());
        assertEquals(java.time.LocalDate.now().plusDays(10), captured.getMaturityDate());
        assertEquals(java.math.BigDecimal.valueOf(2.5), captured.getInterest());
        assertEquals(Account.Status.OPEN, captured.getStatus());
    }

    @Test
    public void updateAccount_investments_tlf_success() throws Exception {
        Account original = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0).toBuilder()
            .id(6L)
            .subType(Account.SubType.TLF)
            .build();
        when(accountRepository.findById(6L)).thenReturn(Optional.of(original));
        Account toUpdate = original.toBuilder()
            .accountName("newTLFName")
            .subType(Account.SubType.TLF)
            .contractId("newContractIdTLF")
            .amountDeposited(java.math.BigDecimal.valueOf(1000))
            .maturityDate(java.time.LocalDate.now().plusDays(20))
            .interest(java.math.BigDecimal.valueOf(3.5))
            .status(Account.Status.OPEN)
            .build();
        when(accountRepository.upsert(any(Account.class))).thenReturn(toUpdate);
        when(readCountriesUseCase.execute("NG")).thenReturn(FakeCountries.NIGERIA);
        updateAccountsUseCase.execute(toUpdate);
        ArgumentCaptor<Account> accountCaptor = ArgumentCaptor.forClass(Account.class);
        verify(accountRepository).upsert(accountCaptor.capture());
        Account captured = accountCaptor.getValue();
        assertEquals("newTLFName", captured.getAccountName());
        assertEquals(Account.SubType.TLF, captured.getSubType());
        assertEquals("newContractIdTLF", captured.getContractId());
        assertEquals(java.math.BigDecimal.valueOf(1000), captured.getAmountDeposited());
        assertEquals(java.time.LocalDate.now().plusDays(20), captured.getMaturityDate());
        assertEquals(java.math.BigDecimal.valueOf(3.5), captured.getInterest());
        assertEquals(Account.Status.OPEN, captured.getStatus());
    }

    @Test
    public void updateAccount_investments_bankGuarantees_success() throws Exception {
        Account original = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0).toBuilder()
            .id(7L)
            .subType(Account.SubType.BANK_GUARANTEES)
            .build();
        when(accountRepository.findById(7L)).thenReturn(Optional.of(original));
        Account toUpdate = original.toBuilder()
            .accountName("newBankGuaranteeName")
            .subType(Account.SubType.BANK_GUARANTEES)
            .contractId("newContractIdBG")
            .amountDeposited(java.math.BigDecimal.valueOf(2000))
            .maturityDate(java.time.LocalDate.now().plusDays(30))
            .interest(java.math.BigDecimal.valueOf(4.5))
            .status(Account.Status.CLOSED)
            .build();
        when(accountRepository.upsert(any(Account.class))).thenReturn(toUpdate);
        when(readCountriesUseCase.execute("NG")).thenReturn(FakeCountries.NIGERIA);
        updateAccountsUseCase.execute(toUpdate);
        ArgumentCaptor<Account> accountCaptor = ArgumentCaptor.forClass(Account.class);
        verify(accountRepository).upsert(accountCaptor.capture());
        Account captured = accountCaptor.getValue();
        assertEquals("newBankGuaranteeName", captured.getAccountName());
        assertEquals(Account.SubType.BANK_GUARANTEES, captured.getSubType());
        assertEquals("newContractIdBG", captured.getContractId());
        assertEquals(java.math.BigDecimal.valueOf(2000), captured.getAmountDeposited());
        assertEquals(java.time.LocalDate.now().plusDays(30), captured.getMaturityDate());
        assertEquals(java.math.BigDecimal.valueOf(4.5), captured.getInterest());
        assertEquals(Account.Status.CLOSED, captured.getStatus());
    }

    @Test
    public void updateAccount_investments_bonds_success() throws Exception {
        Account original = FakeAccounts.getFakeAccounts(1, Account.Type.INVESTMENTS).get(0).toBuilder()
            .id(8L)
            .subType(Account.SubType.BONDS)
            .build();
        when(accountRepository.findById(8L)).thenReturn(Optional.of(original));
        Account toUpdate = original.toBuilder()
            .accountName("newBondName")
            .subType(Account.SubType.BONDS)
            .isin("newISIN")
            .amountDeposited(java.math.BigDecimal.valueOf(3000))
            .maturityDate(java.time.LocalDate.now().plusDays(40))
            .nominalAmount(java.math.BigDecimal.valueOf(5000))
            .couponPaymentPeriodicity(Account.CouponPaymentPeriodicity.SEMI_ANNUAL)
            .couponRate(java.math.BigDecimal.valueOf(1.5))
            .status(Account.Status.OPEN)
            .build();
        when(accountRepository.upsert(any(Account.class))).thenReturn(toUpdate);
        when(readCountriesUseCase.execute("NG")).thenReturn(FakeCountries.NIGERIA);
        updateAccountsUseCase.execute(toUpdate);
        ArgumentCaptor<Account> accountCaptor = ArgumentCaptor.forClass(Account.class);
        verify(accountRepository).upsert(accountCaptor.capture());
        Account captured = accountCaptor.getValue();
        assertEquals("newBondName", captured.getAccountName());
        assertEquals(Account.SubType.BONDS, captured.getSubType());
        assertEquals("newISIN", captured.getIsin());
        assertEquals(java.math.BigDecimal.valueOf(3000), captured.getAmountDeposited());
        assertEquals(java.time.LocalDate.now().plusDays(40), captured.getMaturityDate());
        assertEquals(java.math.BigDecimal.valueOf(5000), captured.getNominalAmount());
        assertEquals(Account.CouponPaymentPeriodicity.SEMI_ANNUAL, captured.getCouponPaymentPeriodicity());
        assertEquals(java.math.BigDecimal.valueOf(1.5), captured.getCouponRate());
        assertEquals(Account.Status.OPEN, captured.getStatus());
    }

    @Test
    public void updateAccount_commonFields_success() throws Exception {
        when(accountRepository.findById(1)).thenReturn(Optional.of(BANK_ACCOUNT));
        Account toUpdateAccount = BANK_ACCOUNT.toBuilder()
            .companyID("updatedCompanyID")
            .country(FakeCountries.EGYPT)
            .partner("updatedPartner")
            .navReference("updatedNavReference")
            .accountNumber("updatedAccountNumber")
            .accountName("updatedAccountName")
            .phoneNumber("updatedPhoneNumber")
            .type(Account.Type.BANK_ACCOUNT)
            .subType(null)
            .status(Account.Status.CLOSED)
            .currency(BANK_ACCOUNT.getCurrency())
            .statementSource(BANK_ACCOUNT.getStatementSource())
            .statementPeriodicity(BANK_ACCOUNT.getStatementPeriodicity())
            .build();
        when(accountRepository.upsert(any(Account.class))).thenReturn(toUpdateAccount);
        when(readCountriesUseCase.execute("EG")).thenReturn(FakeCountries.EGYPT);
        updateAccountsUseCase.execute(toUpdateAccount);
        ArgumentCaptor<Account> accountCaptor = ArgumentCaptor.forClass(Account.class);
        verify(accountRepository).upsert(accountCaptor.capture());
        Account captured = accountCaptor.getValue();
        assertEquals("updatedCompanyID", captured.getCompanyID());
        assertEquals(FakeCountries.EGYPT, captured.getCountry());
        assertEquals("updatedPartner", captured.getPartner());
        assertEquals("updatedNavReference", captured.getNavReference());
        assertEquals("updatedAccountNumber", captured.getAccountNumber());
        assertEquals("updatedAccountName", captured.getAccountName());
        assertEquals("updatedPhoneNumber", captured.getPhoneNumber());
        assertEquals(Account.Type.BANK_ACCOUNT, captured.getType());
        assertEquals(Account.Status.CLOSED, captured.getStatus());
    }

    @Test
    public void updateAccount_bankAccount_fields_success() throws Exception {
        Account original = FakeAccounts.getFakeAccounts(1, Account.Type.BANK_ACCOUNT).get(0).toBuilder()
            .id(10L)
            .build();
        when(accountRepository.findById(10L)).thenReturn(Optional.of(original));
        Account toUpdate = original.toBuilder()
            .beneficiaryName("updatedBeneficiaryName")
            .beneficiaryAddress("updatedBeneficiaryAddress")
            .iban("updatedIban")
            .swiftCode("updatedSwiftCode")
            .bankRoutingCode("updatedBankRoutingCode")
            .sortCode("updatedSortCode")
            .branchCode("updatedBranchCode")
            .rib("updatedRib")
            .build();
        when(accountRepository.upsert(any(Account.class))).thenReturn(toUpdate);
        when(readCountriesUseCase.execute("NG")).thenReturn(FakeCountries.NIGERIA);
        updateAccountsUseCase.execute(toUpdate);
        ArgumentCaptor<Account> accountCaptor = ArgumentCaptor.forClass(Account.class);
        verify(accountRepository).upsert(accountCaptor.capture());
        Account captured = accountCaptor.getValue();
        assertEquals("updatedBeneficiaryName", captured.getBeneficiaryName());
        assertEquals("updatedBeneficiaryAddress", captured.getBeneficiaryAddress());
        assertEquals("updatedIban", captured.getIban());
        assertEquals("updatedSwiftCode", captured.getSwiftCode());
        assertEquals("updatedBankRoutingCode", captured.getBankRoutingCode());
        assertEquals("updatedSortCode", captured.getSortCode());
        assertEquals("updatedBranchCode", captured.getBranchCode());
        assertEquals("updatedRib", captured.getRib());
    }
}
