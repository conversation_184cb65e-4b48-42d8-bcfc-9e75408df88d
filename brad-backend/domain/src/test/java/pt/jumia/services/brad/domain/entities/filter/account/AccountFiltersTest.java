package pt.jumia.services.brad.domain.entities.filter.account;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;

import java.time.LocalDateTime;
import java.util.Arrays;


public class AccountFiltersTest {

    private static AccountFilters filters;
    private static LocalDateTime createdAt;
    @BeforeAll
    public static void setUp() {
        createdAt = LocalDateTime.now();

        filters = AccountFilters.builder()
                .companyID("fakeCompanyID1")
                .countryCodes(Arrays.asList(FakeCountries.NIGERIA.getId(), FakeCountries.EGYPT.getId()))
                .navReference("fakeNavReference1")
                .beneficiaryName("fakeBeneficiaryName1")
                .beneficiaryAddress("fakeBeneficiaryAddress1")
                .accountNumber("fakeAccountNumber1")
                .accountName("fakeBankName1")
                .swiftCode("fakeSwiftCode1")
                .bankRoutingCode("fakeBankRoutingCode1")
                .sortCode("fakeSortCode1")
                .branchCode("fakeBranchCode1")
                .rib("fakeRib1")
                .createdAtStart(createdAt)
                .build();
    }


    @Test
    public void testAccountCompanyIDFilters() {
        Assertions.assertEquals("fakeCompanyID1", filters.getCompanyID());
    }

    @Test
    public void testAccountCountryFilters() {
        Assertions.assertEquals(2, filters.getCountryCodes().size());
    }


    @Test
    public void testAccountNavReferenceFilters() {
        Assertions.assertEquals("fakeNavReference1", filters.getNavReference());
    }

    @Test
    public void testAccountBeneficiaryNameFilters() {
        Assertions.assertEquals("fakeBeneficiaryName1", filters.getBeneficiaryName());
    }

    @Test
    public void testAccountBeneficiaryAddressFilters() {
        Assertions.assertEquals("fakeBeneficiaryAddress1", filters.getBeneficiaryAddress());
    }

    @Test
    public void testAccountAccountNumberFilters() {
        Assertions.assertEquals("fakeAccountNumber1", filters.getAccountNumber());
    }

    @Test
    public void testAccountBankNameFilters() {
        Assertions.assertEquals("fakeBankName1", filters.getAccountName());
    }

    @Test
    public void testAccountSwiftCodeFilters() {
        Assertions.assertEquals("fakeSwiftCode1", filters.getSwiftCode());
    }

    @Test
    public void testAccountBankRoutingCodeFilters() {
        Assertions.assertEquals("fakeBankRoutingCode1", filters.getBankRoutingCode());
    }

    @Test
    public void testAccountSortCodeFilters() {
        Assertions.assertEquals("fakeSortCode1", filters.getSortCode());
    }

    @Test
    public void testAccountBranchCodeFilters() {
        Assertions.assertEquals("fakeBranchCode1", filters.getBranchCode());
    }

    @Test
    public void testAccountRibFilters() {
        Assertions.assertEquals("fakeRib1", filters.getRib());
    }

    @Test
    public void testAccountCreatedAtFilters() {
        Assertions.assertEquals(createdAt, filters.getCreatedAtStart());
    }

    @Test
    public void testAccountGetAsMapFilters() {
        Assertions.assertEquals(14, filters.getAsMap().size());
    }

    @Test
    public void testAccountGetNullAsMapFilters() {
        Assertions.assertEquals(1, AccountFilters.builder().build().getAsMap().size());
    }

}
