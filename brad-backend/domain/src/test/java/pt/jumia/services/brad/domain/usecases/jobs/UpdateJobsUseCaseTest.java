package pt.jumia.services.brad.domain.usecases.jobs;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.Jobs;
import pt.jumia.services.brad.domain.repository.brad.JobsRepository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class UpdateJobsUseCaseTest {

    @Mock
    private JobsRepository jobsRepository;

    @InjectMocks
    private UpdateJobUseCase updateJobUseCase;

    @Test
    public void readJobs_success() throws Exception {
        Jobs job = Jobs.builder()
                .jobName("JobName3")
                .cronExpression("CronExpression3")
                .state("State3")
                .build();

        when(jobsRepository.update(job.getJobName(), job)).thenReturn(job);

        Jobs aJob = updateJobUseCase.execute(job.getJobName(), job);

        assertEquals(job, aJob);
        verify(jobsRepository).update(job.getJobName(), job);
    }

    @Test
    public void toggleState_whenRepositorySucceeds_thenNoException() throws Exception {
        // GIVEN
        String jobName = "JobName3";

        // WHEN
        updateJobUseCase.toggleState(jobName);

        // THEN
        verify(jobsRepository).toggleState(jobName);
    }

    @Test
    public void toggleState_whenRepositoryThrowsSchedulerException_thenPropagateException() throws Exception {
        // GIVEN
        String jobName = "JobName3";
        org.quartz.SchedulerException schedulerException = new org.quartz.SchedulerException("Pause failed");
        org.mockito.Mockito.doThrow(schedulerException).when(jobsRepository).toggleState(jobName);

        // WHEN & THEN
        org.junit.jupiter.api.Assertions.assertThrows(org.quartz.SchedulerException.class, () -> {
            updateJobUseCase.toggleState(jobName);
        });
        verify(jobsRepository).toggleState(jobName);
    }

}
