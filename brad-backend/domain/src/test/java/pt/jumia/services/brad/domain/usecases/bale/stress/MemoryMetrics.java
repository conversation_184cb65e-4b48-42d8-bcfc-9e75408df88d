package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class MemoryMetrics {
    
    @Builder.Default
    long peakMemoryUsage = 0L;
    
    @Builder.Default
    long averageMemoryUsage = 0L;
    
    @Builder.Default
    long memoryVariance = 0L;
    
    @Builder.Default
    long gcCount = 0L;
    
    @Builder.Default
    int snapshotCount = 0;
    
    @Builder.Default
    long usedMemory = 0L;
    
    @Builder.Default
    long freeMemory = 0L;
    
    @Builder.Default
    long maxMemory = 0L;
    
    public static MemoryMetrics empty() {
        return MemoryMetrics.builder().build();
    }
}
