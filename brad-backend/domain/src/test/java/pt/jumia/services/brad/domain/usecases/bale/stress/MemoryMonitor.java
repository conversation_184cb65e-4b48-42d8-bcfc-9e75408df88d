package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.management.GarbageCollectorMXBean;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MemoryMonitor {

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private final List<MemorySnapshot> snapshots = new ArrayList<>();
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    private volatile boolean monitoring = false;

    public void startMonitoring() {
        if (monitoring) {
            return;
        }
        
        monitoring = true;
        snapshots.clear();
        
        scheduler.scheduleAtFixedRate(this::captureSnapshot, 0, 1, TimeUnit.SECONDS);
        log.debug("Memory monitoring started");
    }

    public void stopMonitoring() {
        monitoring = false;
        log.debug("Memory monitoring stopped");
    }

    public void reset() {
        snapshots.clear();
        log.debug("Memory metrics reset");
    }

    public MemoryMetrics getMetrics() {
        if (snapshots.isEmpty()) {
            return MemoryMetrics.empty();
        }

        long peakUsed = snapshots.stream()
                .mapToLong(MemorySnapshot::getUsedMemory)
                .max()
                .orElse(0L);

        long avgUsed = (long) snapshots.stream()
                .mapToLong(MemorySnapshot::getUsedMemory)
                .average()
                .orElse(0.0);

        long memoryVariance = calculateMemoryVariance();
        long gcCount = getTotalGcCount();

        return MemoryMetrics.builder()
                .peakMemoryUsage(peakUsed)
                .averageMemoryUsage(avgUsed)
                .memoryVariance(memoryVariance)
                .gcCount(gcCount)
                .snapshotCount(snapshots.size())
                .build();
    }

    public MemoryMetrics getCurrentMetrics() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        
        return MemoryMetrics.builder()
                .usedMemory(heapUsage.getUsed())
                .freeMemory(heapUsage.getMax() - heapUsage.getUsed())
                .maxMemory(heapUsage.getMax())
                .build();
    }

    private void captureSnapshot() {
        if (!monitoring) {
            return;
        }

        try {
            MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
            long timestamp = System.currentTimeMillis();

            MemorySnapshot snapshot = MemorySnapshot.builder()
                    .timestamp(timestamp)
                    .usedMemory(heapUsage.getUsed())
                    .freeMemory(heapUsage.getMax() - heapUsage.getUsed())
                    .maxMemory(heapUsage.getMax())
                    .build();

            snapshots.add(snapshot);

            if (snapshots.size() > 3600) {
                snapshots.remove(0);
            }

        } catch (Exception e) {
            log.warn("Error capturing memory snapshot", e);
        }
    }

    private long calculateMemoryVariance() {
        if (snapshots.size() < 2) {
            return 0L;
        }

        double mean = snapshots.stream()
                .mapToLong(MemorySnapshot::getUsedMemory)
                .average()
                .orElse(0.0);

        double variance = snapshots.stream()
                .mapToLong(MemorySnapshot::getUsedMemory)
                .mapToDouble(used -> Math.pow(used - mean, 2))
                .average()
                .orElse(0.0);

        return (long) variance;
    }

    private long getTotalGcCount() {
        return ManagementFactory.getGarbageCollectorMXBeans().stream()
                .mapToLong(GarbageCollectorMXBean::getCollectionCount)
                .sum();
    }
}
