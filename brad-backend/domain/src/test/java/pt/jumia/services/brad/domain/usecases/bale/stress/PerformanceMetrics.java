package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class PerformanceMetrics {
    
    @Builder.Default
    double averageCpuUsage = 0.0;
    
    @Builder.Default
    double peakCpuUsage = 0.0;
    
    @Builder.Default
    double cpuVariance = 0.0;
    
    @Builder.Default
    int snapshotCount = 0;
    
    public static PerformanceMetrics empty() {
        return PerformanceMetrics.builder().build();
    }
}
