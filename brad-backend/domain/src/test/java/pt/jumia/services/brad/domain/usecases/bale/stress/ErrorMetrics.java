package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Data;

@Data
public class ErrorMetrics {
    
    private int databaseFailures = 0;
    private int memoryExhaustionEvents = 0;
    private int timeoutEvents = 0;
    private int totalProcessed = 0;
    private int totalErrors = 0;
    
    public void incrementDatabaseFailures() {
        this.databaseFailures++;
        this.totalErrors++;
    }
    
    public void incrementMemoryExhaustionEvents() {
        this.memoryExhaustionEvents++;
        this.totalErrors++;
    }
    
    public void incrementTimeoutEvents() {
        this.timeoutEvents++;
        this.totalErrors++;
    }
    
    public void incrementProcessed() {
        this.totalProcessed++;
    }
    
    public double getRecoveryRate() {
        int total = totalProcessed + totalErrors;
        return total > 0 ? (double) totalProcessed / total : 0.0;
    }
    
    public double getErrorRate() {
        int total = totalProcessed + totalErrors;
        return total > 0 ? (double) totalErrors / total : 0.0;
    }
}
