package pt.jumia.services.brad.domain.usecases.processconvertfiles;

import com.prowidesoftware.swift.model.AbstractMessage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.files.SwiftMessageFileResponse;
import pt.jumia.services.brad.domain.enumerations.SftpRunMode;
import pt.jumia.services.brad.domain.settings.OverallSettings;
import pt.jumia.services.brad.domain.settings.SftpSettings;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.CreateAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.PostStatementWriteUseCase;
import pt.jumia.services.brad.domain.usecases.apilog.CreateApiLogUseCase;
import pt.jumia.services.brad.domain.usecases.processconvertfiles.convertfile.ConvertFile;

import java.io.File;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
class CreateParseSwiftMessageTest {

    @Mock
    private CreateAccountStatementUseCase createAccountStatementUseCase;
    @Mock
    private OverallSettings overallSettings;
    @Mock
    private CreateApiLogUseCase createApiLogUseCase;
    @Mock
    private ReadAccountsUseCase readAccountsUseCase;
    @Mock
    private PostStatementWriteUseCase postStatementWriteUseCase;
    @Mock
    private SftpSettings sftpSettings;


    @InjectMocks
    private CreateParsedSwiftMessage parseSwiftMessage;


    @Test
     void testParseSwiftMessageMT_Success() throws Exception {
         File fileMT = new File("src/test/java/pt/jumia/services/brad/domain/utils/files/JUMIA(*************)_01Aug2024.TXT");

        AbstractMessage message = AbstractMessageParser.getMessage(fileMT);
        ConvertFile convertFile = AbstractMessageParser.getConvertFile(message);
        SwiftMessageFileResponse response = AbstractMessageParser.generateSwiftMessageFileResponse(convertFile, message);

        when(overallSettings.getSftpSettings()).thenReturn(sftpSettings);
        when(sftpSettings.getSftpRunMode(anyLong())).thenReturn(SftpRunMode.LIVE);
        when(readAccountsUseCase.execute(anyString())).thenReturn(FakeAccounts.FAKE_ACCOUNT);
        when(createAccountStatementUseCase.execute(any(), anyString(), anyList())).thenReturn(FakeAccountStatements.FAKE_ACCOUNT_STATEMENT);

        SwiftMessageFileResponse actualResponse = parseSwiftMessage.parseSwiftMessage(fileMT, AccountStatementFile.builder().name("SomeName").build(), null);

        assertNotNull(actualResponse);
        verify(createAccountStatementUseCase).execute(eq(response.getStatement()), eq(response.getAccountNumber()), eq(response.getTransactions()));
        verify(createApiLogUseCase).execute(any());

    }

    @Test
    void testParseSwiftMessageMX_Success() throws Exception {
        File fileMX = new File("src/test/java/pt/jumia/services/brad/domain/utils/files/CAMT053_100626001_2024042402061458");

        AbstractMessage message = AbstractMessageParser.getMessage(fileMX);
        ConvertFile convertFile = AbstractMessageParser.getConvertFile(message);
        SwiftMessageFileResponse response = AbstractMessageParser.generateSwiftMessageFileResponse(convertFile, message);

        when(overallSettings.getSftpSettings()).thenReturn(sftpSettings);
        when(sftpSettings.getSftpRunMode(anyLong())).thenReturn(SftpRunMode.LIVE);
        when(readAccountsUseCase.execute(anyString())).thenReturn(FakeAccounts.FAKE_ACCOUNT);
        when(createAccountStatementUseCase.execute(any(), anyString(), anyList())).thenReturn(FakeAccountStatements.FAKE_ACCOUNT_STATEMENT);
        SwiftMessageFileResponse actualResponse = parseSwiftMessage.parseSwiftMessage(fileMX, AccountStatementFile.builder().name("SomeName").build(), null);

        assertNotNull(actualResponse);
        verify(createAccountStatementUseCase).execute(eq(response.getStatement()), eq(response.getAccountNumber()), eq(response.getTransactions()));
        verify(createApiLogUseCase).execute(any());

    }

    @Test
    void parseSwiftMessage_inDryRun_doesNotCreateStatements() throws Exception {
        // GIVEN
        File fileMX = new File("src/test/java/pt/jumia/services/brad/domain/utils/files/CAMT053_100626001_2024042402061458");

        when(overallSettings.getSftpSettings()).thenReturn(sftpSettings);
        when(sftpSettings.getSftpRunMode(anyLong())).thenReturn(SftpRunMode.DRY_RUN);
        when(readAccountsUseCase.execute(anyString())).thenReturn(FakeAccounts.FAKE_ACCOUNT);

        // WHEN
        SwiftMessageFileResponse actualResponse = parseSwiftMessage.parseSwiftMessage(fileMX, AccountStatementFile.builder().name("SomeName").build(), null);

        //THEN
        assertNotNull(actualResponse);
        verify(createApiLogUseCase).execute(any());
        verify(createAccountStatementUseCase, never()).execute(any(), anyString(), anyList());
    }

    @Test
    void parseSwiftMessage_whenNoTransactionPresent_callsPostWriteUseCaseWithOperationCreate() throws Exception {
        // GIVEN
        File fileMT = new File("src/test/java/pt/jumia/services/brad/domain/utils/files/MT053_with_no_transactions.TXT");

        when(readAccountsUseCase.execute(anyString())).thenReturn(FakeAccounts.FAKE_ACCOUNT);

        SwiftMessageFileResponse actualResponse = parseSwiftMessage.parseSwiftMessage(fileMT,
            AccountStatementFile.builder().name("SomeName").build(), null);

        assertNotNull(actualResponse);
        verify(createAccountStatementUseCase, never()).execute(any(), anyString(), anyList());
        verify(postStatementWriteUseCase, times(1)).execute(any(), any());
    }

    @Test
    void parseSwiftMessage_whenNextStatementIsAvailable_createsStatementWithNextStatement() throws Exception {
        File fileMX = new File("src/test/java/pt/jumia/services/brad/domain/utils/files/CAMT053_100626001_2024042402061458");

        AbstractMessage message = AbstractMessageParser.getMessage(fileMX);
        ConvertFile convertFile = AbstractMessageParser.getConvertFile(message);
        SwiftMessageFileResponse response = AbstractMessageParser.generateSwiftMessageFileResponse(convertFile, message);

        when(overallSettings.getSftpSettings()).thenReturn(sftpSettings);
        when(sftpSettings.getSftpRunMode(anyLong())).thenReturn(SftpRunMode.LIVE);
        when(readAccountsUseCase.execute(anyString())).thenReturn(FakeAccounts.FAKE_ACCOUNT);
        when(createAccountStatementUseCase.execute(any(), anyString(), anyList(), anyString())).thenReturn(FakeAccountStatements.FAKE_ACCOUNT_STATEMENT);
        SwiftMessageFileResponse actualResponse = parseSwiftMessage.parseSwiftMessage(fileMX, AccountStatementFile.builder().name("SomeName").build(), "nextStatementId");

        assertNotNull(actualResponse);
        verify(createAccountStatementUseCase).execute(eq(response.getStatement()), eq(response.getAccountNumber()), eq(response.getTransactions()), eq("nextStatementId"));
        verify(createApiLogUseCase).execute(any());

    }

}
