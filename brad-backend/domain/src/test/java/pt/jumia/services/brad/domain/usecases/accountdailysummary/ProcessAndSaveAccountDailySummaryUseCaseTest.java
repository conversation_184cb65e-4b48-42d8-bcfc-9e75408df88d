package pt.jumia.services.brad.domain.usecases.accountdailysummary;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.AccountDailySummary;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountDailySummaries;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.entities.fake.FakeFxRates;
import pt.jumia.services.brad.domain.entities.fake.FakeTransaction;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.AccountDailySummaryRepository;
import pt.jumia.services.brad.domain.usecases.accountstatement.ReadAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.transactions.ReadTransactionUseCase;

@ExtendWith(MockitoExtension.class)
public class ProcessAndSaveAccountDailySummaryUseCaseTest {

    @Mock
    private AccountDailySummaryRepository accountDailySummaryRepository;

    @Mock
    private ReadAccountDailySummaryUseCase readAccountDailySummaryUseCase;

    @Mock
    private ReadAccountStatementUseCase readAccountStatementUseCase;

    @Mock
    private ReadTransactionUseCase readTransactionUseCase;

    @InjectMocks
    private ProcessAndSaveAccountDailySummaryUseCase processAndSaveAccountDailySummaryUseCase;

    private Account account;
    private AccountDailySummary accountDailySummary;
    private AccountStatement accountStatement;
    private Transaction creditTransaction;
    private Transaction debitTransaction;

    @BeforeEach
    void setup() {

        account = FakeAccounts.FAKE_ACCOUNT.toBuilder()
            .id(123L)
            .balance(BigDecimal.valueOf(1000))
            .build();

        accountDailySummary = FakeAccountDailySummaries.FAKE_ACCOUNT_DAILY_SUMMARY.toBuilder()
            .finalBalance(BigDecimal.valueOf(1000))
            .finalBalanceUsd(BigDecimal.valueOf(1000))
            .build();

        accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
            .currency(FakeCurrencies.EUR)
            .fxRates(Set.of(FakeFxRates.FAKE_FX_RATE_EUR_USD.toBuilder().bid(BigDecimal.ONE).build()))
            .initialAmount(BigDecimal.valueOf(1000))
            .build();

        creditTransaction = FakeTransaction.FAKE_TRANSACTION.toBuilder()
            .amount(BigDecimal.valueOf(100))
            .accountStatement(accountStatement)
            .fxRates(Set.of(FakeFxRates.FAKE_FX_RATE_EUR_USD.toBuilder().bid(BigDecimal.ONE).build()))
            .direction(Direction.CREDIT)
            .transactionDate(LocalDate.now())
            .build();

        debitTransaction = FakeTransaction.FAKE_TRANSACTION.toBuilder()
            .amount(BigDecimal.valueOf(100))
            .accountStatement(accountStatement)
            .fxRates(Set.of(FakeFxRates.FAKE_FX_RATE_EUR_USD.toBuilder().bid(BigDecimal.ONE).build()))
            .direction(Direction.DEBIT)
            .transactionDate(LocalDate.now())
            .build();
    }

    @Test
    void processBatch_whenAccountBalanceMatchesFinalBalance_thenNoProcessing() throws EntityErrorsException {
        // GIVEN
        when(readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(any(), any())).thenReturn(accountDailySummary);

        // WHEN
        processAndSaveAccountDailySummaryUseCase.processBatch(List.of(account), null);

        // THEN
        verify(accountDailySummaryRepository, never()).upsert(any());
    }

    @Test
    void processBatch_whenAccountBalanceDoesNotMatchFinalBalance_thenProcessAndSave() throws EntityErrorsException {
        // GIVEN
        final Integer MULTIPLIER = 1000;
        List<Transaction> transactions = new ArrayList<>();
        for (int i = 0; i < MULTIPLIER; i++) {
            transactions.add(
                creditTransaction.toBuilder()
                    .fxRates(Set.of(FakeFxRates.FAKE_FX_RATE_EUR_USD.toBuilder().bid(BigDecimal.TEN).build()))
                    .valueDate(LocalDate.now().minusDays(11))
                    .build());
        }
        accountDailySummary = accountDailySummary.toBuilder().finalBalance(BigDecimal.valueOf(500)).build();
        when(readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(any(), any()))
            .thenReturn(accountDailySummary)
            .thenReturn(null);
        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(List.of(
            accountStatement.toBuilder().fxRates(Set.of(FakeFxRates.FAKE_FX_RATE_EUR_USD.toBuilder().bid(BigDecimal.TEN).build()))
                .build()));
        when(readTransactionUseCase.executeAll(any(), any(), any())).thenReturn(transactions).thenReturn(transactions)
            .thenReturn(transactions)
            .thenReturn(Collections.emptyList());

        // WHEN
        processAndSaveAccountDailySummaryUseCase.processBatch(List.of(account), null);

        // THEN
        ArgumentCaptor<AccountDailySummary> accountDailySummaryArgumentCaptor = ArgumentCaptor.forClass(AccountDailySummary.class);
        verify(accountDailySummaryRepository, atLeastOnce()).upsert(accountDailySummaryArgumentCaptor.capture());
        AccountDailySummary savedAccountDailySummary = accountDailySummaryArgumentCaptor.getValue();
        assertEquals(account, savedAccountDailySummary.getAccount());
        assertEquals(new BigDecimal(100 * MULTIPLIER), savedAccountDailySummary.getTotalCreditAmount());
        assertEquals(new BigDecimal(10 * 100 * MULTIPLIER), savedAccountDailySummary.getTotalCreditAmountUsd());
        assertEquals(new BigDecimal(0), savedAccountDailySummary.getTotalDebitAmount());
        assertEquals(new BigDecimal(0), savedAccountDailySummary.getTotalDebitAmountUsd());
        assertEquals(0, savedAccountDailySummary.getDebitTransactionsCount());
        assertEquals(MULTIPLIER, savedAccountDailySummary.getCreditTransactionsCount());
        assertEquals(new BigDecimal(100 * MULTIPLIER), savedAccountDailySummary.getNetAmount());
        assertEquals(new BigDecimal(10 * 100 * MULTIPLIER), savedAccountDailySummary.getNetAmountUsd());
        assertEquals(new BigDecimal(1000), savedAccountDailySummary.getInitialBalance());
        assertEquals(new BigDecimal(1000 * 10), savedAccountDailySummary.getInitialBalanceUsd());
        assertEquals(new BigDecimal(1000 + 100 * MULTIPLIER), savedAccountDailySummary.getFinalBalance());
        assertEquals(new BigDecimal(1000 * 10 + 10 * 100 * MULTIPLIER), savedAccountDailySummary.getFinalBalanceUsd());


    }

    @Test
    void processBatch_whenAccountThereIsAnSummary_thenProcessUsingThatSummaryFinalBalanceAsInitialBalance() throws EntityErrorsException {
        // GIVEN
        final Integer MULTIPLIER = 1000;
        List<Transaction> transactions = new ArrayList<>();
        for (int i = 0; i < MULTIPLIER; i++) {
            transactions.add(
                creditTransaction.toBuilder()
                    .fxRates(Set.of(FakeFxRates.FAKE_FX_RATE_EUR_USD.toBuilder().bid(BigDecimal.TEN).build()))
                    .valueDate(LocalDate.now().minusDays(11))
                    .build());
        }
        accountDailySummary = accountDailySummary.toBuilder().finalBalance(BigDecimal.valueOf(500)).finalBalanceUsd(BigDecimal.valueOf(500))
            .build();
        when(readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(any(), any()))
            .thenReturn(accountDailySummary)
            .thenReturn(accountDailySummary)
            .thenReturn(accountDailySummary);
        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(List.of(
            accountStatement.toBuilder().fxRates(Set.of(FakeFxRates.FAKE_FX_RATE_EUR_USD.toBuilder().bid(BigDecimal.TEN).build()))
                .build()));
        when(readTransactionUseCase.executeAll(any(), any(), any())).thenReturn(transactions).thenReturn(transactions)
            .thenReturn(transactions).thenReturn(Collections.emptyList());

        // WHEN
        processAndSaveAccountDailySummaryUseCase.processBatch(List.of(account), null);

        // THEN
        ArgumentCaptor<AccountDailySummary> accountDailySummaryArgumentCaptor = ArgumentCaptor.forClass(AccountDailySummary.class);
        verify(accountDailySummaryRepository, atLeastOnce()).upsert(accountDailySummaryArgumentCaptor.capture());
        AccountDailySummary savedAccountDailySummary = accountDailySummaryArgumentCaptor.getValue();
        assertEquals(account, savedAccountDailySummary.getAccount());
        assertEquals(new BigDecimal(100 * MULTIPLIER), savedAccountDailySummary.getTotalCreditAmount());
        assertEquals(new BigDecimal(10 * 100 * MULTIPLIER), savedAccountDailySummary.getTotalCreditAmountUsd());
        assertEquals(new BigDecimal(0), savedAccountDailySummary.getTotalDebitAmount());
        assertEquals(new BigDecimal(0), savedAccountDailySummary.getTotalDebitAmountUsd());
        assertEquals(0, savedAccountDailySummary.getDebitTransactionsCount());
        assertEquals(MULTIPLIER, savedAccountDailySummary.getCreditTransactionsCount());
        assertEquals(new BigDecimal(100 * MULTIPLIER), savedAccountDailySummary.getNetAmount());
        assertEquals(new BigDecimal(10 * 100 * MULTIPLIER), savedAccountDailySummary.getNetAmountUsd());
        assertEquals(accountDailySummary.getFinalBalance(), savedAccountDailySummary.getInitialBalance());
        assertEquals(accountDailySummary.getFinalBalanceUsd(), savedAccountDailySummary.getInitialBalanceUsd());
        assertEquals(accountDailySummary.getFinalBalanceUsd().add(new BigDecimal(100 * MULTIPLIER)),
            savedAccountDailySummary.getFinalBalance());
        assertEquals(accountDailySummary.getFinalBalanceUsd().add(new BigDecimal(10 * 100 * MULTIPLIER)),
            savedAccountDailySummary.getFinalBalanceUsd());

    }

    @Test
    void processBatch_whenFinalDirectionIsDebit_thenProcessAndSave() throws EntityErrorsException {
        // GIVEN
        accountDailySummary = accountDailySummary.toBuilder().finalBalance(BigDecimal.valueOf(500)).build();
        when(readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(any(), any()))
            .thenReturn(accountDailySummary)
            .thenReturn(null);
        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(List.of(accountStatement));
        when(readTransactionUseCase.executeAll(any(), any(), any())).thenReturn(List.of(debitTransaction));

        // WHEN
        processAndSaveAccountDailySummaryUseCase.processBatch(List.of(account), null);

        // THEN
        ArgumentCaptor<AccountDailySummary> accountDailySummaryArgumentCaptor = ArgumentCaptor.forClass(AccountDailySummary.class);
        verify(accountDailySummaryRepository, atLeastOnce()).upsert(accountDailySummaryArgumentCaptor.capture());
        AccountDailySummary savedAccountDailySummary = accountDailySummaryArgumentCaptor.getValue();
        assertEquals(account, savedAccountDailySummary.getAccount());
        assertEquals(new BigDecimal(0), savedAccountDailySummary.getTotalCreditAmount());
        assertEquals(new BigDecimal(0), savedAccountDailySummary.getTotalCreditAmountUsd());
        assertEquals(new BigDecimal(100), savedAccountDailySummary.getTotalDebitAmount());
        assertEquals(new BigDecimal(100), savedAccountDailySummary.getTotalDebitAmountUsd());
        assertEquals(1, savedAccountDailySummary.getDebitTransactionsCount());
        assertEquals(0, savedAccountDailySummary.getCreditTransactionsCount());
        assertEquals(new BigDecimal(-100), savedAccountDailySummary.getNetAmount());
        assertEquals(new BigDecimal(-100), savedAccountDailySummary.getNetAmountUsd());
        assertEquals(new BigDecimal(1000), savedAccountDailySummary.getInitialBalance());
        assertEquals(new BigDecimal(900), savedAccountDailySummary.getFinalBalance());


    }

    @Test
    void processBatch_whenThereIsAnUptoDateSummaryDate_thenProcessAndSaveFromASpecificDate() throws EntityErrorsException {
        // GIVEN
        accountDailySummary = accountDailySummary.toBuilder().finalBalance(BigDecimal.valueOf(500)).build();
        when(readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(any(), any()))
            .thenReturn(accountDailySummary)
            .thenReturn(
                accountDailySummary.toBuilder().finalBalance(accountStatement.getFinalAmount())
                    .finalBalanceUsd(accountStatement.getAmountInUsd(accountStatement.getFinalAmount())).build())
            .thenReturn(null);

        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(List.of(accountStatement, accountStatement.toBuilder()
            .finalAmount(BigDecimal.valueOf(1000)).finalDate(LocalDate.now().minusDays(111)).build()));
        when(readTransactionUseCase.executeAll(any(), any(), any())).thenReturn(List.of(debitTransaction));

        // WHEN
        processAndSaveAccountDailySummaryUseCase.processBatch(List.of(account), null);

        // THEN
        ArgumentCaptor<AccountDailySummary> accountDailySummaryArgumentCaptor = ArgumentCaptor.forClass(AccountDailySummary.class);
        ArgumentCaptor<AccountStatementFilters> statementFiltersArgumentCaptor = ArgumentCaptor.forClass(AccountStatementFilters.class);
        verify(accountDailySummaryRepository, atLeastOnce()).upsert(accountDailySummaryArgumentCaptor.capture());
        verify(readAccountStatementUseCase, atLeastOnce()).execute(statementFiltersArgumentCaptor.capture(), any(), any());
        List<AccountStatementFilters> usedFilters = statementFiltersArgumentCaptor.getAllValues();
        assertEquals(2, usedFilters.size());
        assertEquals(usedFilters.get(1).getInitialDateStart(), accountStatement.getInitialDate());

    }


    @Test
    void processBatch_whenMinTrxDateIsBeforeStatementInitialDate_thenProcessAndSaveFromMinTransactionDate() throws EntityErrorsException {
        // GIVEN
        accountDailySummary = accountDailySummary.toBuilder().finalBalance(BigDecimal.valueOf(500)).build();
        when(readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(any(), any()))
            .thenReturn(accountDailySummary)
            .thenReturn(
                accountDailySummary.toBuilder().finalBalance(accountStatement.getFinalAmount())
                    .finalBalanceUsd(accountStatement.getAmountInUsd(accountStatement.getFinalAmount())).build())
            .thenReturn(null);

        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(List.of(accountStatement, accountStatement.toBuilder()
            .finalAmount(BigDecimal.valueOf(1000)).finalDate(LocalDate.now().minusDays(111)).build()));
        when(readTransactionUseCase.executeAll(any(), any(), any())).thenReturn(
            List.of(debitTransaction.toBuilder().transactionDate(accountStatement.getInitialDate().minusDays(10)).build()));

        // WHEN
        processAndSaveAccountDailySummaryUseCase.processBatch(List.of(account), null);

        // THEN
        ArgumentCaptor<AccountDailySummary> accountDailySummaryArgumentCaptor = ArgumentCaptor.forClass(AccountDailySummary.class);
        ArgumentCaptor<AccountStatementFilters> statementFiltersArgumentCaptor = ArgumentCaptor.forClass(AccountStatementFilters.class);
        verify(accountDailySummaryRepository, atLeastOnce()).upsert(accountDailySummaryArgumentCaptor.capture());
        verify(readAccountStatementUseCase, atLeastOnce()).execute(statementFiltersArgumentCaptor.capture(), any(), any());
        List<AccountStatementFilters> usedFilters = statementFiltersArgumentCaptor.getAllValues();
        assertEquals(2, usedFilters.size());
        assertEquals(usedFilters.get(1).getInitialDateStart(), accountStatement.getInitialDate().minusDays(10));

    }


    @Test
    void processBatch_whenThereIsMoreThanOneDatesInATrxPage_thenProcessAndSaveForEachDateAppropriately() throws EntityErrorsException {
        // GIVEN
        final Integer MULTIPLIER = 1000;
        List<Transaction> transactions = new ArrayList<>();
        for (int i = 0; i < MULTIPLIER; i++) {
            if (i % 2 == 0) {
                transactions.add(creditTransaction);
            } else {
                transactions.add(debitTransaction.toBuilder()
                    .fxRates(Set.of(
                        FakeFxRates.FAKE_FX_RATE_EUR_USD.toBuilder().rateDate(LocalDate.now().minusDays(1)).bid(BigDecimal.ONE).build()))
                    .transactionDate(LocalDate.now().minusDays(1))
                    .build());
            }
        }
        accountDailySummary = accountDailySummary.toBuilder().finalBalance(BigDecimal.valueOf(500)).build();

        when(readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(any(), any()))
            .thenReturn(accountDailySummary)
            .thenReturn(null);
        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(List.of(accountStatement));
        when(readTransactionUseCase.executeAll(any(), any(), any())).thenReturn(transactions).thenReturn(transactions)
            .thenReturn(transactions).thenReturn(Collections.emptyList());

        // WHEN
        processAndSaveAccountDailySummaryUseCase.processBatch(List.of(account), null);

        // THEN
        ArgumentCaptor<AccountDailySummary> accountDailySummaryArgumentCaptor = ArgumentCaptor.forClass(AccountDailySummary.class);
        verify(accountDailySummaryRepository, atLeastOnce()).upsert(accountDailySummaryArgumentCaptor.capture());
        AccountDailySummary savedAccountDailySummaryDebit = accountDailySummaryArgumentCaptor.getAllValues().get(0);
        AccountDailySummary savedAccountDailySummaryCredit = accountDailySummaryArgumentCaptor.getAllValues().get(1);

        assertEquals(account, savedAccountDailySummaryDebit.getAccount());
        assertEquals(BigDecimal.ZERO, savedAccountDailySummaryDebit.getTotalCreditAmount());
        assertEquals(BigDecimal.ZERO, savedAccountDailySummaryDebit.getTotalCreditAmountUsd());
        assertEquals(new BigDecimal(100 * MULTIPLIER / 2), savedAccountDailySummaryDebit.getTotalDebitAmount());
        assertEquals(new BigDecimal(100 * MULTIPLIER / 2), savedAccountDailySummaryDebit.getTotalDebitAmountUsd());
        assertEquals(MULTIPLIER / 2, savedAccountDailySummaryDebit.getDebitTransactionsCount());
        assertEquals(0, savedAccountDailySummaryDebit.getCreditTransactionsCount());
        assertEquals(new BigDecimal(100 * MULTIPLIER / 2).negate(), savedAccountDailySummaryDebit.getNetAmount());
        assertEquals(new BigDecimal(100 * MULTIPLIER / 2).negate(), savedAccountDailySummaryDebit.getNetAmountUsd());
        assertEquals(new BigDecimal(1000), savedAccountDailySummaryDebit.getInitialBalance());
        assertEquals(new BigDecimal(1000 - 100 * MULTIPLIER / 2), savedAccountDailySummaryDebit.getFinalBalance());

        assertEquals(account, savedAccountDailySummaryCredit.getAccount());
        assertEquals(BigDecimal.ZERO, savedAccountDailySummaryCredit.getTotalDebitAmount());
        assertEquals(BigDecimal.ZERO, savedAccountDailySummaryCredit.getTotalDebitAmountUsd());
        assertEquals(new BigDecimal(100 * MULTIPLIER / 2), savedAccountDailySummaryCredit.getTotalCreditAmount());
        assertEquals(new BigDecimal(100 * MULTIPLIER / 2), savedAccountDailySummaryCredit.getTotalCreditAmountUsd());
        assertEquals(MULTIPLIER / 2, savedAccountDailySummaryCredit.getCreditTransactionsCount());
        assertEquals(0, savedAccountDailySummaryCredit.getDebitTransactionsCount());
        assertEquals(new BigDecimal(100 * MULTIPLIER / 2), savedAccountDailySummaryCredit.getNetAmount());
        assertEquals(new BigDecimal(100 * MULTIPLIER / 2), savedAccountDailySummaryCredit.getNetAmountUsd());
        assertEquals(new BigDecimal(1000 - 100 * MULTIPLIER / 2), savedAccountDailySummaryCredit.getInitialBalance());
        assertEquals(new BigDecimal(1000 - 100 * MULTIPLIER / 2), savedAccountDailySummaryCredit.getInitialBalanceUsd());
        assertEquals(new BigDecimal((1000 - 100 * MULTIPLIER / 2) + (100 * MULTIPLIER / 2)),
            savedAccountDailySummaryCredit.getFinalBalance());
        assertEquals(new BigDecimal((1000 - 100 * MULTIPLIER / 2) + (100 * MULTIPLIER / 2)),
            savedAccountDailySummaryCredit.getFinalBalanceUsd());

    }

    @Test
    void processBatch_whenTransactionsOnADateIsMoreThanOnePage_thenProcessAndSaveSuccessfully() throws EntityErrorsException {
        // GIVEN
        final Integer MULTIPLIER = 1000;
        List<Transaction> page1 = new ArrayList<>();
        List<Transaction> page2 = new ArrayList<>();
        for (int i = 0; i < MULTIPLIER; i++) {
            page1.add(creditTransaction);
        }
        for (int i = 0; i < MULTIPLIER / 2; i++) {
            page2.add(creditTransaction);
        }
        accountDailySummary = accountDailySummary.toBuilder().finalBalance(BigDecimal.valueOf(500)).build();
        when(readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(any(), any()))
            .thenReturn(accountDailySummary)
            .thenReturn(null);
        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(List.of(accountStatement));
        when(readTransactionUseCase.executeAll(any(), any(), any())).thenReturn(page1).thenReturn(page1).thenReturn(page1)
            .thenReturn(page2);

        // WHEN
        processAndSaveAccountDailySummaryUseCase.processBatch(List.of(account), null);

        // THEN
        ArgumentCaptor<AccountDailySummary> accountDailySummaryArgumentCaptor = ArgumentCaptor.forClass(AccountDailySummary.class);
        verify(accountDailySummaryRepository, atLeastOnce()).upsert(accountDailySummaryArgumentCaptor.capture());
        AccountDailySummary savedAccountDailySummary = accountDailySummaryArgumentCaptor.getValue();
        assertEquals(account, savedAccountDailySummary.getAccount());
        int multiplier = MULTIPLIER + MULTIPLIER / 2;
        assertEquals(new BigDecimal(100 * multiplier), savedAccountDailySummary.getTotalCreditAmount());
        assertEquals(new BigDecimal(100 * multiplier), savedAccountDailySummary.getTotalCreditAmountUsd());
        assertEquals(new BigDecimal(0), savedAccountDailySummary.getTotalDebitAmount());
        assertEquals(new BigDecimal(0), savedAccountDailySummary.getTotalDebitAmountUsd());
        assertEquals(0, savedAccountDailySummary.getDebitTransactionsCount());
        assertEquals(multiplier, savedAccountDailySummary.getCreditTransactionsCount());
        assertEquals(new BigDecimal(100 * multiplier), savedAccountDailySummary.getNetAmount());
        assertEquals(new BigDecimal(100 * multiplier), savedAccountDailySummary.getNetAmountUsd());
        assertEquals(new BigDecimal(1000), savedAccountDailySummary.getInitialBalance());
        assertEquals(new BigDecimal(1000 + 100 * multiplier), savedAccountDailySummary.getFinalBalance());

    }

    @Test
    void processBatch_whenPage2StartsFromADifferentDate_thenProcessAndSaveSuccessfully() throws EntityErrorsException {
        // GIVEN
        final Integer MULTIPLIER = 1000;
        List<Transaction> page1 = new ArrayList<>();
        List<Transaction> page2 = new ArrayList<>();
        for (int i = 0; i < MULTIPLIER; i++) {
            page1.add(debitTransaction);
        }
        for (int i = 0; i < MULTIPLIER; i++) {
            page2.add(creditTransaction.toBuilder()
                .fxRates(Set.of(
                    FakeFxRates.FAKE_FX_RATE_EUR_USD.toBuilder().rateDate(LocalDate.now().minusDays(10)).bid(BigDecimal.ONE).build()))
                .transactionDate(LocalDate.now().minusDays(10)).build());
        }
        accountDailySummary = accountDailySummary.toBuilder().finalBalance(BigDecimal.valueOf(500)).build();
        when(readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(any(), any()))
            .thenReturn(accountDailySummary)
            .thenReturn(null);
        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(List.of(accountStatement));
        when(readTransactionUseCase.executeAll(any(), any(), any())).thenReturn(page1).thenReturn(page1).thenReturn(page1).thenReturn(page2)
            .thenReturn(Collections.emptyList());

        // WHEN
        processAndSaveAccountDailySummaryUseCase.processBatch(List.of(account), null);

        // THEN
        ArgumentCaptor<AccountDailySummary> accountDailySummaryArgumentCaptor = ArgumentCaptor.forClass(AccountDailySummary.class);
        verify(accountDailySummaryRepository, atLeastOnce()).upsert(accountDailySummaryArgumentCaptor.capture());
        AccountDailySummary savedAccountDailySummaryDebit = accountDailySummaryArgumentCaptor.getAllValues().get(0);
        AccountDailySummary savedAccountDailySummaryCredit = accountDailySummaryArgumentCaptor.getAllValues().get(1);

        assertEquals(account, savedAccountDailySummaryDebit.getAccount());
        assertEquals(BigDecimal.ZERO, savedAccountDailySummaryDebit.getTotalCreditAmount());
        assertEquals(BigDecimal.ZERO, savedAccountDailySummaryDebit.getTotalCreditAmountUsd());
        assertEquals(new BigDecimal(100 * MULTIPLIER), savedAccountDailySummaryDebit.getTotalDebitAmount());
        assertEquals(new BigDecimal(100 * MULTIPLIER), savedAccountDailySummaryDebit.getTotalDebitAmountUsd());
        assertEquals(MULTIPLIER, savedAccountDailySummaryDebit.getDebitTransactionsCount());
        assertEquals(0, savedAccountDailySummaryDebit.getCreditTransactionsCount());
        assertEquals(new BigDecimal(100 * MULTIPLIER).negate(), savedAccountDailySummaryDebit.getNetAmount());
        assertEquals(new BigDecimal(100 * MULTIPLIER).negate(), savedAccountDailySummaryDebit.getNetAmountUsd());
        assertEquals(new BigDecimal(1000), savedAccountDailySummaryDebit.getInitialBalance());
        assertEquals(new BigDecimal(1000 - 100 * MULTIPLIER), savedAccountDailySummaryDebit.getFinalBalance());

        assertEquals(account, savedAccountDailySummaryCredit.getAccount());
        assertEquals(BigDecimal.ZERO, savedAccountDailySummaryCredit.getTotalDebitAmount());
        assertEquals(BigDecimal.ZERO, savedAccountDailySummaryCredit.getTotalDebitAmountUsd());
        assertEquals(new BigDecimal(100 * MULTIPLIER), savedAccountDailySummaryCredit.getTotalCreditAmount());
        assertEquals(new BigDecimal(100 * MULTIPLIER), savedAccountDailySummaryCredit.getTotalCreditAmountUsd());
        assertEquals(MULTIPLIER, savedAccountDailySummaryCredit.getCreditTransactionsCount());
        assertEquals(0, savedAccountDailySummaryCredit.getDebitTransactionsCount());
        assertEquals(new BigDecimal(100 * MULTIPLIER), savedAccountDailySummaryCredit.getNetAmount());
        assertEquals(new BigDecimal(100 * MULTIPLIER), savedAccountDailySummaryCredit.getNetAmountUsd());
        assertEquals(new BigDecimal(1000 - 100 * MULTIPLIER), savedAccountDailySummaryCredit.getInitialBalance());
        assertEquals(new BigDecimal(1000 - 100 * MULTIPLIER), savedAccountDailySummaryCredit.getInitialBalanceUsd());
        assertEquals(new BigDecimal((1000 - 100 * MULTIPLIER) + (100 * MULTIPLIER)),
            savedAccountDailySummaryCredit.getFinalBalance());
        assertEquals(new BigDecimal((1000 - 100 * MULTIPLIER) + (100 * MULTIPLIER)),
            savedAccountDailySummaryCredit.getFinalBalanceUsd());

    }

    @Test
    void processBatch_wheThereExistsASummaryMissingFxRate_thenProcessFromThatDate() throws EntityErrorsException {
        // GIVEN
        LocalDate missingFxRateDate = LocalDate.now().minusDays(20);
        AccountDailySummary summaryWithMissingFxRate = accountDailySummary.toBuilder()
            .transactionDate(missingFxRateDate)
            .fxRate(null)
            .build();

        accountDailySummary = accountDailySummary.toBuilder().finalBalance(BigDecimal.valueOf(500)).build();
        when(readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(any(), any()))
            .thenReturn(accountDailySummary)
            .thenReturn(
                accountDailySummary.toBuilder().finalBalance(accountStatement.getFinalAmount())
                    .finalBalanceUsd(accountStatement.getAmountInUsd(accountStatement.getFinalAmount())).build())
            .thenReturn(null);
        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(List.of(accountStatement, accountStatement.toBuilder()
            .finalAmount(BigDecimal.valueOf(1000)).finalDate(LocalDate.now().minusDays(111)).build()));
        when(readTransactionUseCase.executeAll(any(), any(), any())).thenReturn(List.of(debitTransaction));
        when(readAccountDailySummaryUseCase.executeOldestMissingFxRate(anyLong())).thenReturn(summaryWithMissingFxRate);

        // WHEN
        processAndSaveAccountDailySummaryUseCase.processBatch(List.of(account), null);

        // THEN
        ArgumentCaptor<AccountDailySummary> accountDailySummaryArgumentCaptor = ArgumentCaptor.forClass(AccountDailySummary.class);
        ArgumentCaptor<AccountStatementFilters> statementFiltersArgumentCaptor = ArgumentCaptor.forClass(AccountStatementFilters.class);
        verify(accountDailySummaryRepository, atLeastOnce()).upsert(accountDailySummaryArgumentCaptor.capture());
        verify(readAccountStatementUseCase, atLeastOnce()).execute(statementFiltersArgumentCaptor.capture(), any(), any());
        List<AccountStatementFilters> usedFilters = statementFiltersArgumentCaptor.getAllValues();
        assertEquals(2, usedFilters.size());
        assertEquals(usedFilters.get(1).getInitialDateStart(), missingFxRateDate);
    }

    @Test
    void processBatch_wheThereIsNoSummary_thenItGoesThroughAllPagesToFindStartDate() throws EntityErrorsException {
        // GIVEN
        LocalDate missingFxRateDate = LocalDate.now().minusDays(20);
        AccountDailySummary summaryWithMissingFxRate = accountDailySummary.toBuilder()
            .transactionDate(missingFxRateDate)
            .fxRate(null)
            .build();
        List<AccountStatement> page1 = new ArrayList<>();
        for (int i = 0; i < 11; i++) {
            page1.add(accountStatement.toBuilder()
                .finalAmount(BigDecimal.valueOf(1000)).finalDate(LocalDate.now().minusDays(111)).build());
        }

        accountDailySummary = accountDailySummary.toBuilder().finalBalance(BigDecimal.valueOf(500)).build();
        when(readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(any(), any()))
            .thenReturn(accountDailySummary)
            .thenReturn(
                accountDailySummary.toBuilder().finalBalance(accountStatement.getFinalAmount())
                    .finalBalanceUsd(accountStatement.getAmountInUsd(accountStatement.getFinalAmount())).build())
            .thenReturn(null);
        when(readAccountStatementUseCase.execute(any(), any(), any()))
            .thenReturn(page1)
            .thenReturn(page1)
            .thenReturn(Collections.emptyList())
            .thenReturn(page1)
            .thenReturn(page1)
            .thenReturn(Collections.emptyList());
        when(readTransactionUseCase.executeAll(any(), any(), any())).thenReturn(List.of(debitTransaction));
        when(readAccountDailySummaryUseCase.executeOldestMissingFxRate(anyLong())).thenReturn(summaryWithMissingFxRate);

        // WHEN
        processAndSaveAccountDailySummaryUseCase.processBatch(List.of(account), null);

        // THEN
        ArgumentCaptor<AccountDailySummary> accountDailySummaryArgumentCaptor = ArgumentCaptor.forClass(AccountDailySummary.class);
        ArgumentCaptor<AccountStatementFilters> statementFiltersArgumentCaptor = ArgumentCaptor.forClass(AccountStatementFilters.class);
        verify(accountDailySummaryRepository, atLeastOnce()).upsert(accountDailySummaryArgumentCaptor.capture());
        verify(readAccountStatementUseCase, atLeastOnce()).execute(statementFiltersArgumentCaptor.capture(), any(), any());
        List<AccountStatementFilters> usedFilters = statementFiltersArgumentCaptor.getAllValues();
        assertEquals(4, usedFilters.size());
        assertEquals(usedFilters.get(3).getInitialDateStart(), LocalDate.now().minusYears(1000));
    }

}
