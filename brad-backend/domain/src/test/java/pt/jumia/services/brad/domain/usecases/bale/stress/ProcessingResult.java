package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Builder;
import lombok.Value;

import java.time.Duration;

@Value
@Builder
public class ProcessingResult {
    
    int threadId;
    long processedCount;
    Duration executionTime;
    boolean success;
    
    @Builder.Default
    String error = null;
    
    public static ProcessingResult success(int threadId, long processedCount, Duration executionTime) {
        return ProcessingResult.builder()
                .threadId(threadId)
                .processedCount(processedCount)
                .executionTime(executionTime)
                .success(true)
                .build();
    }
    
    public static ProcessingResult failure(int threadId, String error) {
        return ProcessingResult.builder()
                .threadId(threadId)
                .processedCount(0)
                .executionTime(Duration.ZERO)
                .success(false)
                .error(error)
                .build();
    }
}
