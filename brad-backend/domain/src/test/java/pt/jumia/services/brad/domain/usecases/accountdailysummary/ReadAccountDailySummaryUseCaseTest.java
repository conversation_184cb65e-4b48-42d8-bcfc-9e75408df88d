package pt.jumia.services.brad.domain.usecases.accountdailysummary;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.account.AccountDailySummary;
import pt.jumia.services.brad.domain.entities.dtos.GroupedAccountDailySummaryDto;
import pt.jumia.services.brad.domain.entities.dtos.StackedCashPositionDto;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashEvolutionFilters;
import pt.jumia.services.brad.domain.entities.filter.accountdailysummary.CashPositionFilters;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.AccountDailySummaryRepository;

@ExtendWith(MockitoExtension.class)
public class ReadAccountDailySummaryUseCaseTest {

    @Mock
    private AccountDailySummaryRepository accountDailySummaryRepository;

    @InjectMocks
    private ReadAccountDailySummaryUseCase readAccountDailySummaryUseCase;

    private AccountDailySummary accountDailySummary;

    @BeforeEach
    void setup() {

        accountDailySummary = AccountDailySummary.builder()
            .transactionDate(LocalDate.now())
            .build();
    }

    @Test
    void executeLatestByAccountId_AndDate_whenSummaryExists_thenReturnSummary() {
        // GIVEN
        when(accountDailySummaryRepository.findLatestByAccountIdAndDate(any(), any())).thenReturn(Optional.of(accountDailySummary));

        // WHEN
        AccountDailySummary result = readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(123L, LocalDate.now());

        // THEN
        assertEquals(accountDailySummary, result);
        verify(accountDailySummaryRepository, atLeastOnce()).findLatestByAccountIdAndDate(any(), any());
    }

    @Test
    void executeLatestByAccountId_AndDate_whenNoSummaryExists_thenThrowNotFoundException() {
        // GIVEN
        when(accountDailySummaryRepository.findLatestByAccountIdAndDate(any(), any())).thenReturn(Optional.empty());

        // WHEN & THEN
        assertThrows(NotFoundException.class, () -> readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(123L, LocalDate.now()));
        verify(accountDailySummaryRepository, atLeastOnce()).findLatestByAccountIdAndDate(any(), any());
    }

    @Test
    void executeLatestByAccountId_AndDate_whenRepositoryThrowsException_thenThrowException() {
        // GIVEN
        when(accountDailySummaryRepository.findLatestByAccountIdAndDate(any(),any())).thenThrow(RuntimeException.class);

        // WHEN & THEN
        assertThrows(RuntimeException.class, () -> readAccountDailySummaryUseCase.executeLatestByAccountIdAndDate(123L, LocalDate.now()));
        verify(accountDailySummaryRepository, atLeastOnce()).findLatestByAccountIdAndDate(any(),any());
    }

    @Test
    void executeCashEvolution_whenSummaryExists_thenReturnSummary() {
        // GIVEN
        CashEvolutionFilters filters = CashEvolutionFilters.builder().build();
        final GroupedAccountDailySummaryDto dto = getDummyGroupedAccountDailySummaryDto();
        when(accountDailySummaryRepository.findCashEvolution(any())).thenReturn(List.of(dto));

        // WHEN
        List<GroupedAccountDailySummaryDto> result = readAccountDailySummaryUseCase.executeCashEvolution(filters);

        // THEN
        assertEquals(dto, result.get(0));
        verify(accountDailySummaryRepository, atLeastOnce()).findCashEvolution(any());
    }

    @Test
    void executeCashPosition_whenSummaryExists_thenReturnSummary() {
        // GIVEN
        CashPositionFilters filters = CashPositionFilters.builder().build();
        final GroupedAccountDailySummaryDto dto = getDummyGroupedAccountDailySummaryDto();
        when(accountDailySummaryRepository.findCashPosition(any())).thenReturn(List.of(dto));

        // WHEN
        List<GroupedAccountDailySummaryDto> result = readAccountDailySummaryUseCase.executeCashPosition(filters);

        // THEN
        assertEquals(dto, result.get(0));
        verify(accountDailySummaryRepository, atLeastOnce()).findCashPosition(any());
    }

    @Test
    void executeCashPositionStacked_whenSummariesExist_thenReturnStackedSummaries() {
        // GIVEN
        CashPositionFilters filters = CashPositionFilters.builder().build();
        StackedCashPositionDto dto = StackedCashPositionDto.builder()
            .parentGroupLabel("parentGroupLabel")
            .stack(List.of(getDummyGroupedAccountDailySummaryDto())).build();
        when(accountDailySummaryRepository.findCashPositionStacked(any())).thenReturn(List.of(dto));

        // WHEN
        List<StackedCashPositionDto> result = readAccountDailySummaryUseCase.executeCashPositionStacked(filters);

        // THEN
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("parentGroupLabel", result.get(0).getParentGroupLabel());
        assertEquals(1, result.get(0).getStack().size());
        assertEquals("groupLabel", result.get(0).getStack().get(0).getGroupLabel());
    }

    @Test
    void executeCashPositionStacked_whenNoSummariesExist_thenReturnEmptyList() {
        // GIVEN
        CashPositionFilters filters = CashPositionFilters.builder().build();
        when(accountDailySummaryRepository.findCashPositionStacked(any())).thenReturn(List.of());

        // WHEN
        List<StackedCashPositionDto> result = readAccountDailySummaryUseCase.executeCashPositionStacked(filters);

        // THEN
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    private GroupedAccountDailySummaryDto getDummyGroupedAccountDailySummaryDto() {

        return GroupedAccountDailySummaryDto.builder()
            .groupLabel("groupLabel")
            .creditAmountUsd(BigDecimal.TEN)
            .debitAmountUsd(BigDecimal.TEN)
            .finalBalanceUsd(BigDecimal.TEN)
            .initialBalanceUsd(BigDecimal.TEN)
            .build();
    }

}
