package pt.jumia.services.brad.domain.usecases.accountstatement;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.fake.FakeTransaction;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.AccountDailySummaryRepository;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementRepository;
import pt.jumia.services.brad.domain.repository.brad.TransactionRepository;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.UpdateAccountStatementFileUseCase;

public class DeleteStatementsAtomicallyHelperTest {

    @Mock
    private AccountStatementRepository accountStatementRepository;

    @Mock
    private TransactionRepository transactionRepository;

    @Mock
    private UpdateAccountStatementFileUseCase updateAccountStatementFileUseCase;

    @Mock
    private AccountDailySummaryRepository accountDailySummaryRepository;

    @InjectMocks
    private DeleteStatementsAtomicallyHelper deleteStatementsAtomicallyHelper;

    private AccountStatement lastImportedStatement;
    private Long statementId;

    @BeforeEach
    public void setUp() {

        MockitoAnnotations.openMocks(this);

        // GIVEN
        lastImportedStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
            .id(1L)
            .previousStatement(null)
            .status(AccountStatementStatus.IMPORTED)
            .build();
        statementId = 1L;
    }

    @Test
    public void deleteAtomically_whenNextStatementExists_thenDiscardAndDelete() throws EntityErrorsException {
        // GIVEN
        AccountStatement nextStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
            .id(2L)
            .previousStatement(lastImportedStatement)
            .status(AccountStatementStatus.IMPORTED)
            .build();

        when(accountStatementRepository.findByPreviousStatementId(lastImportedStatement))
            .thenReturn(Optional.of(nextStatement));
        when(transactionRepository.findAll(any(), any(), any())).thenReturn(List.of(FakeTransaction.FAKE_TRANSACTION));


        // WHEN
        deleteStatementsAtomicallyHelper.deleteAtomically(lastImportedStatement);

        // THEN
        verify(accountStatementRepository).upsert(argThat(stmt ->
            stmt.getStatus() == AccountStatementStatus.DISCARDED && stmt.getPreviousStatement() == null));

        verify(accountStatementRepository, times(2)).upsert(any());
        verify(transactionRepository).deleteByAccountStatementId(statementId);
        verify(accountDailySummaryRepository).deleteByDates(anyLong(),any(),any());

        verify(accountStatementRepository).deleteById(statementId);
    }

    @Test
    public void deleteAtomically_whenNoNextStatementExists_thenDiscardOnly() throws EntityErrorsException {
        // GIVEN
        when(accountStatementRepository.findByPreviousStatementId(lastImportedStatement))
            .thenReturn(Optional.empty());
        when(transactionRepository.findAll(any(), any(), any())).thenReturn(List.of(FakeTransaction.FAKE_TRANSACTION));
        // WHEN
        deleteStatementsAtomicallyHelper.deleteAtomically(lastImportedStatement);

        // THEN
        verify(accountStatementRepository).upsert(argThat(stmt ->
            stmt.getStatus() == AccountStatementStatus.DISCARDED));

        verify(transactionRepository).deleteByAccountStatementId(statementId);
        verify(accountDailySummaryRepository).deleteByDates(anyLong(), any(),any());
        verify(accountStatementRepository).deleteById(statementId);
    }

    @Test
    public void deleteAtomically_whenDetachFails_thenLogError() throws EntityErrorsException {
        // GIVEN
        doThrow(new RuntimeException("Detach error"))
            .when(updateAccountStatementFileUseCase).detachFromStatement(statementId);
        when(transactionRepository.findAll(any(), any(), any())).thenReturn(List.of(FakeTransaction.FAKE_TRANSACTION));

        // WHEN
        deleteStatementsAtomicallyHelper.deleteAtomically(lastImportedStatement);

        // THEN
        verify(transactionRepository, never()).deleteByAccountStatementId(anyLong());
        verify(accountStatementRepository, never()).deleteById(anyLong());
    }

}
