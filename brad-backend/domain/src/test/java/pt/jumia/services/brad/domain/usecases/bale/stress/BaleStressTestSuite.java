package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.BatchProcessingConfig;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.streaming.StreamingConfig;
import pt.jumia.services.brad.domain.usecases.bale.ReadBaleUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive stress testing suite for the Bale Sync process.
 * 
 * This test suite covers:
 * - Performance testing with large datasets
 * - Memory pressure testing
 * - Concurrent processing testing
 * - Error scenario testing
 * - Long-running process testing
 * - Resource exhaustion testing
 * 
 * Usage:
 * - Run individual tests for specific scenarios
 * - Use @EnabledIf conditions for environment-specific tests
 * - Monitor JVM metrics during test execution
 * 
 * <AUTHOR> Principal Java Engineer
 * @since 2.0
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class BaleStressTestSuite {

    @Mock
    private ReadBaleUseCase readBaleUseCase;

    @Mock
    private ReadViewEntityUseCase readViewEntityUseCase;

    @Mock
    private BatchProcessingConfig batchProcessingConfig;

    private StressTestDataGenerator dataGenerator;
    private PerformanceMonitor performanceMonitor;
    private MemoryMonitor memoryMonitor;

    private List<ViewEntity> testViewEntities;
    private StressTestConfig stressTestConfig;

    @BeforeEach
    void setUp() throws Exception {
        log.info("Setting up stress test environment");
        
        // Initialize mocked components
        dataGenerator = new StressTestDataGenerator();
        performanceMonitor = new PerformanceMonitor();
        memoryMonitor = new MemoryMonitor();
        
        testViewEntities = dataGenerator.createTestViewEntities(5);
        
        stressTestConfig = StressTestConfig.builder()
            .maxMemoryUsage(0.9)
            .maxExecutionTime(Duration.ofMinutes(30))
            .expectedThroughput(1000)
            .build();
        
        performanceMonitor.reset();
        memoryMonitor.reset();
        
        log.info("Stress test environment ready - {} view entities", testViewEntities.size());
    }

    /**
     * Test processing performance with large datasets.
     * Validates throughput and memory usage under high load.
     */
    @Test
    void testLargeDatasetProcessing() throws Exception {
        log.info("Starting large dataset processing stress test");
        
        Instant startTime = Instant.now();
        performanceMonitor.startMonitoring();
        memoryMonitor.startMonitoring();
        
        try {
            List<ViewEntity> largeDatasetViews = dataGenerator.createLargeDatasetViews(100000);
            
            List<Bale> processedBales = readBaleUseCase.execute(largeDatasetViews, false);
            
            Duration executionTime = Duration.between(startTime, Instant.now());
            PerformanceMetrics metrics = performanceMonitor.getMetrics();
            MemoryMetrics memoryMetrics = memoryMonitor.getMetrics();
            
            log.info("Large dataset processing completed:");
            log.info("- Execution time: {} seconds", executionTime.getSeconds());
            log.info("- Processed bales: {}", processedBales.size());
            log.info("- Throughput: {} bales/second", processedBales.size() / executionTime.getSeconds());
            log.info("- Peak memory usage: {} MB", memoryMetrics.getPeakMemoryUsage() / (1024 * 1024));
            log.info("- Average CPU usage: {}%", metrics.getAverageCpuUsage());
            
            assertTrue(executionTime.compareTo(stressTestConfig.getMaxExecutionTime()) < 0,
                    "Execution time exceeded maximum allowed time");
            assertTrue(memoryMetrics.getPeakMemoryUsage() < stressTestConfig.getMaxMemoryUsage(),
                    "Memory usage exceeded maximum allowed threshold");
            assertTrue(processedBales.size() / executionTime.getSeconds() >= stressTestConfig.getExpectedThroughput(),
                    "Throughput below expected minimum");
            
        } finally {
            performanceMonitor.stopMonitoring();
            memoryMonitor.stopMonitoring();
        }
    }

    /**
     * Test streaming processing with memory constraints.
     * Validates constant memory usage and streaming behavior.
     */
    @Test
    void testStreamingMemoryConstraints() throws Exception {
        log.info("Starting streaming memory constraints stress test");
        
        StreamingConfig streamingConfig = StreamingConfig.builder()
            .pageSize(500)
            .maxPages(1000)
            .memoryThreshold(0.7)
            .build();
        
        memoryMonitor.startMonitoring();
        
        try {
            List<ViewEntity> streamingViews = dataGenerator.createStreamingTestViews(50000);
            
            readBaleUseCase.execute(streamingViews, true);
            
            MemoryMetrics memoryMetrics = memoryMonitor.getMetrics();
            
            log.info("Streaming processing completed:");
            log.info("- Peak memory usage: {} MB", memoryMetrics.getPeakMemoryUsage() / (1024 * 1024));
            log.info("- Memory variance: {} MB", memoryMetrics.getMemoryVariance() / (1024 * 1024));
            log.info("- GC count: {}", memoryMetrics.getGcCount());
            
            assertTrue(memoryMetrics.getPeakMemoryUsage() < streamingConfig.getMemoryThreshold(),
                    "Streaming processing exceeded memory threshold");
            assertTrue(memoryMetrics.getMemoryVariance() < 100 * 1024 * 1024,
                    "Memory variance too high for streaming processing");
            
        } finally {
            memoryMonitor.stopMonitoring();
        }
    }

    /**
     * Test concurrent processing with multiple threads.
     * Validates thread safety and resource contention handling.
     */
    @Test
    void testConcurrentProcessing() throws Exception {
        log.info("Starting concurrent processing stress test");
        
        int numberOfThreads = 10;
        ExecutorService executor = Executors.newFixedThreadPool(numberOfThreads);
        
        List<CompletableFuture<ProcessingResult>> futures = new ArrayList<>();
        
        performanceMonitor.startMonitoring();
        
        try {
            for (int i = 0; i < numberOfThreads; i++) {
                final int threadId = i;
                CompletableFuture<ProcessingResult> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        log.info("Thread {} starting processing", threadId);
                        
                        List<ViewEntity> threadViews = dataGenerator.createThreadSpecificViews(threadId, 1000);
                        Instant startTime = Instant.now();
                        
                        List<Bale> processedBales = readBaleUseCase.execute(threadViews, false);
                        
                        Duration executionTime = Duration.between(startTime, Instant.now());
                        
                        log.info("Thread {} completed - {} bales in {} seconds", 
                                threadId, processedBales.size(), executionTime.getSeconds());
                        
                        return ProcessingResult.builder()
                            .threadId(threadId)
                            .processedCount(processedBales.size())
                            .executionTime(executionTime)
                            .success(true)
                            .build();
                            
                    } catch (Exception e) {
                        log.error("Thread {} failed", threadId, e);
                        return ProcessingResult.builder()
                            .threadId(threadId)
                            .processedCount(0)
                            .executionTime(Duration.ZERO)
                            .success(false)
                            .error(e.getMessage())
                            .build();
                    }
                }, executor);
                
                futures.add(future);
            }
            
            List<ProcessingResult> results = futures.stream()
                .map(CompletableFuture::join)
                .toList();
            
            PerformanceMetrics metrics = performanceMonitor.getMetrics();
            
            long totalProcessed = results.stream()
                .mapToLong(ProcessingResult::getProcessedCount)
                .sum();
            
            long successfulThreads = results.stream()
                .mapToLong(result -> result.isSuccess() ? 1 : 0)
                .sum();
            
            log.info("Concurrent processing completed:");
            log.info("- Total processed: {} bales", totalProcessed);
            log.info("- Successful threads: {}/{}", successfulThreads, numberOfThreads);
            log.info("- Average execution time: {} seconds", 
                    results.stream().mapToLong(r -> r.getExecutionTime().getSeconds()).average().orElse(0));
            log.info("- Peak CPU usage: {}%", metrics.getPeakCpuUsage());
            
            assertEquals(numberOfThreads, successfulThreads, "Some threads failed during concurrent processing");
            assertTrue(totalProcessed > 0, "No bales were processed");
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(60, TimeUnit.SECONDS);
            performanceMonitor.stopMonitoring();
        }
    }

    /**
     * Test error scenario handling and recovery.
     * Validates system resilience under error conditions.
     */
    @Test
    void testErrorScenarioHandling() throws Exception {
        log.info("Starting error scenario handling stress test");
        
        ErrorScenarioConfig errorConfig = ErrorScenarioConfig.builder()
            .databaseFailureRate(0.1)
            .memoryExhaustionRate(0.05)
            .timeoutRate(0.02)
            .build();
        
        dataGenerator.configureErrorScenarios(errorConfig);
        
        try {
            List<ViewEntity> errorProneViews = dataGenerator.createErrorProneViews(10000);
            
            List<Bale> processedBales = readBaleUseCase.execute(errorProneViews, false);
            
            ErrorMetrics errorMetrics = dataGenerator.getErrorMetrics();
            
            log.info("Error scenario testing completed:");
            log.info("- Database failures encountered: {}", errorMetrics.getDatabaseFailures());
            log.info("- Memory exhaustion events: {}", errorMetrics.getMemoryExhaustionEvents());
            log.info("- Timeout events: {}", errorMetrics.getTimeoutEvents());
            log.info("- Successfully processed: {} bales", processedBales.size());
            log.info("- Error recovery rate: {}%", errorMetrics.getRecoveryRate() * 100);
            
            assertTrue(errorMetrics.getRecoveryRate() > 0.8, 
                    "Error recovery rate too low");
            assertTrue(processedBales.size() > errorProneViews.size() * 0.5,
                    "Too many bales failed processing");
            
        } finally {
            dataGenerator.resetErrorScenarios();
        }
    }

    /**
     * Test long-running process stability.
     * Validates system stability over extended periods.
     */
    @Test
    void testLongRunningProcessStability() throws Exception {
        log.info("Starting long-running process stability test");
        
        Duration testDuration = Duration.ofMinutes(15);
        Instant endTime = Instant.now().plus(testDuration);
        
        performanceMonitor.startMonitoring();
        memoryMonitor.startMonitoring();
        
        int iteration = 0;
        List<ProcessingResult> iterationResults = new ArrayList<>();
        
        try {
            while (Instant.now().isBefore(endTime)) {
                iteration++;
                log.info("Long-running test iteration {}", iteration);
                
                Instant iterationStart = Instant.now();
                List<ViewEntity> iterationViews = dataGenerator.createIterationViews(iteration, 5000);
                
                List<Bale> processedBales = readBaleUseCase.execute(iterationViews, true);
                
                Duration iterationTime = Duration.between(iterationStart, Instant.now());
                
                ProcessingResult result = ProcessingResult.builder()
                    .threadId(iteration)
                    .processedCount(processedBales.size())
                    .executionTime(iterationTime)
                    .success(true)
                    .build();
                
                iterationResults.add(result);
                
                if (iteration % 10 == 0) {
                    MemoryMetrics currentMemory = memoryMonitor.getCurrentMetrics();
                    log.info("Memory status - Used: {} MB, Free: {} MB", 
                            currentMemory.getUsedMemory() / (1024 * 1024),
                            currentMemory.getFreeMemory() / (1024 * 1024));
                }
                
                Thread.sleep(1000);
            }
            
            PerformanceMetrics finalMetrics = performanceMonitor.getMetrics();
            MemoryMetrics finalMemoryMetrics = memoryMonitor.getMetrics();
            
            double averageThroughput = iterationResults.stream()
                .mapToDouble(r -> r.getProcessedCount() / (double) r.getExecutionTime().getSeconds())
                .average().orElse(0);
            
            log.info("Long-running test completed:");
            log.info("- Total iterations: {}", iteration);
            log.info("- Average throughput: {} bales/second", averageThroughput);
            log.info("- Memory stability: {} MB variance", 
                    finalMemoryMetrics.getMemoryVariance() / (1024 * 1024));
            log.info("- CPU stability: {}% variance", finalMetrics.getCpuVariance());
            
            assertTrue(averageThroughput > stressTestConfig.getExpectedThroughput() * 0.8,
                    "Throughput degraded significantly during long run");
            assertTrue(finalMemoryMetrics.getMemoryVariance() < 200 * 1024 * 1024,
                    "Memory usage too unstable during long run");
            
        } finally {
            performanceMonitor.stopMonitoring();
            memoryMonitor.stopMonitoring();
        }
    }

    /**
     * Test resource exhaustion scenarios.
     * Validates system behavior under resource constraints.
     */
    @Test
    void testResourceExhaustionScenarios() throws Exception {
        log.info("Starting resource exhaustion scenarios test");
        
        ResourceConstraints constraints = ResourceConstraints.builder()
            .maxMemoryMB(512)
            .maxCpuPercent(80)
            .maxDatabaseConnections(10)
            .build();
        
        dataGenerator.configureResourceConstraints(constraints);
        
        try {
            List<ViewEntity> resourceIntensiveViews = dataGenerator.createResourceIntensiveViews(20000);
            
            Instant startTime = Instant.now();
            List<Bale> processedBales = readBaleUseCase.execute(resourceIntensiveViews, true);
            Duration executionTime = Duration.between(startTime, Instant.now());
            
            ResourceMetrics resourceMetrics = dataGenerator.getResourceMetrics();
            
            log.info("Resource exhaustion test completed:");
            log.info("- Peak memory usage: {} MB", resourceMetrics.getPeakMemoryUsage());
            log.info("- Peak CPU usage: {}%", resourceMetrics.getPeakCpuUsage());
            log.info("- Peak database connections: {}", resourceMetrics.getPeakDatabaseConnections());
            log.info("- Processed bales: {}", processedBales.size());
            log.info("- Execution time: {} seconds", executionTime.getSeconds());
            
            assertTrue(resourceMetrics.getPeakMemoryUsage() <= constraints.getMaxMemoryMB() * 1.1,
                    "Memory usage exceeded constraints by too much");
            assertTrue(resourceMetrics.getPeakCpuUsage() <= constraints.getMaxCpuPercent() * 1.1,
                    "CPU usage exceeded constraints by too much");
            assertTrue(resourceMetrics.getPeakDatabaseConnections() <= constraints.getMaxDatabaseConnections(),
                    "Database connection limit exceeded");
            
        } finally {
            dataGenerator.resetResourceConstraints();
        }
    }

    /**
     * Test batch processing configuration optimization.
     * Validates different batch sizes and their impact on performance.
     */
    @Test
    void testBatchProcessingOptimization() throws Exception {
        log.info("Starting batch processing optimization test");
        
        int[] batchSizes = {100, 500, 1000, 2000, 5000};
        List<BatchOptimizationResult> results = new ArrayList<>();
        
        for (int batchSize : batchSizes) {
            log.info("Testing batch size: {}", batchSize);
            
            batchProcessingConfig.getBatch().setSize(batchSize);
            batchProcessingConfig.getBatch().setEnabled(true);
            
            List<ViewEntity> batchTestViews = dataGenerator.createBatchTestViews(10000);
            
            Instant startTime = Instant.now();
            performanceMonitor.startMonitoring();
            memoryMonitor.startMonitoring();
            
            List<Bale> processedBales = readBaleUseCase.execute(batchTestViews, false);
            
            Duration executionTime = Duration.between(startTime, Instant.now());
            PerformanceMetrics metrics = performanceMonitor.getMetrics();
            MemoryMetrics memoryMetrics = memoryMonitor.getMetrics();
            
            performanceMonitor.stopMonitoring();
            memoryMonitor.stopMonitoring();
            
            BatchOptimizationResult result = BatchOptimizationResult.builder()
                .batchSize(batchSize)
                .executionTime(executionTime)
                .throughput(processedBales.size() / (double) executionTime.getSeconds())
                .peakMemoryUsage(memoryMetrics.getPeakMemoryUsage())
                .averageCpuUsage(metrics.getAverageCpuUsage())
                .build();
            
            results.add(result);
            
            log.info("Batch size {} results: {} bales/sec, {} MB peak memory", 
                    batchSize, result.getThroughput(), result.getPeakMemoryUsage() / (1024 * 1024));
        }
        
        BatchOptimizationResult optimalResult = results.stream()
            .max((r1, r2) -> Double.compare(r1.getThroughput(), r2.getThroughput()))
            .orElseThrow();
        
        log.info("Batch processing optimization completed:");
        log.info("- Optimal batch size: {}", optimalResult.getBatchSize());
        log.info("- Optimal throughput: {} bales/second", optimalResult.getThroughput());
        log.info("- Optimal memory usage: {} MB", optimalResult.getPeakMemoryUsage() / (1024 * 1024));
        
        assertTrue(optimalResult.getThroughput() > stressTestConfig.getExpectedThroughput(),
                "Optimal batch configuration doesn't meet performance requirements");
    }
}
