package pt.jumia.services.brad.domain.entities.streaming;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class BaleProcessingResultTest {

    @Test
    void success_CreatesSuccessResult() {
        // Act
        BaleProcessingResult result = BaleProcessingResult.success(10);

        // Assert
        assertEquals(10, result.getSuccessCount());
        assertEquals(0, result.getErrorCount());
        assertFalse(result.hasCriticalErrors());
        assertNull(result.getErrorSummary());
    }

    @Test
    void withErrors_CreatesErrorResult() {
        // Act
        BaleProcessingResult result = BaleProcessingResult.withErrors(
                5, 3, true, "Database connection failed");

        // Assert
        assertEquals(5, result.getSuccessCount());
        assertEquals(3, result.getErrorCount());
        assertTrue(result.hasCriticalErrors());
        assertEquals("Database connection failed", result.getErrorSummary());
    }

    @Test
    void withErrors_NonCriticalErrors() {
        // Act
        BaleProcessingResult result = BaleProcessingResult.withErrors(
                8, 2, false, "Some accounts not found");

        // Assert
        assertEquals(8, result.getSuccessCount());
        assertEquals(2, result.getErrorCount());
        assertFalse(result.hasCriticalErrors());
        assertEquals("Some accounts not found", result.getErrorSummary());
    }

    @Test
    void builder_AllowsFullCustomization() {
        // Act
        BaleProcessingResult result = BaleProcessingResult.builder()
                .directSuccessCount(15)
                .directErrorCount(5)
                .directHasCriticalErrors(true)
                .errorSummary("Multiple errors occurred")
                .build();

        // Assert
        assertEquals(15, result.getSuccessCount());
        assertEquals(5, result.getErrorCount());
        assertTrue(result.hasCriticalErrors());
        assertEquals("Multiple errors occurred", result.getErrorSummary());
    }
}