package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class MemorySnapshot {
    
    long timestamp;
    long usedMemory;
    long freeMemory;
    long maxMemory;
    
    public static MemorySnapshot current() {
        Runtime runtime = Runtime.getRuntime();
        return MemorySnapshot.builder()
                .timestamp(System.currentTimeMillis())
                .usedMemory(runtime.totalMemory() - runtime.freeMemory())
                .freeMemory(runtime.freeMemory())
                .maxMemory(runtime.maxMemory())
                .build();
    }
}
