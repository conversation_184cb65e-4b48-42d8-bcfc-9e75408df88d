package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Builder;
import lombok.Value;

import java.time.Duration;

@Value
@Builder
public class BatchOptimizationResult {
    
    int batchSize;
    Duration executionTime;
    double throughput;
    long peakMemoryUsage;
    double averageCpuUsage;
    
    public double getEfficiencyScore() {
        double memoryEfficiency = 1.0 / (peakMemoryUsage / (1024.0 * 1024.0 * 1024.0));
        double timeEfficiency = throughput / 1000.0;
        double cpuEfficiency = 1.0 / (averageCpuUsage / 100.0);
        
        return (memoryEfficiency + timeEfficiency + cpuEfficiency) / 3.0;
    }
}
