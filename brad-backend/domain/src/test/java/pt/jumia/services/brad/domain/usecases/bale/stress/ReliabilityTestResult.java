package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class ReliabilityTestResult {
    String testName;
    int totalOperations;
    int successfulOperations;
    int failedOperations;
    int recoveredOperations;
    int dataConsistencyIssues;
    
    @Builder.Default
    boolean success = false;
    
    String errorMessage;
    
    public double getSuccessRate() {
        return totalOperations > 0 ? (double) successfulOperations / totalOperations : 0.0;
    }
    
    public double getRecoveryRate() {
        return failedOperations > 0 ? (double) recoveredOperations / failedOperations : 0.0;
    }
}
