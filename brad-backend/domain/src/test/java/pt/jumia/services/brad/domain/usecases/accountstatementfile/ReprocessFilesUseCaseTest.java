package pt.jumia.services.brad.domain.usecases.accountstatementfile;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileFilters;

@ExtendWith(MockitoExtension.class)
public class ReprocessFilesUseCaseTest {

    @Mock
    private ReadAccountStatementFileUseCase readAccountStatementFileUseCase;
    @Mock
    private ConsumeAccountStatementFileUseCase consumeAccountStatementFileUseCase;

    @InjectMocks
    private ReprocessFilesUseCase reprocessFilesUseCase;

    @Test
    void execute_whenFilesAvailableAndLessThanPageSize_thenConsumeFilesAndStop() throws Exception {
        // GIVEN
        ProcessingStatus processingStatus = ProcessingStatus.NEW;
        List<AccountStatementFile> files = new ArrayList<>();
        files.add(AccountStatementFile.builder().id(1L).name("file1.pdf").build());

        AccountStatementFileFilters filters = AccountStatementFileFilters.builder()
            .processingStatus(processingStatus)
            .build();
        PageFilters pageFilters = new PageFilters(1, 100);

        when(readAccountStatementFileUseCase.execute(filters, null, pageFilters))
            .thenReturn(files)
            .thenReturn(Collections.emptyList());

        // WHEN
        reprocessFilesUseCase.execute(AccountStatementFileFilters.builder().build());

        // THEN
        verify(readAccountStatementFileUseCase, times(1)).execute(filters, null, pageFilters);
        verify(consumeAccountStatementFileUseCase, times(1)).executeBatch(files);
    }

    @Test
    void execute_whenFilesAvailableAndEqualToPageSize_thenConsumeFilesAndContinue() throws Exception {
        // GIVEN
        ProcessingStatus processingStatus = ProcessingStatus.NEW;
        List<AccountStatementFile> filesPage1 = new ArrayList<>();
        for (long i = 0; i < 100; i++) {
            filesPage1.add(AccountStatementFile.builder().id(+i).name("file" + i + ".pdf").build());
        }
        List<AccountStatementFile> filesPage2 = new ArrayList<>();
        filesPage2.add(AccountStatementFile.builder().id(100L).name("file100.pdf").build());

        when(readAccountStatementFileUseCase.execute(any(), any(), any()))
            .thenReturn(filesPage1)
            .thenReturn(filesPage2)
            .thenReturn(Collections.emptyList());

        // WHEN
        reprocessFilesUseCase.execute(AccountStatementFileFilters.builder().build());

        // THEN
        verify(readAccountStatementFileUseCase, times(2)).execute(any(), eq(null), any());
        verify(consumeAccountStatementFileUseCase, times(1)).executeBatch(filesPage1);
        verify(consumeAccountStatementFileUseCase, times(1)).executeBatch(filesPage2);
    }

    @Test
    void execute_whenReadAccountStatementFileUseCaseThrowsException_thenPropagateException() throws Exception {
        // GIVEN
        ProcessingStatus processingStatus = ProcessingStatus.NEW;
        AccountStatementFileFilters filters = AccountStatementFileFilters.builder()
            .processingStatus(processingStatus)
            .build();
        PageFilters pageFilters = new PageFilters(1, 100);

        when(readAccountStatementFileUseCase.execute(filters, null, pageFilters))
            .thenThrow(new RuntimeException("Failed to read files"));

        // WHEN
        Exception exception = assertThrows(Exception.class, () -> {
            reprocessFilesUseCase.execute(AccountStatementFileFilters.builder().build());
        });

        // THEN
        assertEquals("Failed to read files", exception.getMessage());
        verify(consumeAccountStatementFileUseCase, never()).executeBatch(any());
    }

    @Test
    void execute_whenConsumeAccountStatementFileUseCaseThrowsException_thenPropagateException() throws Exception {
        // GIVEN
        ProcessingStatus processingStatus = ProcessingStatus.NEW;
        List<AccountStatementFile> files = new ArrayList<>();
        files.add(AccountStatementFile.builder().id(1L).name("file1.pdf").build());

        AccountStatementFileFilters filters = AccountStatementFileFilters.builder()
                .processingStatus(processingStatus)
                .build();
        PageFilters pageFilters = new PageFilters(1, 100);

        when(readAccountStatementFileUseCase.execute(filters, null, pageFilters))
                .thenReturn(files);
        doThrow(new RuntimeException("Failed to consume files")).when(consumeAccountStatementFileUseCase).executeBatch(files);

        // WHEN
        Exception exception = assertThrows(Exception.class, () -> {
            reprocessFilesUseCase.execute(AccountStatementFileFilters.builder().build());
        });

        // THEN
        assertEquals("Failed to consume files", exception.getMessage());
        verify(readAccountStatementFileUseCase, times(1)).execute(filters, null, pageFilters);
        verify(consumeAccountStatementFileUseCase, times(1)).executeBatch(files);
    }

    @Test
    void execute_filters_addFilters() throws Exception {
        // GIVEN
        ProcessingStatus processingStatus = ProcessingStatus.NEW;
        List<AccountStatementFile> files = new ArrayList<>();
        files.add(AccountStatementFile.builder().id(1L).name("file1.pdf").build());

        AccountStatementFileFilters requestFilters = AccountStatementFileFilters.builder()
                .ids(List.of(1L, 2L, 3L))
                .name("name")
                .statementId(123L)
                .accountId(555L)
                .createdAtFrom(LocalDateTime.MIN)
                .createdAtTo(LocalDateTime.MAX)
                .build();
        AccountStatementFileFilters expectedFilters = requestFilters.toBuilder()
                .processingStatus(processingStatus)
                .build();
        PageFilters pageFilters = new PageFilters(1, 100);

        when(readAccountStatementFileUseCase.execute(expectedFilters, null, pageFilters))
                .thenReturn(files)
                .thenReturn(Collections.emptyList());

        // WHEN
        reprocessFilesUseCase.execute(requestFilters);

        // THEN
        verify(readAccountStatementFileUseCase, times(1)).execute(expectedFilters, null, pageFilters);
        verify(consumeAccountStatementFileUseCase, times(1)).executeBatch(files);
    }

    @Test
    void execute_withValidFileIdAndNextStatementId_thenConsumeFile() throws Exception {
        // GIVEN
        Long accountStatementFileId = 1L;
        String nextStatementId = "next-statement-id";

        // WHEN
        reprocessFilesUseCase.execute(accountStatementFileId, nextStatementId);

        // THEN
        verify(consumeAccountStatementFileUseCase, times(1)).execute(accountStatementFileId, nextStatementId);
    }

    @Test
    void execute_withExceptionThrown_thenCatchAndLogException() throws Exception {
        // GIVEN
        Long accountStatementFileId = 1L;
        String nextStatementId = "next-statement-id";

        IOException testException = new IOException("Test exception");
        doThrow(testException).when(consumeAccountStatementFileUseCase).execute(accountStatementFileId, nextStatementId);

        // WHEN
        reprocessFilesUseCase.execute(accountStatementFileId, nextStatementId);

        // THEN
        verify(consumeAccountStatementFileUseCase, times(1)).execute(accountStatementFileId, nextStatementId);
    }

}
