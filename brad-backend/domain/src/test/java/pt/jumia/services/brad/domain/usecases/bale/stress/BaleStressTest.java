package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.BatchProcessingConfig;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeExecutionLogs;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.bale.ReadBaleUseCase;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCase;
import pt.jumia.services.brad.domain.usecases.bale.streaming.BaleStreamingOrchestrator;
import pt.jumia.services.brad.domain.usecases.executionlogs.CreateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.ReadExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.UpdateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Focused stress testing for the Bale Sync process.
 * 
 * This test suite validates:
 * - Performance with large datasets
 * - Memory usage under load
 * - Concurrent processing behavior
 * - Batch processing optimization
 * - Error handling resilience
 * 
 * <AUTHOR> Principal Java Engineer
 * @since 2.0
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
public class BaleStressTest {

    @Mock
    private BaleRepository baleRepository;

    @Mock
    private BradBaleRepository bradBaleRepository;

    @Mock
    private CreateExecutionLogsUseCase createExecutionLogsUseCase;

    @Mock
    private UpdateExecutionLogsUseCase updateExecutionLogsUseCase;

    @Mock
    private ReadExecutionLogsUseCase readExecutionLogsUseCase;

    @Mock
    private SyncBradBaleUseCase syncBradBaleUseCase;

    @Mock
    private ReadViewEntityUseCase readViewEntityUseCase;

    @Mock
    private BatchProcessingConfig batchProcessingConfig;

    @Mock
    private BaleStreamingOrchestrator streamingOrchestrator;

    @InjectMocks
    private ReadBaleUseCase readBaleUseCase;

    private List<ViewEntity> testViewEntities;
    private ExecutionLog testExecutionLog;

    @BeforeEach
    void setUp() throws Exception {
        log.info("Setting up stress test environment");
        
        testViewEntities = FakeViewEntity.getFakeViewEntity(10);
        testExecutionLog = FakeExecutionLogs.getFakeExecutionLogs(1).get(0);
        
        BatchProcessingConfig.Batch batchConfig = new BatchProcessingConfig.Batch();
        batchConfig.setEnabled(true);
        batchConfig.setSize(1000);
        batchConfig.setMaxBatches(500);
        batchConfig.setMemoryThreshold(0.8);
        
        when(batchProcessingConfig.getBatch()).thenReturn(batchConfig);
        when(readViewEntityUseCase.execute(ViewEntity.EntityType.BALE)).thenReturn(testViewEntities);
        when(createExecutionLogsUseCase.execute(any())).thenReturn(testExecutionLog);
        when(readExecutionLogsUseCase.execute(any())).thenReturn(testExecutionLog);
        doNothing().when(updateExecutionLogsUseCase).execute(any());
        doNothing().when(bradBaleRepository).createPartition(any());
        when(baleRepository.fetchCompanyId(any(ViewEntity.class), eq(false))).thenReturn("TEST_COMPANY");
        when(bradBaleRepository.findLastBaleInBradOfCompanyId(any())).thenReturn(Optional.empty());
        
        log.info("Stress test environment ready");
    }

    /**
     * Test processing performance with large datasets.
     * Validates throughput and execution time under high load.
     */
    @Test
    void testLargeDatasetProcessing() throws Exception {
        log.info("Starting large dataset processing stress test");
        
        int largeDatasetSize = 100000;
        List<Bale> largeBaleList = FakeBale.getFakeBale(largeDatasetSize);
        
        when(baleRepository.findAll(any(), any(), eq(false), any())).thenReturn(largeBaleList);
        when(syncBradBaleUseCase.execute(any(), any())).thenReturn(largeBaleList);
        
        Instant startTime = Instant.now();
        List<Bale> processedBales = readBaleUseCase.execute(testViewEntities, false);
        Duration executionTime = Duration.between(startTime, Instant.now());
        
        double throughput = processedBales.size() / (double) executionTime.getSeconds();
        
        log.info("Large dataset processing completed:");
        log.info("- Execution time: {} seconds", executionTime.getSeconds());
        log.info("- Processed bales: {}", processedBales.size());
        log.info("- Throughput: {} bales/second", throughput);
        
        assertTrue(processedBales.size() >= largeDatasetSize * testViewEntities.size(),
                "Not all bales were processed");
        assertTrue(executionTime.getSeconds() < 300, "Processing took too long");
        assertTrue(throughput > 100, "Throughput too low");
        
        verify(baleRepository, times(testViewEntities.size())).findAll(any(), any(), eq(false), any());
        verify(syncBradBaleUseCase, times(testViewEntities.size())).execute(any(), any());
    }

    /**
     * Test batch processing configuration optimization.
     * Validates different batch sizes and their impact on performance.
     */
    @Test
    void testBatchProcessingOptimization() throws Exception {
        log.info("Starting batch processing optimization test");
        
        int[] batchSizes = {100, 500, 1000, 2000, 5000};
        List<BatchPerformanceResult> results = new ArrayList<>();
        
        for (int batchSize : batchSizes) {
            log.info("Testing batch size: {}", batchSize);
            
            BatchProcessingConfig.Batch batchConfig = new BatchProcessingConfig.Batch();
            batchConfig.setSize(batchSize);
            batchConfig.setEnabled(true);
            batchConfig.setMaxBatches(100);
            batchConfig.setMemoryThreshold(0.8);
            
            when(batchProcessingConfig.getBatch()).thenReturn(batchConfig);
            
            List<Bale> batchTestBales = FakeBale.getFakeBale(10000);
            when(baleRepository.findAllBatched(any(), any(), eq(false), any(), anyInt(), eq(batchSize)))
                    .thenReturn(batchTestBales.subList(0, Math.min(batchSize, batchTestBales.size())));
            when(syncBradBaleUseCase.execute(any(), any())).thenReturn(batchTestBales);
            
            Instant startTime = Instant.now();
            List<Bale> processedBales = readBaleUseCase.execute(testViewEntities, false);
            Duration executionTime = Duration.between(startTime, Instant.now());
            
            double throughput = processedBales.size() / (double) executionTime.getSeconds();
            
            BatchPerformanceResult result = BatchPerformanceResult.builder()
                    .batchSize(batchSize)
                    .executionTime(executionTime)
                    .throughput(throughput)
                    .processedCount(processedBales.size())
                    .build();
            
            results.add(result);
            
            log.info("Batch size {} results: {} bales/sec, {} bales processed in {} seconds", 
                    batchSize, throughput, processedBales.size(), executionTime.getSeconds());
        }
        
        BatchPerformanceResult optimalResult = results.stream()
                .max((r1, r2) -> Double.compare(r1.getThroughput(), r2.getThroughput()))
                .orElseThrow();
        
        log.info("Batch processing optimization completed:");
        log.info("- Optimal batch size: {}", optimalResult.getBatchSize());
        log.info("- Optimal throughput: {} bales/second", optimalResult.getThroughput());
        
        assertTrue(optimalResult.getThroughput() > 100, "Optimal batch configuration too slow");
        assertTrue(results.size() == batchSizes.length, "Not all batch sizes were tested");
    }

    /**
     * Test concurrent processing with multiple threads.
     * Validates thread safety and performance under concurrent load.
     */
    @Test
    void testConcurrentProcessing() throws Exception {
        log.info("Starting concurrent processing stress test");
        
        int numberOfThreads = 5;
        ExecutorService executor = Executors.newFixedThreadPool(numberOfThreads);
        
        List<CompletableFuture<ProcessingResult>> futures = new ArrayList<>();
        List<Bale> concurrentTestBales = FakeBale.getFakeBale(1000);
        
        when(baleRepository.findAll(any(), any(), eq(false), any())).thenReturn(concurrentTestBales);
        when(syncBradBaleUseCase.execute(any(), any())).thenReturn(concurrentTestBales);
        
        try {
            for (int i = 0; i < numberOfThreads; i++) {
                final int threadId = i;
                CompletableFuture<ProcessingResult> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        log.info("Thread {} starting processing", threadId);
                        
                        Instant startTime = Instant.now();
                        List<Bale> processedBales = readBaleUseCase.execute(testViewEntities, false);
                        Duration executionTime = Duration.between(startTime, Instant.now());
                        
                        log.info("Thread {} completed - {} bales in {} seconds", 
                                threadId, processedBales.size(), executionTime.getSeconds());
                        
                        return ProcessingResult.success(threadId, processedBales.size(), executionTime);
                            
                    } catch (Exception e) {
                        log.error("Thread {} failed", threadId, e);
                        return ProcessingResult.failure(threadId, e.getMessage());
                    }
                }, executor);
                
                futures.add(future);
            }
            
            List<ProcessingResult> results = futures.stream()
                    .map(CompletableFuture::join)
                    .toList();
            
            long totalProcessed = results.stream()
                    .mapToLong(ProcessingResult::getProcessedCount)
                    .sum();
            
            long successfulThreads = results.stream()
                    .mapToLong(result -> result.isSuccess() ? 1 : 0)
                    .sum();
            
            double avgExecutionTime = results.stream()
                    .filter(ProcessingResult::isSuccess)
                    .mapToDouble(r -> r.getExecutionTime().getSeconds())
                    .average()
                    .orElse(0.0);
            
            log.info("Concurrent processing completed:");
            log.info("- Total processed: {} bales", totalProcessed);
            log.info("- Successful threads: {}/{}", successfulThreads, numberOfThreads);
            log.info("- Average execution time: {} seconds", avgExecutionTime);
            
            assertEquals(numberOfThreads, successfulThreads, "Some threads failed during concurrent processing");
            assertTrue(totalProcessed > 0, "No bales were processed");
            assertTrue(avgExecutionTime < 60, "Average execution time too high");
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(60, TimeUnit.SECONDS);
        }
    }

    /**
     * Test streaming processing performance.
     * Validates streaming mode behavior and memory efficiency.
     */
    @Test
    void testStreamingProcessingPerformance() throws Exception {
        log.info("Starting streaming processing performance test");
        
        List<Bale> streamingTestBales = FakeBale.getFakeBale(50000);
        
        BaleStreamingOrchestrator.StreamingSummary mockSummary = 
                BaleStreamingOrchestrator.StreamingSummary.builder()
                        .executionLogId(testExecutionLog.getId())
                        .viewEntityName("TEST_VIEW")
                        .totalProcessed(streamingTestBales.size())
                        .totalErrors(0)
                        .hasCriticalErrors(false)
                        .stopped(false)
                        .build();
        
        when(streamingOrchestrator.processBalesInStreams(any(), any(), any(), any()))
                .thenReturn(mockSummary);
        
        Instant startTime = Instant.now();
        readBaleUseCase.execute(testViewEntities, true);
        Duration executionTime = Duration.between(startTime, Instant.now());
        
        log.info("Streaming processing completed:");
        log.info("- Execution time: {} seconds", executionTime.getSeconds());
        log.info("- Processing mode: streaming");
        log.info("- Expected processed count: {}", streamingTestBales.size() * testViewEntities.size());
        
        assertTrue(executionTime.getSeconds() < 120, "Streaming processing took too long");
        verify(streamingOrchestrator, times(testViewEntities.size()))
                .processBalesInStreams(any(), any(), any(), any());
    }

    /**
     * Test memory threshold handling.
     * Validates behavior when memory usage approaches limits.
     */
    @Test
    void testMemoryThresholdHandling() throws Exception {
        log.info("Starting memory threshold handling test");
        
        BatchProcessingConfig.Batch batchConfig = new BatchProcessingConfig.Batch();
        batchConfig.setEnabled(true);
        batchConfig.setSize(10000);
        batchConfig.setMaxBatches(1000);
        batchConfig.setMemoryThreshold(0.5);
        
        when(batchProcessingConfig.getBatch()).thenReturn(batchConfig);
        
        List<Bale> memoryTestBales = FakeBale.getFakeBale(50000);
        when(baleRepository.findAllBatched(any(), any(), eq(false), any(), anyInt(), anyInt()))
                .thenReturn(memoryTestBales);
        when(syncBradBaleUseCase.execute(any(), any())).thenReturn(memoryTestBales);
        
        Runtime runtime = Runtime.getRuntime();
        long beforeMemory = runtime.totalMemory() - runtime.freeMemory();
        
        Instant startTime = Instant.now();
        List<Bale> processedBales = readBaleUseCase.execute(testViewEntities, false);
        Duration executionTime = Duration.between(startTime, Instant.now());
        
        long afterMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = afterMemory - beforeMemory;
        
        log.info("Memory threshold test completed:");
        log.info("- Memory increase: {} MB", memoryIncrease / (1024 * 1024));
        log.info("- Execution time: {} seconds", executionTime.getSeconds());
        log.info("- Processed bales: {}", processedBales.size());
        
        assertTrue(processedBales.size() > 0, "No bales were processed");
        assertTrue(memoryIncrease < 500 * 1024 * 1024, "Memory usage increased too much");
    }

    @lombok.Value
    @lombok.Builder
    private static class BatchPerformanceResult {
        int batchSize;
        Duration executionTime;
        double throughput;
        long processedCount;
    }
}
