package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class PerformanceSnapshot {
    
    long timestamp;
    double cpuUsage;
    
    public static PerformanceSnapshot current() {
        return PerformanceSnapshot.builder()
                .timestamp(System.currentTimeMillis())
                .cpuUsage(0.0)
                .build();
    }
}
