package pt.jumia.services.brad.domain.usecases.accountstatement;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;

@ExtendWith(MockitoExtension.class)
class RevalidateAccountStatementUseCaseTest {

    @Mock
    private ReadAccountStatementUseCase readAccountStatementUseCase;

    @Mock
    private ValidateAccountStatementUseCase validateAccountStatementUseCase;

    @InjectMocks
    private RevalidateAccountStatementUseCase revalidateAccountStatementUseCase;

    @Test
    void execute_whenValidAccountStatementWithNoPreviousStatements_thenSuccessfullyValidatesOnce() throws Exception {
        // GIVEN
        AccountStatement accountStatement = AccountStatement.builder()
            .id(1L)
            .status(AccountStatementStatus.REVIEW)
            .build();

        AccountStatement updatedAccountStatement = accountStatement.toBuilder()
            .status(AccountStatementStatus.OPEN)
            .build();

        when(readAccountStatementUseCase.executeByPreviousStatement(any()))
            .thenThrow(new NotFoundException("Previous statement not found"));

        // WHEN
        revalidateAccountStatementUseCase.execute(accountStatement);

        // THEN
        verify(validateAccountStatementUseCase).execute(updatedAccountStatement);
        verify(readAccountStatementUseCase).executeByPreviousStatement(updatedAccountStatement);
        verifyNoMoreInteractions(validateAccountStatementUseCase, readAccountStatementUseCase);
    }

    @Test
    void execute_whenValidateAccountStatementThrowsException_thenExceptionPropagates() throws Exception {
        // GIVEN
        AccountStatement accountStatement = AccountStatement.builder()
            .id(1L)
            .status(AccountStatementStatus.REVIEW)
            .build();

        AccountStatement updatedAccountStatement = accountStatement.toBuilder()
            .status(AccountStatementStatus.OPEN)
            .build();

        doThrow(new EntityErrorsException("Validation Error"))
            .when(validateAccountStatementUseCase)
            .execute(updatedAccountStatement);

        // WHEN
        try {
            revalidateAccountStatementUseCase.execute(accountStatement);
        } catch (EntityErrorsException e) {
            // Expected exception
        }

        // THEN
        verify(validateAccountStatementUseCase).execute(updatedAccountStatement);
        verifyNoMoreInteractions(validateAccountStatementUseCase, readAccountStatementUseCase);
    }

    @Test
    void execute_whenReadAccountStatementThrowsGenericException_thenStopsProcessing() throws Exception {
        // GIVEN
        AccountStatement accountStatement = AccountStatement.builder()
            .id(1L)
            .status(AccountStatementStatus.REVIEW)
            .build();

        AccountStatement updatedAccountStatement = accountStatement.toBuilder()
            .status(AccountStatementStatus.OPEN)
            .build();

        when(readAccountStatementUseCase.executeByPreviousStatement(updatedAccountStatement))
            .thenThrow(new DatabaseErrorsException("Database Error"));

        // WHEN
        revalidateAccountStatementUseCase.execute(accountStatement);

        // THEN
        verify(validateAccountStatementUseCase).execute(updatedAccountStatement);
        verify(readAccountStatementUseCase).executeByPreviousStatement(updatedAccountStatement);
        verifyNoMoreInteractions(validateAccountStatementUseCase, readAccountStatementUseCase);
    }

    @Test
    void execute_retryParentStatementUnsuccessfully_stopsRetryingFollowingStatements()
        throws NotFoundException, EntityErrorsException, DatabaseErrorsException {

        final AccountStatement invalidParentAccountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT
            .toBuilder()
            .status(AccountStatementStatus.REVIEW)
            .statusDescription(AccountStatementStatus.Description.ERROR_OPENING_BALANCE)
            .build();

        final AccountStatement invalidChildAccountStatement = AccountStatement.builder()
            .previousStatement(invalidParentAccountStatement)
            .status(AccountStatementStatus.REVIEW)
            .statusDescription(AccountStatementStatus.Description.ERROR_PREVIOUS_STATEMENT)
            .build();

        doReturn(invalidChildAccountStatement)
            .when(readAccountStatementUseCase)
            .executeByPreviousStatement(any());

        revalidateAccountStatementUseCase.execute(invalidParentAccountStatement);

        verify(validateAccountStatementUseCase, times(1))
            .execute(any(AccountStatement.class));
    }

    @Test
    void execute_retryParentStatementSuccessfully_retriesFollowingStatements()
        throws NotFoundException, EntityErrorsException, DatabaseErrorsException {

        final AccountStatement invalidParentAccountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT
            .toBuilder()
            .status(AccountStatementStatus.REVIEW)
            .statusDescription(AccountStatementStatus.Description.ERROR_PERIOD_OVERLAP)
            .build();

        final AccountStatement invalidChildAccountStatement = AccountStatement.builder()
            .previousStatement(
                invalidParentAccountStatement.toBuilder()
                    .status(AccountStatementStatus.IMPORTED)
                    .statusDescription(AccountStatementStatus.Description.IMPORTED)
                    .build())
            .status(AccountStatementStatus.REVIEW)
            .statusDescription(AccountStatementStatus.Description.ERROR_PREVIOUS_STATEMENT)
            .build();

        doReturn(invalidChildAccountStatement,
            invalidParentAccountStatement.toBuilder()
                .status(AccountStatementStatus.IMPORTED)
                .statusDescription(AccountStatementStatus.Description.IMPORTED)
                .build())
            .when(readAccountStatementUseCase)
            .executeByPreviousStatement(any());

        revalidateAccountStatementUseCase.execute(invalidParentAccountStatement);

        verify(validateAccountStatementUseCase, times(2))
            .execute(any(AccountStatement.class));
    }

    @Test
    void execute_validateWithInitialDateBeforePreviousStatementFinalDate_ErrorOpeningBalance()
        throws NotFoundException, EntityErrorsException, DatabaseErrorsException {

        final AccountStatement previousAccountStatement = AccountStatement.builder()
            .finalDate(LocalDate.of(2024, 1, 2))
            .status(AccountStatementStatus.IMPORTED)
            .build();

        final AccountStatement accountStatement = AccountStatement
            .builder()
            .initialDate(LocalDate.of(2024, 1, 1))
            .previousStatement(previousAccountStatement)
            .status(AccountStatementStatus.OPEN)
            .build();

        when(readAccountStatementUseCase.executeByPreviousStatement(any())).thenReturn(previousAccountStatement);

        revalidateAccountStatementUseCase.execute(accountStatement);

        verify(validateAccountStatementUseCase, times(1))
            .execute(any(AccountStatement.class));
    }

}

