package pt.jumia.services.brad.domain.entities.filter.bale;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.entities.filter.account.AccountFilters;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

public class BalesFiltersTest {

    private static BaleFilters filters;

    private static LocalDate postingDate;
    private static String baleTimestamp;
    private static String bankAccountTimestamp;


    @BeforeAll
    public static void setUp() {
        postingDate = LocalDate.now();
        baleTimestamp = "a timestamp";
        bankAccountTimestamp = "a timestamp";
        filters = BaleFilters.builder()
                .idCompany("idCompany")
                .accountNumber("account")
                .entryNo(Collections.singletonList(1))
                .documentNo("documentNo")
                .documentType("documentType")
                .postingDateStart(postingDate)
                .postingDateEnd(postingDate.plusDays(1))
                .accountPostingGroup("bankAccountPostingGroup")
                .description("description")
                .sourceCode("sourceCode")
                .reasonCode("reasonCode")
                .busLine("busLine")
                .department("department")
                .amount(BigDecimal.ONE)
                .remainingAmount(BigDecimal.ONE)
                .transactionCurrency(List.of("transactionCurrency"))
                .amountLcy(BigDecimal.ONE)
                .balanceAccountType("balanceAccountType")
                .isOpen(true)
                .isReversed(true)
                .postedBy("postedBy")
                .externalDocumentNo("externalDocumentNo")
                .baleTimestamp(baleTimestamp)
                .accountTimestamp(bankAccountTimestamp)
                .build();
    }

    @Test
    public void testGetIdCompany() {
        Assertions.assertEquals("idCompany", filters.getIdCompany());
    }

    @Test
    public void testGetAccount() {
        Assertions.assertEquals("account", filters.getAccountNumber());
    }

    @Test
    public void testGetEntryNo() {
        Assertions.assertEquals(1, filters.getEntryNo().get(0));
        Assertions.assertEquals(1, filters.getEntryNo().size());
    }

    @Test
    public void testGetDocumentNo() {
        Assertions.assertEquals("documentNo", filters.getDocumentNo());
    }

    @Test
    public void testGetDocumentType() {
        Assertions.assertEquals("documentType", filters.getDocumentType());
    }

    @Test
    public void testGetPostingDateStart() {
        Assertions.assertEquals(postingDate, filters.getPostingDateStart());
    }

    @Test
    public void testGetPostingDateEnd() {
        Assertions.assertEquals(postingDate.plusDays(1), filters.getPostingDateEnd());
    }

    @Test
    public void testGetBankAccountPostingGroup() {
        Assertions.assertEquals("bankAccountPostingGroup", filters.getAccountPostingGroup());
    }

    @Test
    public void testGetDescription() {
        Assertions.assertEquals("description", filters.getDescription());
    }

    @Test
    public void testGetSourceCode() {
        Assertions.assertEquals("sourceCode", filters.getSourceCode());
    }

    @Test
    public void testGetReasonCode() {
        Assertions.assertEquals("reasonCode", filters.getReasonCode());
    }

    @Test
    public void testGetBusLine() {
        Assertions.assertEquals("busLine", filters.getBusLine());
    }

    @Test
    public void testGetDepartment() {
        Assertions.assertEquals("department", filters.getDepartment());
    }

    @Test
    public void testGetAmount() {
        Assertions.assertEquals(BigDecimal.ONE, filters.getAmount());
    }

    @Test
    public void testGetRemainingAmount() {
        Assertions.assertEquals(BigDecimal.ONE, filters.getRemainingAmount());
    }

    @Test
    public void testGetTransactionCurrency() {
        Assertions.assertEquals(List.of("transactionCurrency"), filters.getTransactionCurrency());
    }

    @Test
    public void testGetAmountLcy() {
        Assertions.assertEquals(BigDecimal.ONE, filters.getAmountLcy());
    }

    @Test
    public void testGetBalanceAccountType() {
        Assertions.assertEquals("balanceAccountType", filters.getBalanceAccountType());
    }

    @Test
    public void testIsOpen() {
        Assertions.assertTrue(filters.getIsOpen());
    }

    @Test
    public void testIsReversed() {
        Assertions.assertTrue(filters.getIsReversed());
    }

    @Test
    public void testGetPostedBy() {
        Assertions.assertEquals("postedBy", filters.getPostedBy());
    }

    @Test
    public void testGetExternalDocumentNo() {
        Assertions.assertEquals("externalDocumentNo", filters.getExternalDocumentNo());
    }

    @Test
    public void testGetBaleTimestamp() {
        Assertions.assertEquals(baleTimestamp, filters.getBaleTimestamp());
    }

    @Test
    public void testGetBankAccountTimestamp() {
        Assertions.assertEquals(bankAccountTimestamp, filters.getAccountTimestamp());
    }

    @Test
    public void testBaleGetAsMapFilters() {
        Assertions.assertEquals(24, filters.getAsMap().size());
    }

    @Test
    public void testBaleGetNullAsMapFilters() {
        Assertions.assertEquals(1, AccountFilters.builder().build().getAsMap().size());
    }
}
