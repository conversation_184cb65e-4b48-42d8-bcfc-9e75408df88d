package pt.jumia.services.brad.domain.usecases.accountstatement;

import org.assertj.core.api.ThrowableAssert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.filter.accountstatementfile.AccountStatementFileFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus.Description;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.InvalidEntityException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.ReadAccountStatementFileUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatementfile.ReprocessFilesUseCase;

import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RetryAccountStatementUseCaseTest {

    private static final long STATEMENT_ID = 1L;
    private static final long FILE_ID = 10L;
    private static final long NEXT_STATEMENT_ID = 2L;

    @Mock
    private ReadAccountStatementUseCase readAccountStatementUseCase;

    @Mock
    private RevalidateAccountStatementUseCase revalidateAccountStatementUseCase;

    @Mock
    private ReadAccountStatementFileUseCase readAccountStatementFileUseCase;

    @Mock
    private UpdateAccountStatementUseCase updateAccountStatementUseCase;

    @Mock
    private DiscardAccountStatementUseCase discardAccountStatementUseCase;

    @Mock
    private ReprocessFilesUseCase reprocessFilesUseCase;

    @InjectMocks
    private RetryAccountStatementUseCase retryAccountStatementUseCase;

    @Test
    void execute_whenStatementHasStatusImported_thenReturnCannotBeRetriedMessage() throws NotFoundException, EntityErrorsException, DatabaseErrorsException {
        // GIVEN
        AccountStatement importedStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
                .status(AccountStatementStatus.IMPORTED)
                .statusDescription(AccountStatementStatus.Description.IMPORTED)
                .build();

        doReturn(importedStatement)
                .when(readAccountStatementUseCase)
                .execute(STATEMENT_ID);

        // WHEN
        String result = retryAccountStatementUseCase.execute(STATEMENT_ID);

        // THEN
        assertThat(result)
                .isEqualTo("Statement with in status - IMPORTED cannot be retried");
        verify(readAccountStatementUseCase).execute(STATEMENT_ID);
        verifyNoMoreInteractions(
                revalidateAccountStatementUseCase,
                readAccountStatementFileUseCase,
                updateAccountStatementUseCase,
                discardAccountStatementUseCase,
                reprocessFilesUseCase
        );
    }

    @Test
    void execute_whenParentStatementIsInReview_thenThrowInvalidEntityException() throws NotFoundException {
        // GIVEN
        final AccountStatement invalidParentAccountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT
                .toBuilder()
                .status(AccountStatementStatus.REVIEW)
                .statusDescription(AccountStatementStatus.Description.ERROR_OPENING_BALANCE)
                .build();

        final AccountStatement accountStatementToBeRetried = AccountStatement.builder()
                .id(STATEMENT_ID)
                .previousStatement(invalidParentAccountStatement)
                .status(AccountStatementStatus.REVIEW)
                .statusDescription(AccountStatementStatus.Description.ERROR_PREVIOUS_STATEMENT)
                .build();

        doReturn(accountStatementToBeRetried)
                .when(readAccountStatementUseCase)
                .execute(STATEMENT_ID);

        // WHEN
        ThrowableAssert.ThrowingCallable callable = () -> retryAccountStatementUseCase.execute(STATEMENT_ID);

        // THEN
        assertThatThrownBy(callable)
                .isExactlyInstanceOf(InvalidEntityException.class)
                .hasMessageContaining("Parent of statement with identifier 1 has invalid status");

        verify(readAccountStatementUseCase).execute(STATEMENT_ID);
        verifyNoMoreInteractions(
                revalidateAccountStatementUseCase,
                readAccountStatementFileUseCase,
                updateAccountStatementUseCase,
                discardAccountStatementUseCase,
                reprocessFilesUseCase
        );
    }

    @Test
    void execute_whenStatementFileExists_thenReprocessFile() throws NotFoundException, EntityErrorsException, DatabaseErrorsException {
        // GIVEN
        final AccountStatement accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT
                .toBuilder()
                .id(STATEMENT_ID)
                .status(AccountStatementStatus.REVIEW)
                .statusDescription(Description.ERROR_CLOSING_BALANCE)
                .build();

        final AccountStatement nextAccountStatement = AccountStatement.builder()
                .id(NEXT_STATEMENT_ID)
                .build();

        final AccountStatementFile statementFile = AccountStatementFile.builder()
                .id(FILE_ID)
                .build();

        doReturn(accountStatement)
                .when(readAccountStatementUseCase)
                .execute(STATEMENT_ID);

        doReturn(accountStatement)
                .when(updateAccountStatementUseCase)
                .execute(any());

        doReturn(nextAccountStatement)
                .when(readAccountStatementUseCase)
                .executeByPreviousStatement(accountStatement);

        doReturn(Collections.singletonList(statementFile))
                .when(readAccountStatementFileUseCase)
                .execute(any(AccountStatementFileFilters.class), eq(null), eq(null));

        // WHEN
        String result = retryAccountStatementUseCase.execute(STATEMENT_ID);

        // THEN
        assertThat(result)
                .isEqualTo("Statement file with with id - " + STATEMENT_ID + " is being reprocessed");

        verify(readAccountStatementUseCase).execute(STATEMENT_ID);
        verify(readAccountStatementUseCase).executeByPreviousStatement(accountStatement);
        verify(readAccountStatementFileUseCase).execute(any(AccountStatementFileFilters.class), eq(null), eq(null));
        verify(updateAccountStatementUseCase).execute(any(AccountStatement.class));
        verify(discardAccountStatementUseCase).execute(STATEMENT_ID);
        verify(reprocessFilesUseCase).execute(FILE_ID, String.valueOf(NEXT_STATEMENT_ID));
        verifyNoMoreInteractions(revalidateAccountStatementUseCase);
    }

    @Test
    void execute_whenNoStatementFileExists_thenRevalidateStatement() throws NotFoundException, EntityErrorsException, DatabaseErrorsException {
        // GIVEN
        final AccountStatement accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT
                .toBuilder()
                .id(STATEMENT_ID)
                .status(AccountStatementStatus.REVIEW)
                .statusDescription(Description.ERROR_PREVIOUS_STATEMENT)
                .build();

        final AccountStatement nextAccountStatement = AccountStatement.builder()
                .id(NEXT_STATEMENT_ID)
                .build();

        doReturn(accountStatement)
                .when(readAccountStatementUseCase)
                .execute(STATEMENT_ID);

        doReturn(nextAccountStatement)
                .when(readAccountStatementUseCase)
                .executeByPreviousStatement(accountStatement);

        doReturn(Collections.emptyList())
                .when(readAccountStatementFileUseCase)
                .execute(any(AccountStatementFileFilters.class), eq(null), eq(null));

        doNothing()
                .when(revalidateAccountStatementUseCase)
                .execute(accountStatement);

        // WHEN
        String result = retryAccountStatementUseCase.execute(STATEMENT_ID);

        // THEN
        assertThat(result)
                .isEqualTo("Statement with identifier - " + STATEMENT_ID + " is being retried");

        verify(readAccountStatementUseCase).execute(STATEMENT_ID);
        verify(readAccountStatementUseCase).executeByPreviousStatement(accountStatement);
        verify(readAccountStatementFileUseCase).execute(any(AccountStatementFileFilters.class), eq(null), eq(null));
        verify(revalidateAccountStatementUseCase).execute(accountStatement);
        verifyNoMoreInteractions(
                updateAccountStatementUseCase,
                discardAccountStatementUseCase,
                reprocessFilesUseCase
        );
    }
}
