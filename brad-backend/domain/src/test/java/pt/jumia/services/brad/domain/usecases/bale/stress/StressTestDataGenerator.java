package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
@Component
public class StressTestDataGenerator {

    private ErrorScenarioConfig errorConfig;
    private ResourceConstraints resourceConstraints;
    private ErrorMetrics errorMetrics = new ErrorMetrics();
    private ResourceMetrics resourceMetrics = new ResourceMetrics();

    public List<ViewEntity> createTestViewEntities(int count) {
        log.debug("Creating {} test view entities", count);
        return FakeViewEntity.getFakeViewEntity(count);
    }

    public List<ViewEntity> createLargeDatasetViews(int baleCount) {
        log.debug("Creating large dataset views with {} bales", baleCount);
        int viewCount = Math.max(1, baleCount / 10000);
        return FakeViewEntity.getFakeViewEntity(viewCount);
    }

    public List<ViewEntity> createStreamingTestViews(int baleCount) {
        log.debug("Creating streaming test views with {} bales", baleCount);
        int viewCount = Math.max(1, baleCount / 5000);
        return FakeViewEntity.getFakeViewEntity(viewCount);
    }

    public List<ViewEntity> createThreadSpecificViews(int threadId, int baleCount) {
        log.debug("Creating views for thread {} with {} bales", threadId, baleCount);
        int viewCount = Math.max(1, baleCount / 1000);
        return FakeViewEntity.getFakeViewEntity(viewCount);
    }

    public List<ViewEntity> createErrorProneViews(int baleCount) {
        log.debug("Creating error prone views with {} bales", baleCount);
        if (errorConfig != null) {
            simulateErrors();
        }
        int viewCount = Math.max(1, baleCount / 1000);
        return FakeViewEntity.getFakeViewEntity(viewCount);
    }

    public List<ViewEntity> createIterationViews(int iteration, int baleCount) {
        log.debug("Creating iteration {} views with {} bales", iteration, baleCount);
        int viewCount = Math.max(1, baleCount / 1000);
        return FakeViewEntity.getFakeViewEntity(viewCount);
    }

    public List<ViewEntity> createResourceIntensiveViews(int baleCount) {
        log.debug("Creating resource intensive views with {} bales", baleCount);
        if (resourceConstraints != null) {
            simulateResourceUsage();
        }
        int viewCount = Math.max(1, baleCount / 2000);
        return FakeViewEntity.getFakeViewEntity(viewCount);
    }

    public List<ViewEntity> createBatchTestViews(int baleCount) {
        log.debug("Creating batch test views with {} bales", baleCount);
        int viewCount = Math.max(1, baleCount / 1000);
        return FakeViewEntity.getFakeViewEntity(viewCount);
    }

    public void configureErrorScenarios(ErrorScenarioConfig config) {
        this.errorConfig = config;
        this.errorMetrics = new ErrorMetrics();
        log.info("Configured error scenarios: DB failure rate {}, Memory exhaustion rate {}", 
                config.getDatabaseFailureRate(), config.getMemoryExhaustionRate());
    }

    public void configureResourceConstraints(ResourceConstraints constraints) {
        this.resourceConstraints = constraints;
        this.resourceMetrics = new ResourceMetrics();
        log.info("Configured resource constraints: {} MB memory, {}% CPU", 
                constraints.getMaxMemoryMB(), constraints.getMaxCpuPercent());
    }

    public void resetErrorScenarios() {
        this.errorConfig = null;
        log.info("Reset error scenarios");
    }

    public void resetResourceConstraints() {
        this.resourceConstraints = null;
        log.info("Reset resource constraints");
    }

    public ErrorMetrics getErrorMetrics() {
        return errorMetrics;
    }

    public ResourceMetrics getResourceMetrics() {
        return resourceMetrics;
    }

    private void simulateErrors() {
        if (ThreadLocalRandom.current().nextDouble() < errorConfig.getDatabaseFailureRate()) {
            errorMetrics.incrementDatabaseFailures();
        }
        if (ThreadLocalRandom.current().nextDouble() < errorConfig.getMemoryExhaustionRate()) {
            errorMetrics.incrementMemoryExhaustionEvents();
        }
        if (ThreadLocalRandom.current().nextDouble() < errorConfig.getTimeoutRate()) {
            errorMetrics.incrementTimeoutEvents();
        }
    }

    private void simulateResourceUsage() {
        Runtime runtime = Runtime.getRuntime();
        long currentMemory = runtime.totalMemory() - runtime.freeMemory();
        
        resourceMetrics.updatePeakMemoryUsage(currentMemory / (1024 * 1024));
        resourceMetrics.updatePeakCpuUsage(getCurrentCpuUsage());
        resourceMetrics.updatePeakDatabaseConnections(getCurrentDatabaseConnections());
    }

    private double getCurrentCpuUsage() {
        return ThreadLocalRandom.current().nextDouble(50, 90);
    }

    private int getCurrentDatabaseConnections() {
        return ThreadLocalRandom.current().nextInt(5, 15);
    }
}
