package pt.jumia.services.brad.domain.usecases.accounts;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.dtos.AccountNetChangeResultDto;
import pt.jumia.services.brad.domain.entities.dtos.AccountTroubleshootingDto;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.fake.FakeTransaction;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountNetChangeFilters;
import pt.jumia.services.brad.domain.entities.filter.account.AccountSortFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.repository.brad.AccountRepository;
import pt.jumia.services.brad.domain.usecases.accountstatement.ReadAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.transactions.ReadTransactionUseCase;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ReadAccountsUseCaseTest {

    private static final List<Account> BANK_ACCOUNTS = FakeAccounts.getFakeAccounts(20, null);
    @Mock
    private AccountRepository accountRepository;

    @Mock
    private ReadAccountStatementUseCase readAccountStatementUseCase;

    @Mock
    private ReadTransactionUseCase readTransactionUseCase;

    @InjectMocks
    private ReadAccountsUseCase readAccountUseCase;

    @Test
    public void readAllAccounts_success() throws EntityErrorsException {

        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.DESC);

        AccountFilters accountFilters = AccountFilters.builder().build();

        List<Account> accountsToReturn = List.of(BANK_ACCOUNTS.get(0).toBuilder().id(1L).build());

        when(accountRepository.findAll(accountFilters, accountSortFilters, pageFilters)).thenReturn(accountsToReturn);

        List<Account> accounts = readAccountUseCase.execute(accountFilters, accountSortFilters, pageFilters);

        assertEquals(accountsToReturn, accounts);

        verify(accountRepository).findAll(accountFilters, accountSortFilters, pageFilters);
    }

    @Test
    public void readAccount_success() throws Exception {

        when(accountRepository.findById(1)).thenReturn(Optional.of(BANK_ACCOUNTS.get(0)));

        Account accounts = readAccountUseCase.execute(1);

        assertEquals(BANK_ACCOUNTS.get(0), accounts);
        verify(accountRepository).findById(1);
    }

    @Test
    public void readAllAccounts_empty() throws EntityErrorsException {
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.DESC);

        AccountFilters accountFilters = AccountFilters.builder().build();


        when(accountRepository.findAll(accountFilters, accountSortFilters, pageFilters)).thenReturn(Collections.emptyList());

        List<Account> accounts = readAccountUseCase.execute(accountFilters, accountSortFilters, pageFilters);

        assertTrue(accounts.isEmpty());
        verify(accountRepository).findAll(accountFilters, accountSortFilters, pageFilters);
    }


    @Test
    public void readAccount_empty() {

        when(accountRepository.findById(1)).thenReturn(Optional.empty());

        assertThrows(Exception.class, () ->{
            Account accounts = readAccountUseCase.execute(1);
            assertNull(accounts);
        });


        verify(accountRepository).findById(1);
    }

    @Test
    public void executeAdditionalInfo_success() throws Exception {
        Account account = BANK_ACCOUNTS.get(0).toBuilder().id(1L).build();
        List<AccountStatement> accountStatements = FakeAccountStatements.getFakeAccountStatements(20, account);
        List<Transaction> transactionList = FakeTransaction.getFakeCreditTransactions(20, accountStatements.get(0));

        when(accountRepository.findById(1)).thenReturn(Optional.of(account));
        when(readAccountStatementUseCase.executeLastImportedStatement(1)).thenReturn(Optional.ofNullable(accountStatements.get(0)));
        when(readTransactionUseCase.executeLastTransactionDate(any())).thenReturn(transactionList.get(transactionList.size()-1));
        when(accountRepository.findById(1)).thenReturn(Optional.of(account));

        Account readAccount = readAccountUseCase.executeAdditionalInfo(1);

        assertEquals(readAccount.getBalance(), accountStatements.get(0).getFinalAmount());
        assertEquals(readAccount.getLastTransactionDate(), transactionList.get(transactionList.size()-1).getTransactionDate());
        assertEquals(readAccount.getLastStatementDate(), accountStatements.get(0).getFinalDate());
        verify(accountRepository).findById(1);
    }

    @Test
    public void readAccountsInTroubleShooting_success() throws EntityErrorsException {

        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.DESC);

        AccountFilters accountFilters = AccountFilters.builder().build();

        final Account account = BANK_ACCOUNTS.get(0).toBuilder().id(1L).build();
        List<AccountTroubleshootingDto> accountsToReturn = List.of(new AccountTroubleshootingDto(account, false, true, true));

        when(accountRepository.findAllAccountsInTroubleShooting(accountFilters, accountSortFilters, pageFilters)).thenReturn(
            accountsToReturn);

        List<AccountTroubleshootingDto> accountTroubleshootingDtos = readAccountUseCase.executeAllTroubleshootingAccounts(accountFilters,
            accountSortFilters,
            pageFilters);

        assertEquals(accountsToReturn.get(0).getAccount(), accountTroubleshootingDtos.get(0).getAccount());

        verify(accountRepository).findAllAccountsInTroubleShooting(accountFilters, accountSortFilters, pageFilters);
    }

    @Test
    void executeAccountNetChange_whenNoAccountStatementsFound_thenReturnsZeroNetChange() throws EntityErrorsException {
        // GIVEN
        AccountNetChangeFilters netChangeFilters = AccountNetChangeFilters.builder()
            .fromBeginning(true)
            .endDate(LocalDate.now().minusDays(10))
            .build();
        List<Long> partitionKeys = List.of(1L, 2L);

        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(new ArrayList<>());

        // WHEN
        List<AccountNetChangeResultDto> result = readAccountUseCase.executeAccountNetChange(partitionKeys, netChangeFilters);

        // THEN
        assertEquals(2, result.size());
        assertEquals(BigDecimal.ZERO, result.get(0).netChange());
        assertEquals(BigDecimal.ZERO, result.get(1).netChange());
    }

    @Test
    void executeAccountNetChange_whenOneAccountStatementFound_thenReturnsCalculatedNetChange() throws EntityErrorsException {
        // GIVEN
        LocalDate endDate = LocalDate.now().minusDays(10);
        AccountNetChangeFilters netChangeFilters = AccountNetChangeFilters.builder()
            .fromBeginning(false)
            .endDate(endDate)
            .build();
        List<Long> partitionKeys = Stream.of(1L, 2L).toList();

        AccountStatement singleStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder().finalAmount(new BigDecimal("100"))
            .build();

        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(Stream.of(singleStatement).collect(Collectors.toList()));

        when(readTransactionUseCase.executeNetChange(any())).thenReturn(new BigDecimal("50"));

        // WHEN
        List<AccountNetChangeResultDto> result = readAccountUseCase.executeAccountNetChange(partitionKeys, netChangeFilters);

        // THEN
        assertEquals(2, result.size());
        assertEquals(new BigDecimal("50"), result.get(0).netChange());
    }

    @Test
    void executeAccountNetChange_whenMultipleAccountStatementsFound_thenReturnsCalculatedNetChange() throws EntityErrorsException {
        // GIVEN
        LocalDate endDate = LocalDate.now().minusDays(10);
        AccountNetChangeFilters netChangeFilters = AccountNetChangeFilters.builder()
            .fromBeginning(false)
            .endDate(endDate)
            .build();
        List<Long> partitionKeys = List.of(1L, 2L);

        AccountStatement firstStmt = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder().finalAmount(new BigDecimal("100"))
            .build();

        AccountStatement lastStmt = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder().finalAmount(new BigDecimal("200"))
            .build();

        List<AccountStatement> statements = Stream.of(firstStmt, lastStmt).collect(Collectors.toList());

        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(statements);

        when(readTransactionUseCase.executeNetChange(any())).thenReturn(new BigDecimal("30")).thenReturn(new BigDecimal("20"));

        // WHEN
        List<AccountNetChangeResultDto> result = readAccountUseCase.executeAccountNetChange(partitionKeys, netChangeFilters);

        // THEN
        assertEquals(2, result.size());

        BigDecimal expectedNetChange = lastStmt.getInitialAmount().subtract(firstStmt.getFinalAmount())
            .add(new BigDecimal("30"))
            .add(new BigDecimal("20"));

        assertEquals(expectedNetChange, result.get(0).netChange());
    }

    @Test
    void executeAccountNetChange_StatementHaveTheSameInitialDate_thenOrdersByCreatedAt() throws EntityErrorsException {
        // GIVEN
        LocalDate endDate = LocalDate.now().minusDays(10);
        AccountNetChangeFilters netChangeFilters = AccountNetChangeFilters.builder()
            .fromBeginning(false)
            .endDate(endDate)
            .build();
        List<Long> partitionKeys = List.of(1L, 2L);

        AccountStatement firstStmt = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder().finalAmount(new BigDecimal("100"))
            .build();

        AccountStatement secondStmt = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
            .createdAt(LocalDateTime.now().minusDays(10))
            .finalAmount(new BigDecimal("1040"))
            .build();

        AccountStatement lastStmt = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
            .createdAt(LocalDateTime.now().minusDays(1))
            .finalAmount(new BigDecimal("200"))
            .build();

        List<AccountStatement> statements = Stream.of(firstStmt, lastStmt, secondStmt).collect(Collectors.toList());

        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(statements);

        when(readTransactionUseCase.executeNetChange(any())).thenReturn(new BigDecimal("30")).thenReturn(new BigDecimal("20"));

        // WHEN
        List<AccountNetChangeResultDto> result = readAccountUseCase.executeAccountNetChange(partitionKeys, netChangeFilters);

        // THEN
        assertEquals(2, result.size());

        BigDecimal expectedNetChange = lastStmt.getInitialAmount().subtract(firstStmt.getFinalAmount())
            .add(new BigDecimal("30"))
            .add(new BigDecimal("20"));

        assertEquals(expectedNetChange, result.get(0).netChange());
    }

    @Test
    void executeAccountNetChange_whenSinceBeginning_thenReturnsCalculatedNetChange() throws EntityErrorsException {
        // GIVEN
        LocalDate endDate = LocalDate.now().minusDays(10);
        AccountNetChangeFilters netChangeFilters = AccountNetChangeFilters.builder()
            .fromBeginning(true)
            .endDate(endDate)
            .build();
        List<Long> partitionKeys = List.of(1L);

        AccountStatement lastStmt = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder().initialAmount(new BigDecimal("100"))
            .finalAmount(new BigDecimal("200"))
            .build();

        when(readAccountStatementUseCase.execute(any(), any(), any())).thenReturn(List.of(lastStmt).stream().collect(Collectors.toList()));

        when(readTransactionUseCase.executeNetChange(any())).thenReturn(new BigDecimal("30"));

        // WHEN
        List<AccountNetChangeResultDto> result = readAccountUseCase.executeAccountNetChange(partitionKeys, netChangeFilters);

        // THEN
        BigDecimal expectedNetChange = lastStmt.getInitialAmount().add(new BigDecimal("30"));

        assertEquals(expectedNetChange, result.get(0).netChange());
    }

    @Test
    public void execute_withNegativeBalanceFilterTrue_returnsOnlyNegativeBalanceAccounts() throws EntityErrorsException {
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.ASC);
        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .build();

        Account negativeBalanceAccount = BANK_ACCOUNTS.get(0).toBuilder().id(1L).build();
        List<Account> accountsToReturn = List.of(negativeBalanceAccount);

        AccountStatement negativeStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
                .finalAmount(new BigDecimal("100.00"))
                .finalDirection(Direction.DEBIT)
                .status(AccountStatementStatus.IMPORTED)
                .build();

        when(accountRepository.findAll(accountFilters, accountSortFilters, pageFilters)).thenReturn(accountsToReturn);
        when(readAccountStatementUseCase.executeLastImportedStatement(1L)).thenReturn(Optional.of(negativeStatement));
        when(readAccountStatementUseCase.executeLastStatement(1L)).thenReturn(Optional.of(negativeStatement));

        List<Account> accounts = readAccountUseCase.execute(accountFilters, accountSortFilters, pageFilters);

        assertEquals(1, accounts.size());
        assertEquals(new BigDecimal("100.00").negate(), accounts.get(0).getBalance());
        assertTrue(accounts.get(0).getHasError() == null || !accounts.get(0).getHasError());
        verify(accountRepository).findAll(accountFilters, accountSortFilters, pageFilters);
    }

    @Test
    public void execute_withNegativeBalanceFilterFalse_returnsAllAccounts() throws EntityErrorsException {
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.ASC);
        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(false)
                .build();

        Account negativeBalanceAccount = BANK_ACCOUNTS.get(0).toBuilder().id(1L).build();
        Account positiveBalanceAccount = BANK_ACCOUNTS.get(1).toBuilder().id(2L).build();
        List<Account> accountsToReturn = List.of(negativeBalanceAccount, positiveBalanceAccount);

        AccountStatement negativeStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
                .finalAmount(new BigDecimal("100.00"))
                .finalDirection(Direction.DEBIT)
                .build();

        AccountStatement positiveStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
                .finalAmount(new BigDecimal("500.00"))
                .finalDirection(Direction.CREDIT)
                .build();

        when(accountRepository.findAll(accountFilters, accountSortFilters, pageFilters)).thenReturn(accountsToReturn);
        when(readAccountStatementUseCase.executeLastImportedStatement(1L)).thenReturn(Optional.of(negativeStatement));
        when(readAccountStatementUseCase.executeLastStatement(1L)).thenReturn(Optional.of(negativeStatement));
        when(readAccountStatementUseCase.executeLastImportedStatement(2L)).thenReturn(Optional.of(positiveStatement));
        when(readAccountStatementUseCase.executeLastStatement(2L)).thenReturn(Optional.of(positiveStatement));

        List<Account> accounts = readAccountUseCase.execute(accountFilters, accountSortFilters, pageFilters);

        assertEquals(2, accounts.size());
        assertEquals(new BigDecimal("100.00").negate(), accounts.get(0).getBalance());
        assertEquals(new BigDecimal("500.00"), accounts.get(1).getBalance());
        verify(accountRepository).findAll(accountFilters, accountSortFilters, pageFilters);
    }

    @Test
    public void execute_withZeroBalanceAccount_handlesCorrectly() throws EntityErrorsException {
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.ASC);
        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .build();

        Account zeroBalanceAccount = BANK_ACCOUNTS.get(0).toBuilder().id(1L).build();
        List<Account> accountsToReturn = List.of(zeroBalanceAccount);

        AccountStatement zeroStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
                .finalAmount(BigDecimal.ZERO)
                .finalDirection(Direction.CREDIT)
                .build();

        when(accountRepository.findAll(accountFilters, accountSortFilters, pageFilters)).thenReturn(accountsToReturn);
        when(readAccountStatementUseCase.executeLastImportedStatement(1L)).thenReturn(Optional.of(zeroStatement));
        when(readAccountStatementUseCase.executeLastStatement(1L)).thenReturn(Optional.of(zeroStatement));

        List<Account> accounts = readAccountUseCase.execute(accountFilters, accountSortFilters, pageFilters);

        assertEquals(1, accounts.size());
        assertEquals(BigDecimal.ZERO, accounts.get(0).getBalance());
        verify(accountRepository).findAll(accountFilters, accountSortFilters, pageFilters);
    }

    @Test
    public void execute_withNegativeBalanceAndOtherFilters_passesAllFiltersToRepository() throws EntityErrorsException {
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(10);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ACCOUNT_NAME, OrderDirection.DESC);
        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .companyID("COMPANY123")
                .countryCodes(List.of(1L))
                .build();

        Account negativeBalanceAccount = BANK_ACCOUNTS.get(0).toBuilder().id(1L).build();
        List<Account> accountsToReturn = List.of(negativeBalanceAccount);

        AccountStatement negativeStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
                .finalAmount(new BigDecimal("250.75"))
                .finalDirection(Direction.DEBIT)
                .build();

        when(accountRepository.findAll(accountFilters, accountSortFilters, pageFilters)).thenReturn(accountsToReturn);
        when(readAccountStatementUseCase.executeLastImportedStatement(1L)).thenReturn(Optional.of(negativeStatement));
        when(readAccountStatementUseCase.executeLastStatement(1L)).thenReturn(Optional.of(negativeStatement));

        List<Account> accounts = readAccountUseCase.execute(accountFilters, accountSortFilters, pageFilters);

        assertEquals(1, accounts.size());
        assertEquals(new BigDecimal("250.75").negate(), accounts.get(0).getBalance());
        verify(accountRepository).findAll(accountFilters, accountSortFilters, pageFilters);
    }

    @Test
    public void execute_withMultipleNegativeBalanceAccounts_returnsAllWithCorrectBalances() throws EntityErrorsException {
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.ASC);
        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .build();

        Account firstNegativeAccount = BANK_ACCOUNTS.get(0).toBuilder().id(1L).build();
        Account secondNegativeAccount = BANK_ACCOUNTS.get(1).toBuilder().id(2L).build();
        List<Account> accountsToReturn = List.of(firstNegativeAccount, secondNegativeAccount);

        AccountStatement firstNegativeStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
                .finalAmount(new BigDecimal("100.50"))
                .finalDirection(Direction.DEBIT)
                .build();

        AccountStatement secondNegativeStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
                .finalAmount(new BigDecimal("250.75"))
                .finalDirection(Direction.DEBIT)
                .build();

        when(accountRepository.findAll(accountFilters, accountSortFilters, pageFilters)).thenReturn(accountsToReturn);
        when(readAccountStatementUseCase.executeLastImportedStatement(1L)).thenReturn(Optional.of(firstNegativeStatement));
        when(readAccountStatementUseCase.executeLastStatement(1L)).thenReturn(Optional.of(firstNegativeStatement));
        when(readAccountStatementUseCase.executeLastImportedStatement(2L)).thenReturn(Optional.of(secondNegativeStatement));
        when(readAccountStatementUseCase.executeLastStatement(2L)).thenReturn(Optional.of(secondNegativeStatement));

        List<Account> accounts = readAccountUseCase.execute(accountFilters, accountSortFilters, pageFilters);

        assertEquals(2, accounts.size());
        assertEquals(new BigDecimal("100.50").negate(), accounts.get(0).getBalance());
        assertEquals(new BigDecimal("250.75").negate(), accounts.get(1).getBalance());
        verify(accountRepository).findAll(accountFilters, accountSortFilters, pageFilters);
    }

    @Test
    public void execute_withNoAccountStatements_handlesGracefully() throws EntityErrorsException {
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.ASC);
        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .build();

        Account accountWithoutStatements = BANK_ACCOUNTS.get(0).toBuilder().id(1L).build();
        List<Account> accountsToReturn = List.of(accountWithoutStatements);

        when(accountRepository.findAll(accountFilters, accountSortFilters, pageFilters)).thenReturn(accountsToReturn);
        when(readAccountStatementUseCase.executeLastImportedStatement(1L)).thenReturn(Optional.empty());
        when(readAccountStatementUseCase.executeLastStatement(1L)).thenReturn(Optional.empty());

        List<Account> accounts = readAccountUseCase.execute(accountFilters, accountSortFilters, pageFilters);

        assertEquals(1, accounts.size());
        assertEquals(accountWithoutStatements.getBalance(), accounts.get(0).getBalance());
        assertNull(accounts.get(0).getBalanceUSD());
        assertNull(accounts.get(0).getLastStatementDate());
        assertEquals(accountWithoutStatements.getHasError(), accounts.get(0).getHasError());
        verify(accountRepository).findAll(accountFilters, accountSortFilters, pageFilters);
    }

    @Test
    public void execute_withAccountStatementErrors_setsHasErrorCorrectly() throws EntityErrorsException {
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.ASC);
        AccountFilters accountFilters = AccountFilters.builder()
                .isNegativeBalance(true)
                .build();

        Account accountWithError = BANK_ACCOUNTS.get(0).toBuilder().id(1L).build();
        List<Account> accountsToReturn = List.of(accountWithError);

        AccountStatement importedStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
                .finalAmount(new BigDecimal("100.00"))
                .finalDirection(Direction.DEBIT)
                .status(AccountStatementStatus.IMPORTED)
                .build();

        AccountStatement errorStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
                .finalAmount(new BigDecimal("100.00"))
                .finalDirection(Direction.DEBIT)
                .status(AccountStatementStatus.REVIEW)
                .build();

        when(accountRepository.findAll(accountFilters, accountSortFilters, pageFilters)).thenReturn(accountsToReturn);
        when(readAccountStatementUseCase.executeLastImportedStatement(1L)).thenReturn(Optional.of(importedStatement));
        when(readAccountStatementUseCase.executeLastStatement(1L)).thenReturn(Optional.of(errorStatement));

        List<Account> accounts = readAccountUseCase.execute(accountFilters, accountSortFilters, pageFilters);

        assertEquals(1, accounts.size());
        assertTrue(accounts.get(0).getHasError());
        assertEquals(new BigDecimal("100.00").negate(), accounts.get(0).getBalance());
        verify(accountRepository).findAll(accountFilters, accountSortFilters, pageFilters);
    }

    @Test
    public void execute_withNullFilters_passesNullToRepository() throws EntityErrorsException {
        PageFilters pageFilters = new PageFilters();
        AccountSortFilters accountSortFilters = new AccountSortFilters(Account.SortingFields.ID, OrderDirection.ASC);

        Account account = BANK_ACCOUNTS.get(0).toBuilder().id(1L).build();
        List<Account> accountsToReturn = List.of(account);

        when(accountRepository.findAll(null, accountSortFilters, pageFilters)).thenReturn(accountsToReturn);
        when(readAccountStatementUseCase.executeLastImportedStatement(1L)).thenReturn(Optional.empty());
        when(readAccountStatementUseCase.executeLastStatement(1L)).thenReturn(Optional.empty());

        List<Account> accounts = readAccountUseCase.execute(null, accountSortFilters, pageFilters);

        assertEquals(1, accounts.size());
        verify(accountRepository).findAll(null, accountSortFilters, pageFilters);
    }
}

