package pt.jumia.services.brad.domain.usecases.accountstatement;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementRepository;
import pt.jumia.services.brad.domain.usecases.accountstatement.PostStatementWriteUseCase.Operation;
import pt.jumia.services.brad.domain.usecases.transactions.ReadTransactionUseCase;

@ExtendWith(MockitoExtension.class)
class ValidateAccountStatementUseCaseTest {

    @Mock
    private AccountStatementRepository accountStatementRepository;

    @Mock
    private ReadTransactionUseCase readTransactionUseCase;

    @Mock
    private PostStatementWriteUseCase postStatementWriteUseCase;

    @InjectMocks
    private ValidateAccountStatementUseCase subject;

    @Test
    void isOpeningDateValid_differenceIsOneDay_false() {
        final AccountStatement accountStatement = AccountStatement
                .builder()
                .initialDate(LocalDate.of(2024, 1,2))
                .previousStatement(
                        AccountStatement.builder()
                                .finalDate(LocalDate.of(2024, 1,1))
                                .build()
                )
                .build();

        assertTrue(subject.isOpeningDateValid(accountStatement));
    }

    @Test
    void isOpeningDateValid_differenceIsMoreThanOneDay_false() {
        final AccountStatement accountStatement = AccountStatement
            .builder()
            .initialDate(LocalDate.of(2024, 1, 3))
            .previousStatement(
                AccountStatement.builder()
                    .finalDate(LocalDate.of(2024, 1, 1))
                    .build()
            )
            .build();

        // WHEN
        boolean result = subject.isOpeningDateValid(accountStatement);

        // THEN
        assertFalse(result);
    }

    @Test
    void isOpeningDateValid_whenNoPreviousStatement_thenReturnsTrue() {
        // GIVEN
        final AccountStatement accountStatement = AccountStatement
            .builder()
            .initialDate(LocalDate.of(2024, 1, 3))
            .previousStatement(null)
            .build();

        // WHEN
        boolean result = subject.isOpeningDateValid(accountStatement);

        // THEN
        assertTrue(result);
    }

    @Test
    void isClosingBalanceValid_whenBalancesMatch_thenReturnsTrue() {
        // GIVEN
        Currency usd = FakeCurrencies.USD;
        final AccountStatement accountStatement = AccountStatement
            .builder()
            .initialAmount(BigDecimal.valueOf(100))
            .initialDirection(Direction.CREDIT)
            .finalAmount(BigDecimal.valueOf(150))
            .finalDirection(Direction.CREDIT)
            .currency(usd)
            .build();

        List<Transaction> transactions = new ArrayList<>();
        transactions.add(Transaction.builder()
            .amount(BigDecimal.valueOf(50))
            .direction(Direction.CREDIT)
            .currency(usd)
            .build());

        // WHEN
        boolean result = subject.isClosingBalanceValid(accountStatement, transactions);

        // THEN
        assertTrue(result);
    }

    @Test
    void isClosingBalanceValid_whenBalancesDontMatch_thenReturnsFalse() {
        // GIVEN
        Currency usd = FakeCurrencies.USD;
        final AccountStatement accountStatement = AccountStatement
            .builder()
            .initialAmount(BigDecimal.valueOf(100))
            .initialDirection(Direction.CREDIT)
            .finalAmount(BigDecimal.valueOf(200))
            .finalDirection(Direction.CREDIT)
            .currency(usd)
            .build();

        List<Transaction> transactions = new ArrayList<>();
        transactions.add(Transaction.builder()
            .amount(BigDecimal.valueOf(50))
            .direction(Direction.CREDIT)
            .currency(usd)
            .build());

        // WHEN
        boolean result = subject.isClosingBalanceValid(accountStatement, transactions);

        // THEN
        assertFalse(result);
    }

    @Test
    void isClosingBalanceValid_whenTransactionCurrencyDifferent_thenReturnsFalse() {
        // GIVEN
        Currency usd = FakeCurrencies.USD;
        Currency eur = FakeCurrencies.EUR;
        final AccountStatement accountStatement = AccountStatement
            .builder()
            .initialAmount(BigDecimal.valueOf(100))
            .initialDirection(Direction.CREDIT)
            .finalAmount(BigDecimal.valueOf(150))
            .finalDirection(Direction.CREDIT)
            .currency(usd)
            .build();

        List<Transaction> transactions = new ArrayList<>();
        transactions.add(Transaction.builder()
            .amount(BigDecimal.valueOf(50))
            .direction(Direction.CREDIT)
            .currency(eur)
            .build());

        // WHEN
        boolean result = subject.isClosingBalanceValid(accountStatement, transactions);

        // THEN
        assertFalse(result);
    }

    @Test
    void execute_whenStatementHasNoErrors_thenMarkAsImported() throws NotFoundException, EntityErrorsException, DatabaseErrorsException {
        // GIVEN
        Currency usd = FakeCurrencies.USD;
        AccountStatement accountStatement = AccountStatement
            .builder()
            .id(1L)
            .initialDate(LocalDate.of(2024, 1, 1))
            .finalDate(LocalDate.of(2024, 1, 31))
            .initialAmount(BigDecimal.valueOf(100))
            .finalAmount(BigDecimal.valueOf(150))
            .initialDirection(Direction.CREDIT)
            .finalDirection(Direction.CREDIT)
            .status(AccountStatementStatus.OPEN)
            .currency(usd)
            .account(Account.builder().id(1L).build())
            .build();

        List<Transaction> transactions = new ArrayList<>();
        transactions.add(Transaction.builder()
            .amount(BigDecimal.valueOf(50))
            .direction(Direction.CREDIT)
            .transactionDate(LocalDate.of(2024, 1, 15))
            .currency(usd)
            .build());

        when(readTransactionUseCase.execute(any(TransactionFilters.class), any(), any())).thenReturn(transactions);

        // WHEN
        subject.execute(accountStatement);

        // THEN
        verify(accountStatementRepository).upsert(argThat(statement ->
            statement.getStatus() == AccountStatementStatus.IMPORTED &&
                statement.getStatusDescription() == AccountStatementStatus.Description.IMPORTED
        ));
        verify(postStatementWriteUseCase).execute(any(AccountStatement.class), eq(Operation.IMPORTED));
    }

    @Test
    void execute_whenPreviousStatementNotImported_thenMoveToReview()
        throws NotFoundException, EntityErrorsException, DatabaseErrorsException {
        // GIVEN
        Currency usd = FakeCurrencies.USD;
        AccountStatement previousStatement = AccountStatement.builder()
            .id(1L)
            .finalDate(LocalDate.of(2023, 12, 31))
            .status(AccountStatementStatus.OPEN)
            .finalAmount(BigDecimal.valueOf(100))
            .finalDirection(Direction.CREDIT)
            .build();

        AccountStatement accountStatement = AccountStatement
            .builder()
            .id(2L)
            .initialDate(LocalDate.of(2024, 1, 1))
            .finalDate(LocalDate.of(2024, 1, 31))
            .initialAmount(BigDecimal.valueOf(100))
            .finalAmount(BigDecimal.valueOf(150))
            .initialDirection(Direction.CREDIT)
            .finalDirection(Direction.CREDIT)
            .status(AccountStatementStatus.OPEN)
            .currency(usd)
            .account(Account.builder().id(1L).build())
            .previousStatement(previousStatement)
            .build();

        List<Transaction> transactions = new ArrayList<>();
        transactions.add(Transaction.builder()
            .amount(BigDecimal.valueOf(50))
            .direction(Direction.CREDIT)
            .transactionDate(LocalDate.of(2024, 1, 15))
            .currency(usd)
            .build());

        when(readTransactionUseCase.execute(any(TransactionFilters.class), any(), any())).thenReturn(transactions);

        // WHEN
        subject.execute(accountStatement);

        // THEN
        verify(accountStatementRepository).upsert(argThat(statement ->
            statement.getStatus() == AccountStatementStatus.REVIEW &&
                statement.getStatusDescription() == AccountStatementStatus.Description.ERROR_PREVIOUS_STATEMENT
        ));
    }

    @Test
    void execute_whenOpeningBalanceInvalid_thenMoveToReview() throws NotFoundException, EntityErrorsException, DatabaseErrorsException {
        // GIVEN
        Currency usd = FakeCurrencies.USD;
        AccountStatement previousStatement = AccountStatement.builder()
            .id(1L)
            .finalDate(LocalDate.of(2023, 12, 31))
            .status(AccountStatementStatus.IMPORTED)
            .finalAmount(BigDecimal.valueOf(200))  // Different from opening balance of current statement
            .finalDirection(Direction.CREDIT)
            .build();

        AccountStatement accountStatement = AccountStatement
            .builder()
            .id(2L)
            .initialDate(LocalDate.of(2024, 1, 1))
            .finalDate(LocalDate.of(2024, 1, 31))
            .initialAmount(BigDecimal.valueOf(100))  // Doesn't match previous statement's final amount
            .finalAmount(BigDecimal.valueOf(150))
            .initialDirection(Direction.CREDIT)
            .finalDirection(Direction.CREDIT)
            .status(AccountStatementStatus.OPEN)
            .currency(usd)
            .account(Account.builder().id(1L).build())
            .previousStatement(previousStatement)
            .build();

        List<Transaction> transactions = new ArrayList<>();
        transactions.add(Transaction.builder()
            .amount(BigDecimal.valueOf(50))
            .direction(Direction.CREDIT)
            .transactionDate(LocalDate.of(2024, 1, 15))
            .currency(usd)
            .build());

        when(readTransactionUseCase.execute(any(TransactionFilters.class), any(), any())).thenReturn(transactions);

        // WHEN
        subject.execute(accountStatement);

        // THEN
        verify(accountStatementRepository).upsert(argThat(statement ->
            statement.getStatus() == AccountStatementStatus.REVIEW &&
                statement.getStatusDescription() == AccountStatementStatus.Description.ERROR_OPENING_BALANCE
        ));
    }
}
