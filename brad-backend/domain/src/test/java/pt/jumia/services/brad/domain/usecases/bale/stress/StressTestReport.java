package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Builder;
import lombok.Value;

import java.time.Duration;

@Value
@Builder
public class StressTestReport {
    
    @Builder.Default
    boolean success = false;
    
    String errorMessage;
    Duration totalExecutionTime;
    
    PerformanceTestResult performanceTestResult;
    MemoryTestResult memoryTestResult;
    ConcurrencyTestResult concurrencyTestResult;
    ReliabilityTestResult reliabilityTestResult;
    ScalabilityTestResult scalabilityTestResult;
    
    public String generateSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("=== Bale Sync Stress Test Report ===\n");
        summary.append("Overall Status: ").append(success ? "PASSED" : "FAILED").append("\n");
        
        if (totalExecutionTime != null) {
            summary.append("Total Execution Time: ").append(totalExecutionTime.getSeconds()).append(" seconds\n");
        }
        
        if (errorMessage != null) {
            summary.append("Error: ").append(errorMessage).append("\n");
        }
        
        summary.append("\n=== Individual Test Results ===\n");
        
        if (performanceTestResult != null) {
            summary.append("Performance Test: ").append(performanceTestResult.isSuccess() ? "PASSED" : "FAILED").append("\n");
            summary.append("  - Throughput: ").append(performanceTestResult.getThroughput()).append(" ops/sec\n");
            summary.append("  - CPU Utilization: ").append(performanceTestResult.getCpuUtilization()).append("%\n");
        }
        
        if (memoryTestResult != null) {
            summary.append("Memory Test: ").append(memoryTestResult.isSuccess() ? "PASSED" : "FAILED").append("\n");
            summary.append("  - Peak Memory: ").append(memoryTestResult.getPeakMemoryUsage() / (1024 * 1024)).append(" MB\n");
            summary.append("  - Memory Leakage: ").append(memoryTestResult.isMemoryLeakage() ? "DETECTED" : "NONE").append("\n");
        }
        
        if (concurrencyTestResult != null) {
            summary.append("Concurrency Test: ").append(concurrencyTestResult.isSuccess() ? "PASSED" : "FAILED").append("\n");
            summary.append("  - Successful Threads: ").append(concurrencyTestResult.getSuccessfulThreads())
                    .append("/").append(concurrencyTestResult.getThreadCount()).append("\n");
        }
        
        if (reliabilityTestResult != null) {
            summary.append("Reliability Test: ").append(reliabilityTestResult.isSuccess() ? "PASSED" : "FAILED").append("\n");
            summary.append("  - Success Rate: ").append(String.format("%.1f%%", 
                    (double) reliabilityTestResult.getSuccessfulOperations() / reliabilityTestResult.getTotalOperations() * 100)).append("\n");
        }
        
        if (scalabilityTestResult != null) {
            summary.append("Scalability Test: ").append(scalabilityTestResult.isSuccess() ? "PASSED" : "FAILED").append("\n");
            summary.append("  - Scalability Factor: ").append(scalabilityTestResult.getScalabilityFactor()).append("\n");
        }
        
        return summary.toString();
    }
}
