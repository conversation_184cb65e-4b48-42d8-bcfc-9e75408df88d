package pt.jumia.services.brad.domain.usecases.accountstatement;

import org.assertj.core.api.ThrowableAssert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementRepository;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DiscardAccountStatementUseCaseTest {

    @Mock
    private AccountStatementRepository accountStatementRepository;

    @Mock
    private RetryAccountStatementUseCase retryAccountStatementUseCase;

    @Mock
    private DeleteStatementsAtomicallyHelper deleteStatementsAtomicallyHelper;

    @Mock
    private PostStatementWriteUseCase postStatementWriteUseCase;

    @InjectMocks
    private DiscardAccountStatementUseCase discardAccountStatementUseCase;

    @Test
    void execute_whenNextStatementIsNull_discardSuccess() throws NotFoundException, EntityErrorsException {
        //GIVEN
        Account account = FakeAccounts.getFakeAccounts(1, null).get(0);
        AccountStatement accountStatement = FakeAccountStatements.getFakeAccountStatements(1, account).get(0)
                .toBuilder()
                .status(AccountStatementStatus.REVIEW)
                .build();
        when(accountStatementRepository.findById(any())).thenReturn(java.util.Optional.of(accountStatement));
        doNothing().when(deleteStatementsAtomicallyHelper).deleteAtomically(any());

        //WHEN
        discardAccountStatementUseCase.execute(1L);

        verify(deleteStatementsAtomicallyHelper, times(1)).deleteAtomically(any());


    }

    @Test
    void execute_whenNextStatementExists_discardSuccess() throws NotFoundException, EntityErrorsException {
        //GIVEN
        Account account = FakeAccounts.getFakeAccounts(1, null).get(0).toBuilder().id(1L).build();
        AccountStatement accountStatement = FakeAccountStatements.getFakeAccountStatements(1, account).get(0).toBuilder()
                .status(AccountStatementStatus.REVIEW)
                .build();
        when(accountStatementRepository.findById(any())).thenReturn(java.util.Optional.of(accountStatement));
        doNothing().when(deleteStatementsAtomicallyHelper).deleteAtomically(any());
        doNothing().when(postStatementWriteUseCase).execute(any(), any());

        //WHEN
        discardAccountStatementUseCase.execute(1L);

        //THEN
        verify(deleteStatementsAtomicallyHelper, times(1)).deleteAtomically(any());
        verify(postStatementWriteUseCase, atLeastOnce()).execute(any(), any());

    }
    @Test
    void execute_whenStatementNotFound_returnNotFound() {
        //GIVEN
        when(accountStatementRepository.findById(any())).thenReturn(Optional.empty());

        //WHEN
        ThrowableAssert.ThrowingCallable throwingCallable = () -> discardAccountStatementUseCase.execute(1L);

        //THEN
        assertThatThrownBy(throwingCallable).isInstanceOf(NotFoundException.class);

    }


    @Test
    void execute_whenStatementIsNotInReview_discardError() {
        //GIVEN
        Account account = FakeAccounts.getFakeAccounts(1, null).get(0);
        AccountStatement accountStatement = FakeAccountStatements.getFakeAccountStatements(1, account).get(0).toBuilder()
                .status(AccountStatementStatus.IMPORTED)
                .build();
        when(accountStatementRepository.findById(any())).thenReturn(java.util.Optional.of(accountStatement));

        //WHEN
        ThrowableAssert.ThrowingCallable throwingCallable = () -> discardAccountStatementUseCase.execute(1L);

        //THEN
        assertThatThrownBy(throwingCallable).isInstanceOf(IllegalArgumentException.class).as("Account statement is not in review");
    }


    @Test
    void execute_discard_lastImportedStatement_Success() throws NotFoundException, EntityErrorsException {
        //GIVEN
        Account account = FakeAccounts.FAKE_ACCOUNT_A;
        AccountStatement accountStatement = FakeAccountStatements.getFakeAccountStatements(1, account).get(0)
                .toBuilder()
                .status(AccountStatementStatus.IMPORTED)
                .build();

        when(accountStatementRepository.findById(any())).thenReturn(java.util.Optional.of(accountStatement));
        when(accountStatementRepository.findLastImportedStatementInList(any())).thenReturn(Optional.of(accountStatement));
        doNothing().when(deleteStatementsAtomicallyHelper).deleteAtomically(any());
        doNothing().when(postStatementWriteUseCase).execute(any(), any());


        //WHEN
        discardAccountStatementUseCase.executeDiscardLastImportedStatement(1L);

        verify(deleteStatementsAtomicallyHelper, times(1)).deleteAtomically(any());
        verify(postStatementWriteUseCase, atLeastOnce()).execute(any(), any());

    }

    @Test
    void execute_whenStatementIsNotLastImported_discardError() {
        //GIVEN
        Account account = FakeAccounts.getFakeAccounts(1, Account.Type.BANK_ACCOUNT).get(0);
        AccountStatement accountStatement = FakeAccountStatements.getFakeAccountStatements(2, account).get(1).toBuilder()
                .status(AccountStatementStatus.IMPORTED)
                .build();
        AccountStatement lastImportedStatement = FakeAccountStatements.getFakeAccountStatements(1, account).get(0).toBuilder()
                .status(AccountStatementStatus.IMPORTED)
                .build();

        when(accountStatementRepository.findById(any())).thenReturn(java.util.Optional.of(accountStatement));
        when(accountStatementRepository.findLastImportedStatementInList(any())).thenReturn(java.util.Optional.of(lastImportedStatement));

        //WHEN
        ThrowableAssert.ThrowingCallable throwingCallable = () -> discardAccountStatementUseCase.executeDiscardLastImportedStatement(3L);

        //THEN
        assertThatThrownBy(throwingCallable).isInstanceOf(IllegalArgumentException.class).as("Account statement is not the last imported");
    }


}
