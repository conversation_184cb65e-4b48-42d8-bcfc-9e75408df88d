package pt.jumia.services.brad.domain.usecases.accountstatement;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementRepository;

@ExtendWith(MockitoExtension.class)
class UpdateAccountStatementUseCaseTest {

    @Mock
    private AccountStatementRepository accountStatementRepository;

    private UpdateAccountStatementUseCase updateAccountStatementUseCase;

    @BeforeEach
    void setUp() {

        updateAccountStatementUseCase = new UpdateAccountStatementUseCase(accountStatementRepository);
    }

    @Test
    void execute_whenRepositoryUpdatesSuccessfully_thenUpdateCompletes() {
        // GIVEN
        AccountStatement accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT;
        when(accountStatementRepository.upsert(accountStatement)).thenReturn(accountStatement);

        // WHEN
        updateAccountStatementUseCase.execute(accountStatement);

        // THEN
        verify(accountStatementRepository, times(1)).upsert(accountStatement);
    }

    @Test
    void execute_whenRepositoryThrowsException_thenHandlesError() {
        // GIVEN
        AccountStatement accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT;
        doThrow(new RuntimeException("Database error")).when(accountStatementRepository).upsert(any(AccountStatement.class));

        // WHEN - THEN
        assertThrows(RuntimeException.class, () -> updateAccountStatementUseCase.execute(accountStatement));
        verify(accountStatementRepository, times(1)).upsert(accountStatement);

    }

}

