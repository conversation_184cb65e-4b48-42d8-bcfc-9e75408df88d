package pt.jumia.services.brad.domain.usecases.bale.streaming;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class MemoryMonitorTest {

    private MemoryMonitor memoryMonitor;

    @BeforeEach
    void setUp() {
        memoryMonitor = new MemoryMonitor();
    }

    @Test
    void isMemoryThresholdExceeded_LowMemoryUsage_ReturnsFalse() {
        // Act - Use a very high threshold (99%) that should never be exceeded in tests
        boolean exceeded = memoryMonitor.isMemoryThresholdExceeded(0.99);

        // Assert
        assertFalse(exceeded, "Memory usage should not exceed 99% threshold in normal test conditions");
    }

    @Test
    void isMemoryThresholdExceeded_VeryLowThreshold_ReturnsTrue() {
        // Act - Use a very low threshold (1%) that should always be exceeded
        boolean exceeded = memoryMonitor.isMemoryThresholdExceeded(0.01);

        // Assert
        assertTrue(exceeded, "Memory usage should exceed 1% threshold");
    }

    @Test
    void getCurrentMemoryInfo_ReturnsValidInfo() {
        // Act
        MemoryMonitor.MemoryInfo memoryInfo = memoryMonitor.getCurrentMemoryInfo();

        // Assert
        assertNotNull(memoryInfo);
        assertTrue(memoryInfo.getMaxMemory() > 0, "Max memory should be positive");
        assertTrue(memoryInfo.getTotalMemory() > 0, "Total memory should be positive");
        assertTrue(memoryInfo.getFreeMemory() >= 0, "Free memory should be non-negative");
        assertTrue(memoryInfo.getUsedMemory() >= 0, "Used memory should be non-negative");
        assertTrue(memoryInfo.getUsagePercentage() >= 0 && memoryInfo.getUsagePercentage() <= 1,
                "Memory usage percentage should be between 0 and 1");
        
        // Verify mathematical relationship
        assertEquals(memoryInfo.getTotalMemory() - memoryInfo.getFreeMemory(), 
                     memoryInfo.getUsedMemory(),
                     "Used memory should equal total - free");
    }

    @Test
    void getCurrentMemoryInfo_ToReadableString_ReturnsFormattedString() {
        // Act
        MemoryMonitor.MemoryInfo memoryInfo = memoryMonitor.getCurrentMemoryInfo();
        String readable = memoryInfo.toReadableString();

        // Assert
        assertNotNull(readable);
        assertTrue(readable.contains("Memory:"), "Should contain 'Memory:' label");
        assertTrue(readable.contains("%"), "Should contain percentage symbol");
        assertTrue(readable.contains("MB"), "Should contain MB units");
        assertTrue(readable.contains("/"), "Should contain separator between used/total");
    }

    @Test
    void isMemoryThresholdExceeded_ZeroThreshold_ReturnsTrue() {
        // Act
        boolean exceeded = memoryMonitor.isMemoryThresholdExceeded(0.0);

        // Assert
        assertTrue(exceeded, "Any memory usage should exceed 0% threshold");
    }

    @Test
    void isMemoryThresholdExceeded_OneThreshold_ReturnsFalse() {
        // Act
        boolean exceeded = memoryMonitor.isMemoryThresholdExceeded(1.0);

        // Assert
        assertFalse(exceeded, "Memory usage should not exceed 100% threshold");
    }

    @Test
    void memoryMonitoring_ConsistentResults() {
        // Act - Get memory info multiple times
        MemoryMonitor.MemoryInfo info1 = memoryMonitor.getCurrentMemoryInfo();
        MemoryMonitor.MemoryInfo info2 = memoryMonitor.getCurrentMemoryInfo();

        // Assert - Max memory should be consistent
        assertEquals(info1.getMaxMemory(), info2.getMaxMemory(),
                "Max memory should be consistent between calls");
        
        // Usage percentages should be reasonable (allowing for small variations)
        double diff = Math.abs(info1.getUsagePercentage() - info2.getUsagePercentage());
        assertTrue(diff < 0.1, "Memory usage should not vary drastically between consecutive calls");
    }
}