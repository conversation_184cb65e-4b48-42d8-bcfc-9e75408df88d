package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Builder;
import lombok.Value;

import java.time.Duration;

@Value
@Builder
public class ConcurrencyTestResult {
    String testName;
    int threadCount;
    int successfulThreads;
    Duration averageExecutionTime;
    int deadlocksDetected;
    int racConditionsDetected;
    
    @Builder.Default
    boolean success = false;
    
    String errorMessage;
}
