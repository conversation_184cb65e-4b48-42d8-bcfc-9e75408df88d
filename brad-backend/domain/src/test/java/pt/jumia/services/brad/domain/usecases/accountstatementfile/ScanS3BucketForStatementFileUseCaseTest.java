package pt.jumia.services.brad.domain.usecases.accountstatementfile;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.FileStorageClient;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.ExecutionLog.ExecutionLogStatus;
import pt.jumia.services.brad.domain.entities.dtos.AccountStatementFilesScanResponseDto;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatementFiles;
import pt.jumia.services.brad.domain.entities.fake.FakeExecutionLogs;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementFileRepository;
import pt.jumia.services.brad.domain.repository.brad.ExecutionLogRepository;
import pt.jumia.services.brad.domain.usecases.jobs.RunJobsUsecase;

@ExtendWith(MockitoExtension.class)
public class ScanS3BucketForStatementFileUseCaseTest {


    @Mock
    private AccountStatementFileRepository accountStatementFileRepository;

    @Mock
    private FileStorageClient fileStorageClient;

    @Mock
    private RunJobsUsecase runJobsUsecase;

    @Mock
    private ExecutionLogRepository executionLogRepository;

    @Mock
    private ConsumeAccountStatementFileUseCase consumeAccountStatementFileUseCase;

    @InjectMocks
    private ScanS3BucketForStatementFileUseCase scanS3BucketForStatementFileUseCase;


    @Test
    public void execute_whenNoFilesFound_thenLogsSuccessMessage() throws Exception {

        // GIVEN
        ExecutionLog executionLog = FakeExecutionLogs.FAKE_API_LOG;

        when(fileStorageClient.scanBucketPaginated(anyInt(), any())).thenReturn(
            AccountStatementFilesScanResponseDto.builder().accountStatementFiles(Collections.emptyList()).build());

        // WHEN
        scanS3BucketForStatementFileUseCase.execute(executionLog);

        // THEN
        verify(accountStatementFileRepository, never()).upsert(any());
        verify(consumeAccountStatementFileUseCase, never()).execute(any(), anyString());

    }

    @Test
    public void execute_whenSingleFileFoundAndNotDuplicated_thenSavesFile() throws Exception {
        // GIVEN
        ExecutionLog executionLog = FakeExecutionLogs.FAKE_API_LOG;
        AccountStatementFile file = FakeAccountStatementFiles.ACCOUNT_STATEMENT_FILE_1;
        when(fileStorageClient.scanBucketPaginated(anyInt(), any()))
            .thenReturn(AccountStatementFilesScanResponseDto.builder().accountStatementFiles(List.of(file)).build());
        when(accountStatementFileRepository.findAll(any(), any(), any())).thenReturn(Collections.emptyList());
        when(accountStatementFileRepository.upsert(any())).thenReturn(file);

        // WHEN
        scanS3BucketForStatementFileUseCase.execute(executionLog);

        // THEN
        verify(accountStatementFileRepository, times(1)).upsert(any());
        ArgumentCaptor<ExecutionLog> argumentCaptor = ArgumentCaptor.forClass(ExecutionLog.class);
        verify(executionLogRepository, times(1)).upsert(argumentCaptor.capture());
        verify(consumeAccountStatementFileUseCase, times(1)).execute(any(), any());
        assertEquals(ExecutionLogStatus.SYNCED, argumentCaptor.getValue().getLogStatus());
    }

    @Test
    public void execute_whenSingleFileFoundAndDuplicated_thenSavesAsDuplicated() throws Exception {
        // GIVEN
        ExecutionLog executionLog = FakeExecutionLogs.FAKE_API_LOG;
        AccountStatementFile file = FakeAccountStatementFiles.ACCOUNT_STATEMENT_FILE_1;
        when(fileStorageClient.scanBucketPaginated(anyInt(), any()))
            .thenReturn(AccountStatementFilesScanResponseDto.builder().accountStatementFiles(List.of(file)).build());
        when(accountStatementFileRepository.findAll(any(), any(), any()))
            .thenReturn(List.of(file.toBuilder().checksum("a_different_checksum").build())); // Duplicate with different checksum
        when(accountStatementFileRepository.upsert(any())).thenReturn(file);

        // WHEN
        scanS3BucketForStatementFileUseCase.execute(executionLog);

        // THEN
        verify(accountStatementFileRepository).upsert(argThat(savedFile ->
            savedFile.getProcessingStatus() == ProcessingStatus.DUPLICATED));
    }

    @Test
    public void execute_whenMultipleFilesFound_thenDoesNotSaveUnmodifiedFiles() throws Exception {
        // GIVEN
        ExecutionLog executionLog = FakeExecutionLogs.FAKE_API_LOG;
        AccountStatementFile file1 = FakeAccountStatementFiles.getFakeAccountStatementFiles(2).get(0);
        AccountStatementFile file2 = FakeAccountStatementFiles.getFakeAccountStatementFiles(2).get(1);
        when(fileStorageClient.scanBucketPaginated(anyInt(), any()))
            .thenReturn(AccountStatementFilesScanResponseDto.builder().accountStatementFiles(List.of(file1, file2)).build());
        when(accountStatementFileRepository.findAll(any(), any(), any()))
            .thenReturn(List.of(file1))
            .thenReturn(Collections.emptyList());
        when(accountStatementFileRepository.upsert(any())).thenReturn(file2);

        // WHEN
        scanS3BucketForStatementFileUseCase.execute(executionLog);

        // THEN
        ArgumentCaptor<AccountStatementFile> argumentCaptor = ArgumentCaptor.forClass(AccountStatementFile.class);
        verify(accountStatementFileRepository, times(1)).upsert(argumentCaptor.capture());
        verify(consumeAccountStatementFileUseCase, times(1)).execute(any(), any());
        assertEquals(file2.getProcessingStatus(), argumentCaptor.getValue().getProcessingStatus());
    }

    @Test
    public void execute_whenExceptionOccursDuringProcessing_thenLogsException() throws Exception {
        // GIVEN
        ExecutionLog executionLog = FakeExecutionLogs.FAKE_API_LOG;
        AccountStatementFile file = FakeAccountStatementFiles.ACCOUNT_STATEMENT_FILE_1;
        when(fileStorageClient.scanBucketPaginated(anyInt(), any()))
            .thenReturn(AccountStatementFilesScanResponseDto.builder().accountStatementFiles(List.of(file)).build());
        when(accountStatementFileRepository.findAll(any(), any(), any()))
            .thenThrow(new RuntimeException("Database error"));

        // WHEN
        scanS3BucketForStatementFileUseCase.execute(executionLog);

        // THEN
        verify(accountStatementFileRepository, never()).upsert(any());
        ArgumentCaptor<ExecutionLog> argumentCaptor = ArgumentCaptor.forClass(ExecutionLog.class);
        verify(executionLogRepository, times(1)).upsert(argumentCaptor.capture());
        assertEquals(ExecutionLogStatus.ERROR, argumentCaptor.getValue().getLogStatus());
    }

    @Test
    public void start_whenCalled_thenExecutesRunJobsUsecase() throws Exception {
        // GIVEN
        doNothing().when(runJobsUsecase).execute(ScanS3BucketForStatementFileUseCase.ACCOUNT_STATEMENTS_FILES_SCAN);

        // WHEN
        scanS3BucketForStatementFileUseCase.start();

        // THEN
        verify(runJobsUsecase).execute(ScanS3BucketForStatementFileUseCase.ACCOUNT_STATEMENTS_FILES_SCAN);
    }

}