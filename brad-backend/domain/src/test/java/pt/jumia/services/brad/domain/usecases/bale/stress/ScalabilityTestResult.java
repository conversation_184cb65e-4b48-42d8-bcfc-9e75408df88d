package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class ScalabilityTestResult {
    String testName;
    int baselineLoad;
    int maxLoad;
    double performanceDegradation;
    double scalabilityFactor;
    String bottleneckIdentified;
    
    @Builder.Default
    boolean success = false;
    
    String errorMessage;
    
    public double getLoadIncreaseFactor() {
        return baselineLoad > 0 ? (double) maxLoad / baselineLoad : 0.0;
    }
    
    public boolean isLinearScaling() {
        return scalabilityFactor > 0.8 * getLoadIncreaseFactor();
    }
}
