package pt.jumia.services.brad.domain.entities.error;

import org.junit.jupiter.api.Test;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;

import java.sql.SQLException;
import java.sql.SQLTimeoutException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class BaleErrorClassifierTest {

    @Test
    void shouldClassifyAccountNotFoundAsRecoverable() {
        NotFoundException accountNotFound = new NotFoundException("Account with reference 'ACC123' not found");
        
        assertTrue(BaleErrorClassifier.isRecoverableError(accountNotFound));
        assertFalse(BaleErrorClassifier.isCriticalError(accountNotFound));
        assertEquals(BaleErrorClassifier.ErrorCategory.RECOVERABLE, BaleErrorClassifier.categorizeError(accountNotFound));
    }

    @Test
    void shouldClassifyCurrencyNotFoundAsRecoverable() {
        NotFoundException currencyNotFound = new NotFoundException("Currency 'EUR' not found");
        
        assertTrue(BaleErrorClassifier.isRecoverableError(currencyNotFound));
        assertFalse(BaleErrorClassifier.isCriticalError(currencyNotFound));
        assertEquals(BaleErrorClassifier.ErrorCategory.RECOVERABLE, BaleErrorClassifier.categorizeError(currencyNotFound));
    }

    @Test
    void shouldClassifyFxRateNotFoundAsRecoverable() {
        NotFoundException fxRateNotFound = new NotFoundException("FX rate not found for currency pair");
        
        assertTrue(BaleErrorClassifier.isRecoverableError(fxRateNotFound));
        assertFalse(BaleErrorClassifier.isCriticalError(fxRateNotFound));
        assertEquals(BaleErrorClassifier.ErrorCategory.RECOVERABLE, BaleErrorClassifier.categorizeError(fxRateNotFound));
    }

    @Test
    void shouldClassifyDatabaseConnectionAsCritical() {
        SQLException connectionError = new SQLException("Connection to database failed");
        
        assertFalse(BaleErrorClassifier.isRecoverableError(connectionError));
        assertTrue(BaleErrorClassifier.isCriticalError(connectionError));
        assertEquals(BaleErrorClassifier.ErrorCategory.CRITICAL, BaleErrorClassifier.categorizeError(connectionError));
    }

    @Test
    void shouldClassifyTimeoutAsCritical() {
        SQLTimeoutException timeoutError = new SQLTimeoutException("Query timeout");
        
        assertFalse(BaleErrorClassifier.isRecoverableError(timeoutError));
        assertTrue(BaleErrorClassifier.isCriticalError(timeoutError));
        assertEquals(BaleErrorClassifier.ErrorCategory.CRITICAL, BaleErrorClassifier.categorizeError(timeoutError));
    }

    @Test
    void shouldClassifyOutOfMemoryAsCritical() {
        OutOfMemoryError memoryError = new OutOfMemoryError("Java heap space");
        
        assertFalse(BaleErrorClassifier.isRecoverableError(memoryError));
        assertTrue(BaleErrorClassifier.isCriticalError(memoryError));
        assertEquals(BaleErrorClassifier.ErrorCategory.CRITICAL, BaleErrorClassifier.categorizeError(memoryError));
    }

    @Test
    void shouldClassifyAuthenticationAsCritical() {
        RuntimeException authError = new RuntimeException("Authentication failed");
        
        assertFalse(BaleErrorClassifier.isRecoverableError(authError));
        assertTrue(BaleErrorClassifier.isCriticalError(authError));
        assertEquals(BaleErrorClassifier.ErrorCategory.CRITICAL, BaleErrorClassifier.categorizeError(authError));
    }

    @Test
    void shouldClassifyEntityErrorWithAccountAsRecoverable() {
        EntityErrorsException entityError = new EntityErrorsException("Account validation failed");
        
        assertTrue(BaleErrorClassifier.isRecoverableError(entityError));
        assertFalse(BaleErrorClassifier.isCriticalError(entityError));
        assertEquals(BaleErrorClassifier.ErrorCategory.RECOVERABLE, BaleErrorClassifier.categorizeError(entityError));
    }

    @Test
    void shouldClassifyInvalidAmountAsRecoverable() {
        RuntimeException invalidAmount = new RuntimeException("Invalid amount format");
        
        assertTrue(BaleErrorClassifier.isRecoverableError(invalidAmount));
        assertFalse(BaleErrorClassifier.isCriticalError(invalidAmount));
        assertEquals(BaleErrorClassifier.ErrorCategory.RECOVERABLE, BaleErrorClassifier.categorizeError(invalidAmount));
    }

    @Test
    void shouldClassifyParsingErrorAsRecoverable() {
        RuntimeException parsingError = new RuntimeException("Error parsing date field");
        
        assertTrue(BaleErrorClassifier.isRecoverableError(parsingError));
        assertFalse(BaleErrorClassifier.isCriticalError(parsingError));
        assertEquals(BaleErrorClassifier.ErrorCategory.RECOVERABLE, BaleErrorClassifier.categorizeError(parsingError));
    }

    @Test
    void shouldClassifyUnknownErrorAsDataError() {
        RuntimeException unknownError = new RuntimeException("Some unknown error");
        
        assertFalse(BaleErrorClassifier.isRecoverableError(unknownError));
        assertFalse(BaleErrorClassifier.isCriticalError(unknownError));
        assertEquals(BaleErrorClassifier.ErrorCategory.DATA_ERROR, BaleErrorClassifier.categorizeError(unknownError));
    }

    @Test
    void shouldHandleNullError() {
        assertEquals(BaleErrorClassifier.ErrorCategory.DATA_ERROR, BaleErrorClassifier.categorizeError(null));
        assertFalse(BaleErrorClassifier.isRecoverableError(null));
        assertFalse(BaleErrorClassifier.isCriticalError(null));
    }

    @Test
    void shouldCreateErrorContextWithDetails() {
        NotFoundException accountNotFound = new NotFoundException("Account 'ACC123' not found");
        String context = BaleErrorClassifier.createErrorContext(accountNotFound, "Account Lookup");
        
        assertNotNull(context);
        assertTrue(context.contains("RECOVERABLE"));
        assertTrue(context.contains("Account Lookup"));
        assertTrue(context.contains("NotFoundException"));
        assertTrue(context.contains("Account 'ACC123' not found"));
    }

    @Test
    void shouldHandleNestedExceptions() {
        RuntimeException rootCause = new RuntimeException("Connection timeout");
        RuntimeException wrappedException = new RuntimeException("Database operation failed", rootCause);
        
        assertTrue(BaleErrorClassifier.isCriticalError(wrappedException));
        assertEquals(BaleErrorClassifier.ErrorCategory.CRITICAL, BaleErrorClassifier.categorizeError(wrappedException));
    }

    @Test
    void shouldClassifyDatabaseErrorsWithConnectionMessageAsCritical() {
        DatabaseErrorsException dbError = new DatabaseErrorsException("Database connection lost");
        
        assertTrue(BaleErrorClassifier.isCriticalError(dbError));
        assertEquals(BaleErrorClassifier.ErrorCategory.CRITICAL, BaleErrorClassifier.categorizeError(dbError));
    }

    @Test
    void shouldClassifyDatabaseErrorsWithSchemaMessageAsCritical() {
        DatabaseErrorsException dbError = new DatabaseErrorsException("Schema validation failed");
        
        assertTrue(BaleErrorClassifier.isCriticalError(dbError));
        assertEquals(BaleErrorClassifier.ErrorCategory.CRITICAL, BaleErrorClassifier.categorizeError(dbError));
    }

    @Test
    void shouldClassifyPermissionErrorAsCritical() {
        RuntimeException permissionError = new RuntimeException("Access denied to resource");
        
        assertTrue(BaleErrorClassifier.isCriticalError(permissionError));
        assertEquals(BaleErrorClassifier.ErrorCategory.CRITICAL, BaleErrorClassifier.categorizeError(permissionError));
    }
}