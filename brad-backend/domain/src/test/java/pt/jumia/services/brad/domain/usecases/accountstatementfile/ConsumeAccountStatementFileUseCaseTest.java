package pt.jumia.services.brad.domain.usecases.accountstatementfile;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.FileStorageClient;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;
import pt.jumia.services.brad.domain.entities.ApiLog;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatementFiles;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeApiLogs;
import pt.jumia.services.brad.domain.entities.files.SwiftMessageFileResponse;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.AccountStatementFileRepository;
import pt.jumia.services.brad.domain.usecases.apilog.CreateApiLogUseCase;
import pt.jumia.services.brad.domain.usecases.processconvertfiles.CreateParsedSwiftMessage;


@ExtendWith(MockitoExtension.class)
class ConsumeAccountStatementFileUseCaseTest {

    @Mock
    private AccountStatementFileRepository accountStatementFileRepository;

    @Mock
    private ReadAccountStatementFileUseCase readAccountStatementFileUseCase;

    @Mock
    private FileStorageClient fileStorageClient;

    @Mock
    private CreateParsedSwiftMessage createParsedSwiftMessage;

    @Mock
    private CreateApiLogUseCase createApiLogUseCase;

    @InjectMocks
    private ConsumeAccountStatementFileUseCase consumeAccountStatementFileUseCase;

    private Long accountStatementId;
    private AccountStatementFile accountStatementFile;
    private File testFile;
    private SwiftMessageFileResponse swiftMessageFileResponse;

    @BeforeEach
    void setUp() throws IOException {

        accountStatementId = 1L;
        accountStatementFile = FakeAccountStatementFiles.ACCOUNT_STATEMENT_FILE_1;
        testFile = new File("test-file.txt");
        FileOutputStream fos = new FileOutputStream(testFile);
        fos.write(new byte[1]);
        fos.close();
        swiftMessageFileResponse = buildResponse();
    }

    @AfterEach
    void tearDown() throws IOException {

        if (testFile.exists()) {
            Files.delete(testFile.toPath());
        }
    }

    @Test
    void execute_whenFileIsProcessedSuccessfully_thenReturnUpdatedAccountStatementFile() throws Exception {
        // GIVEN
        accountStatementFile = accountStatementFile.toBuilder().name("test-file.txt").build();
        when(readAccountStatementFileUseCase.execute(accountStatementId)).thenReturn(accountStatementFile);
        when(fileStorageClient.getaccountStatementFile(accountStatementFile.getName())).thenReturn(testFile);
        when(createParsedSwiftMessage.parseSwiftMessage(any(),any(), any())).thenReturn(swiftMessageFileResponse);
        when(accountStatementFileRepository.upsert(any(AccountStatementFile.class)))
            .thenReturn(accountStatementFile.toBuilder().processingStatus(ProcessingStatus.PROCESSING).build())
            .thenReturn(accountStatementFile.toBuilder()
                .processingStatus(ProcessingStatus.PROCESSED)
                .statusDescription(ProcessingStatus.PROCESSED.getDescription())
                .build());

        // WHEN
        consumeAccountStatementFileUseCase.execute(accountStatementId, null);

        // THEN

     ArgumentCaptor<AccountStatementFile> captor = ArgumentCaptor.forClass(AccountStatementFile.class);
     verify(accountStatementFileRepository, times(2)).upsert(captor.capture());
     List<AccountStatementFile> capturedFiles = captor.getAllValues();
     assertEquals(ProcessingStatus.PROCESSING, capturedFiles.get(0).getProcessingStatus());
     assertEquals(ProcessingStatus.PROCESSED, capturedFiles.get(1).getProcessingStatus());
    }

    @Test
    void execute_whenReadAccountStatementFileFails_thenThrowException() throws Exception {
        // GIVEN
        when(readAccountStatementFileUseCase.execute(accountStatementId)).thenThrow(new NotFoundException("File not found"));

        // WHEN & THEN
        Exception exception = assertThrows(NotFoundException.class, () -> {
            consumeAccountStatementFileUseCase.execute(accountStatementId, null);
        });
        assertEquals("File not found", exception.getMessage());
    }

    @Test
    void execute_whenParseSwiftMessageFails_thenFileIsSavedWithStatusDescription() throws Exception {
        // GIVEN
        when(readAccountStatementFileUseCase.execute(accountStatementId)).thenReturn(accountStatementFile);
        when(fileStorageClient.getaccountStatementFile(accountStatementFile.getName())).thenReturn(testFile);
        when(accountStatementFileRepository.upsert(any(AccountStatementFile.class))).thenReturn(accountStatementFile);
        when(createApiLogUseCase.execute(any())).thenReturn(FakeApiLogs.FAKE_API_LOG);
        when(createParsedSwiftMessage.parseSwiftMessage(any(),any(), anyString())).thenThrow(new RuntimeException("Parsing error"));

        // WHEN
        consumeAccountStatementFileUseCase.execute(accountStatementId, null);

        // THEN
        ArgumentCaptor<AccountStatementFile> captor = ArgumentCaptor.forClass(AccountStatementFile.class);
        verify(accountStatementFileRepository, times(2)).upsert(captor.capture());
        List<AccountStatementFile> capturedFiles = captor.getAllValues();
        assertEquals(ProcessingStatus.PROCESSING, capturedFiles.get(0).getProcessingStatus());
        assertEquals(ProcessingStatus.FAILED_PROCESSING, capturedFiles.get(1).getProcessingStatus());
        verify(createApiLogUseCase, times(1)).execute(any(ApiLog.class));


    }

    private static @NotNull SwiftMessageFileResponse buildResponse() {

        return SwiftMessageFileResponse.builder()
            .processingStatus(ProcessingStatus.PROCESSED)
            .statement(FakeAccountStatements.FAKE_ACCOUNT_STATEMENT)
            .accountNumber(FakeAccounts.FAKE_ACCOUNT.getAccountNumber())
            .build();
    }

}