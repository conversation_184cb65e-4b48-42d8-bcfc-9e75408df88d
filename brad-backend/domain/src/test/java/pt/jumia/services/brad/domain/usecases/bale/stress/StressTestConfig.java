package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Builder;
import lombok.Value;

import java.time.Duration;

@Value
@Builder
public class StressTestConfig {
    
    @Builder.Default
    double maxMemoryUsage = 0.9;
    
    @Builder.Default
    Duration maxExecutionTime = Duration.ofMinutes(30);
    
    @Builder.Default
    int expectedThroughput = 1000;
    
    @Builder.Default
    int maxConcurrentThreads = 10;
    
    @Builder.Default
    double errorTolerance = 0.05;
}
