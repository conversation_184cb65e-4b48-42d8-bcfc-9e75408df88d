package pt.jumia.services.brad.domain.usecases.transactions;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.entities.fake.FakeTransaction;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.TransactionRepository;
import pt.jumia.services.brad.domain.usecases.accountstatement.RetryAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.RevalidateAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.fxrates.brad.ReadBradFxRateUseCase;

import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CreateTransactionUseCaseTest {

    @Mock
    private TransactionRepository transactionRepository;

    @Mock
    private ReadCurrenciesUseCase readCurrenciesUseCase;

    @Mock
    private RetryAccountStatementUseCase retryAccountStatementUseCase;

    @Mock
    private ReadBradFxRateUseCase readBradFxRateUseCase;

    @Mock
    private RevalidateAccountStatementUseCase revalidateAccountStatementUseCase;

    @InjectMocks
    private CreateTransactionUseCase createTransactionUseCase;


    @Test
    void execute_whenTransactionIsValid_returnsCreatedTransaction() throws NotFoundException, EntityErrorsException,
            InterruptedException, DatabaseErrorsException {

        Account account = FakeAccounts.FAKE_ACCOUNT.toBuilder()
                .currency(FakeCurrencies.NGN).build();
        //GIVEN
        AccountStatement accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
                .account(account)
                .currency(FakeCurrencies.NGN).build();

        final Transaction transactionToCreate = FakeTransaction
                .getFakeCreditTransactions(1, accountStatement).get(0);

        when(transactionRepository.insert(any(Transaction.class), any(String.class)))
                .thenReturn(CompletableFuture.completedFuture(
                        FakeTransaction.getFakeCreditTransactions(1, accountStatement).get(0)
                                .toBuilder()
                                .currency(FakeCurrencies.NGN)
                                .build()
                        )
                );
        when(readCurrenciesUseCase.execute(FakeCurrencies.NGN.getCode())).thenReturn(FakeCurrencies.NGN);

        createTransactionUseCase.execute(
                List.of(transactionToCreate), "username", accountStatement
        );

        verify(transactionRepository).insert(any(Transaction.class), any(String.class));
        verify(revalidateAccountStatementUseCase).execute(any());
    }

}
