package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.Data;

@Data
public class ResourceMetrics {
    
    private long peakMemoryUsage = 0;
    private double peakCpuUsage = 0.0;
    private int peakDatabaseConnections = 0;
    
    public void updatePeakMemoryUsage(long memoryUsageMB) {
        if (memoryUsageMB > this.peakMemoryUsage) {
            this.peakMemoryUsage = memoryUsageMB;
        }
    }
    
    public void updatePeakCpuUsage(double cpuUsage) {
        if (cpuUsage > this.peakCpuUsage) {
            this.peakCpuUsage = cpuUsage;
        }
    }
    
    public void updatePeakDatabaseConnections(int connections) {
        if (connections > this.peakDatabaseConnections) {
            this.peakDatabaseConnections = connections;
        }
    }
}
