package pt.jumia.services.brad.domain.usecases.bale.stress;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class PerformanceMonitor {

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private final List<PerformanceSnapshot> snapshots = new ArrayList<>();
    private volatile boolean monitoring = false;

    public void startMonitoring() {
        if (monitoring) {
            return;
        }
        
        monitoring = true;
        snapshots.clear();
        
        scheduler.scheduleAtFixedRate(this::captureSnapshot, 0, 1, TimeUnit.SECONDS);
        log.debug("Performance monitoring started");
    }

    public void stopMonitoring() {
        monitoring = false;
        log.debug("Performance monitoring stopped");
    }

    public void reset() {
        snapshots.clear();
        log.debug("Performance metrics reset");
    }

    public PerformanceMetrics getMetrics() {
        if (snapshots.isEmpty()) {
            return PerformanceMetrics.empty();
        }

        double avgCpu = snapshots.stream()
                .mapToDouble(PerformanceSnapshot::getCpuUsage)
                .average()
                .orElse(0.0);

        double peakCpu = snapshots.stream()
                .mapToDouble(PerformanceSnapshot::getCpuUsage)
                .max()
                .orElse(0.0);

        double cpuVariance = calculateVariance(
                snapshots.stream().mapToDouble(PerformanceSnapshot::getCpuUsage).toArray());

        return PerformanceMetrics.builder()
                .averageCpuUsage(avgCpu)
                .peakCpuUsage(peakCpu)
                .cpuVariance(cpuVariance)
                .snapshotCount(snapshots.size())
                .build();
    }

    private void captureSnapshot() {
        if (!monitoring) {
            return;
        }

        try {
            double cpuUsage = getCpuUsage();
            long timestamp = System.currentTimeMillis();

            PerformanceSnapshot snapshot = PerformanceSnapshot.builder()
                    .timestamp(timestamp)
                    .cpuUsage(cpuUsage)
                    .build();

            snapshots.add(snapshot);

            if (snapshots.size() > 3600) {
                snapshots.remove(0);
            }

        } catch (Exception e) {
            log.warn("Error capturing performance snapshot", e);
        }
    }

    private double getCpuUsage() {
        com.sun.management.OperatingSystemMXBean osBean = 
            (com.sun.management.OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
        return osBean.getProcessCpuLoad() * 100.0;
    }

    private double calculateVariance(double[] values) {
        if (values.length < 2) {
            return 0.0;
        }

        double mean = 0.0;
        for (double value : values) {
            mean += value;
        }
        mean /= values.length;

        double variance = 0.0;
        for (double value : values) {
            variance += Math.pow(value - mean, 2);
        }
        variance /= values.length;

        return variance;
    }
}
