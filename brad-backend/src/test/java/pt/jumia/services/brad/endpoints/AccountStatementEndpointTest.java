package pt.jumia.services.brad.endpoints;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.prowidesoftware.swift.model.AbstractMessage;
import lombok.SneakyThrows;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import pt.jumia.services.brad.api.payloads.request.accountstatement.AccountStatementWithFileApiRequestPayload;
import pt.jumia.services.brad.api.payloads.response.AccountStatementApiResponsePayload;
import pt.jumia.services.brad.api.payloads.response.PageApiResponsePayload;
import pt.jumia.services.brad.domain.entities.AccountStatement;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.Transaction;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.account.Country;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeAccountStatements;
import pt.jumia.services.brad.domain.entities.fake.FakeCountries;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.entities.fake.FakeFxRates;
import pt.jumia.services.brad.domain.entities.fake.FakeTransaction;
import pt.jumia.services.brad.domain.entities.files.SwiftMessageFileResponse;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.enumerations.AccountStatementFlow;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.enumerations.Direction;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.usecases.accountstatement.ReadAccountStatementUseCase;
import pt.jumia.services.brad.domain.usecases.processconvertfiles.AbstractMessageParser;
import pt.jumia.services.brad.domain.usecases.processconvertfiles.convertfile.ConvertFile;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static pt.jumia.services.brad.domain.entities.fake.FakeAccounts.FAKE_ACCOUNT;

public class AccountStatementEndpointTest extends BaseEndpointTest {

    private List<AccountStatement> statementList;
    private Account aAccount;

    private static final int TIMEOUT_SECONDS = 5;

    @Autowired
    ReadAccountStatementUseCase readAccountStatementUseCase;

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
        Currency createdCurrency = createCurrency(FakeCurrencies.NGN).toEntity();
        Country createdCountry = createCountry(FakeCountries.NIGERIA.toBuilder().currency(createdCurrency).build()).toEntity();
        aAccount = createAccount(FAKE_ACCOUNT
                .toBuilder()
                .country(createdCountry)
                .currency(createdCurrency)
                .build());
    }

    @Test
    public void fetchAll_success() throws JsonProcessingException {
        statementList = FakeAccountStatements.getFakeAccountStatements(20, aAccount);
        List<AccountStatement> expectedAccountStatements = new ArrayList<>();
        for (AccountStatement statement : statementList) {
            expectedAccountStatements.add(statement.withoutDbFields().toBuilder().build());
        }

        PageFilters pageFilters = PageFilters.builder()
                .page(1)
                .size(20)
                .build();


        expectedAccountStatements.forEach(statement ->
                createStatementWithTransactions(aAccount, statement, FakeTransaction.getFakeCreditTransactions(10, statement)));

        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder().accountID(aAccount.getId()).build();
        PageApiResponsePayload<AccountStatementApiResponsePayload> accountStatements = accountStatementRobot.fetchAll(accountStatementFilters, null, pageFilters);

        assertThat(accountStatements.getResults())
                .hasSize(expectedAccountStatements.size());

    }

    @Test
    public void fetch_withFilters_success() throws JsonProcessingException {
        statementList = FakeAccountStatements.getFakeAccountStatements(20, aAccount);
        List<AccountStatement> expectedAccountStatements = new ArrayList<>();
        for (AccountStatement statement : statementList) {
            expectedAccountStatements.add(statement.withoutDbFields().toBuilder().build());
        }

        PageFilters pageFilters = PageFilters.builder()
                .page(1)
                .size(20)
                .build();

        AccountStatementFilters filters = AccountStatementFilters.builder()
                .statementId(expectedAccountStatements.get(0).getStatementId())
                .accountID(aAccount.getId())
                .build();

        expectedAccountStatements.forEach(statement ->
                createStatementWithTransactions(aAccount, statement, FakeTransaction.getFakeCreditTransactions(10, statement)));

        PageApiResponsePayload<AccountStatementApiResponsePayload> accountStatements = accountStatementRobot.fetchAll(filters, null, pageFilters);

        //11 accountstatements because of nº1 and nº10-19
        assertThat(accountStatements.getResults())
                .hasSize(11);

    }

    @Test
    public void fetch_withAllFilters_success() throws JsonProcessingException {
        statementList = FakeAccountStatements.getFakeAccountStatements(20, aAccount);
        List<AccountStatement> expectedAccountStatements = new ArrayList<>();
        for (AccountStatement statement : statementList) {
            expectedAccountStatements.add(statement.withoutDbFields().toBuilder().build());
        }

        PageFilters pageFilters = PageFilters.builder()
                .page(1)
                .size(20)
                .build();


        List<AccountStatement> createdStatements = new ArrayList<>();

        expectedAccountStatements.forEach(statement ->
                createdStatements.add(createStatementWithTransactions(aAccount, statement,
                        FakeTransaction.getFakeCreditTransactions(10, statement))));

        AccountStatementFilters filters = AccountStatementFilters.builder()
                .currencyCodes(List.of(createdStatements.get(15).getCurrency().getCode()))
                .statementId(createdStatements.get(15).getStatementId())
//                .initialDate(createdStatements.get(15).getInitialDate())
//                .finalDate(createdStatements.get(15).getFinalDate())
                .initialDirection(List.of(createdStatements.get(15).getInitialDirection().toString()))
                .finalDirection(List.of(Direction.getDirection(createdStatements.get(15).getFinalDirection().toString()).toString()))
                .initialAmount(createdStatements.get(15).getInitialAmount())
                .finalAmount(createdStatements.get(15).getFinalAmount())
                .accountID(createdStatements.get(15).getAccount().getId())
//                .createdAt(createdStatements.get(15).getCreatedAt())
                .build();

        PageApiResponsePayload<AccountStatementApiResponsePayload> accountStatements = accountStatementRobot.fetchAll(filters, null, pageFilters);

        assertThat(accountStatements.getResults())
                .hasSize(1);
        assertThat(accountStatements.getResults().get(0).getStatementId())
                .isEqualTo(createdStatements.get(15).getStatementId());

    }


    @Test
    public void createAccountStatement_noTransactions_returnsBadRequest() {
        //GIVEN
        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0);

        List<Transaction> transactions = new ArrayList<>();

        accountStatementRobot.createFails(accountStatement, transactions)
                .statusCode(HttpStatus.BAD_REQUEST.value());
    }

    @Test
    public void createAccountStatement_negativeInitialAmount_returnsBadRequest() {

        int numberOfTransactions = 10;

        //GIVEN
        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .initialAmount(BigDecimal.valueOf(-10))
                .build();

        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(numberOfTransactions, accountStatement);


        accountStatementRobot.createFails(accountStatement, transactions)
                .statusCode(HttpStatus.BAD_REQUEST.value());
    }

    @Test
    public void createAccountStatement_negativeFinalAmount_returnsBadRequest() {

        int numberOfTransactions = 10;

        //GIVEN
        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .finalAmount(BigDecimal.valueOf(-10))
                .build();

        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(numberOfTransactions, accountStatement);


        accountStatementRobot.createFails(accountStatement, transactions)
                .statusCode(HttpStatus.BAD_REQUEST.value());
    }

    @Test
    public void createAccountStatement_noAccount_returnsNotFound() {
        //GIVEN
        AccountStatement accountStatement = FakeAccountStatements.FAKE_ACCOUNT_STATEMENT.toBuilder()
                .account(FAKE_ACCOUNT.toBuilder().accountNumber("nonExistingAccountID").build())
                .build();

        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(1, accountStatement);

        accountStatementRobot.createFails(accountStatement, transactions)
                .statusCode(HttpStatus.NOT_FOUND.value());
    }

    @SneakyThrows
    @Test
    void createTransaction_whenRequestIsValid_createsTransactionSuccessfully() {
        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0);


        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(10, accountStatement);
        AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);
        TransactionFilters transactionFilters = TransactionFilters.builder()
                .accountStatementID(String.valueOf(createdAccountStatement.getId()))
                .build();
        List<Transaction> createdTransactions = transactionRepository.findAllWithReconciliation(transactionFilters, null, null);

        assertThat(createdTransactions.size())
                .isEqualTo(transactions.size());
    }


    @Test
    void createTransactions_whenRequestIsValid_importStatementSuccessfully() {

        this.createImportedStatementWithTransactions();

    }


    @Test
    void getTransaction_withExistingID_returnStatement() {
        AccountStatement accountStatement = this.createImportedStatementWithTransactions();

        AccountStatementApiResponsePayload fetchedAccountStatement = accountStatementRobot.fetchByID(accountStatement.getId());

        assertThat(fetchedAccountStatement.getId())
                .isEqualTo(accountStatement.getId());
        assertThat(fetchedAccountStatement.getStatementId())
                .isEqualTo(accountStatement.getStatementId());

    }

    @Test
    void getTransaction_withNonExistingID_returnNotFound() {

        accountStatementRobot.fetchByIDFails(9999L)
                .statusCode(HttpStatus.NOT_FOUND.value());
    }

    @Test
    void createTransactions_whenPreviousStatementIsValid_importStatementSuccessfully() {

        int numberOfTransactions = 10;

        AccountStatement previousStatement = createImportedStatementWithTransactions();

        //Statement has initial amount 10
        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .statementId("2")
                .previousStatement(previousStatement)
                .initialAmount(previousStatement.getFinalAmount())
                .initialDirection(previousStatement.getFinalDirection())
                .finalDirection(previousStatement.getFinalDirection())
                .finalAmount(previousStatement.getFinalAmount().add(BigDecimal.valueOf(numberOfTransactions)))
                .initialDate(previousStatement.getFinalDate().plusDays(1))
                .finalDate(previousStatement.getFinalDate().plusDays(1))
                .build();

        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(numberOfTransactions, accountStatement);

        AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);


        Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
            assert accountStatementRepository.findById(createdAccountStatement.getId()).isPresent();
            assertThat(accountStatementRepository.findById(createdAccountStatement.getId()).get().getStatus())
                    .isEqualTo(AccountStatementStatus.IMPORTED);
        });

    }

    //Testing amount calculation with different types of transactions
    @Test
    void createTransactions_whenDifferentTypesOfTransactions_importStatementSuccessfully() {

        int numberOfCreditTransactions = 10;
        int numberOfDebitTransactions = 50;

        AccountStatement previousStatement = createImportedStatementWithTransactions();


        //Statement has initial amount 10
        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .statementId("2")
                .previousStatement(previousStatement)
                .initialAmount(previousStatement.getFinalAmount())
                .initialDirection(previousStatement.getFinalDirection())
                .finalDirection(Direction.DEBIT)
                .finalAmount(previousStatement.getFinalAmount().add(
                        BigDecimal.valueOf(numberOfCreditTransactions).subtract(BigDecimal.valueOf(numberOfDebitTransactions))).abs())
                .initialDate(previousStatement.getFinalDate().plusDays(1))
                .finalDate(previousStatement.getFinalDate().plusDays(1))
                .build();

        //+CREDIT
        List<Transaction> transactionsCredit = FakeTransaction.getFakeCreditTransactions(10, accountStatement);

        //-DEBIT
        List<Transaction> transactionsDebit = FakeTransaction.getFakeDebitTransactions(50, accountStatement);

        //join both lists
        List<Transaction> transactions = new ArrayList<>();
        transactions.addAll(transactionsDebit);
        transactions.addAll(transactionsCredit);

        AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);

        Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
            Optional<AccountStatement> statement = accountStatementRepository.findById(createdAccountStatement.getId());
            assert statement.isPresent();


            assertThat(statement.get().getStatus())
                    .isEqualTo(AccountStatementStatus.IMPORTED);
        });

    }

    //Testing errors on statement


    @Test
    void createTransactions_whenPreviousStatementIsValidAndRequestInvalidPeriodOverlap_PeriodOverlapError() {

        int numberOfTransactions = 10;

        AccountStatement previousStatement = createImportedStatementWithTransactions();

        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .statementId("2")
                .previousStatement(previousStatement)
                .initialAmount(previousStatement.getFinalAmount())
                .initialDirection(previousStatement.getFinalDirection())
                .finalDirection(previousStatement.getFinalDirection())
                .finalAmount(previousStatement.getFinalAmount().add(BigDecimal.valueOf(numberOfTransactions)))
                .initialDate(previousStatement.getFinalDate().minusDays(1L))
                .finalDate(previousStatement.getFinalDate())
                .build();

        //Each transaction has +Credit value 1
        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(numberOfTransactions, accountStatement);

        AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);

        Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
            Optional<AccountStatement> statement = accountStatementRepository.findById(createdAccountStatement.getId());
            assert statement.isPresent();


            assertThat(statement.get().getStatus())
                    .isEqualTo(AccountStatementStatus.REVIEW);
            assertThat(statement.get().getStatusDescription())
                    .isEqualTo(AccountStatementStatus.Description.ERROR_PERIOD_OVERLAP);
        });

    }

    @Test
    void createTransactions_whenPreviousStatementIsInReviewAndErrorPeriodOverlap_previousStatementError() {

        int numberOfTransactions = 10;

        AccountStatement previousStatement = createPeriodOverlapErrorStatementWithTransactions();

        //Statement has initial amount 10
        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .statementId("2")
                .previousStatement(previousStatement)
                .initialAmount(previousStatement.getFinalAmount())
                .initialDirection(previousStatement.getFinalDirection())
                .finalDirection(previousStatement.getFinalDirection())
                .finalAmount(previousStatement.getFinalAmount().add(BigDecimal.valueOf(numberOfTransactions)))
                .initialDate(previousStatement.getFinalDate().minusDays(1L))
                .finalDate(previousStatement.getFinalDate())
                .build();

        //Each transaction has +Credit value 1
        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(numberOfTransactions, accountStatement);

        AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);

        Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
            Optional<AccountStatement> statement = accountStatementRepository.findById(createdAccountStatement.getId());
            assert statement.isPresent();

            assertThat(statement.get().getStatus())
                    .isEqualTo(AccountStatementStatus.REVIEW);
            assertThat(statement.get().getStatusDescription())
                    .isEqualTo(AccountStatementStatus.Description.ERROR_PREVIOUS_STATEMENT);
        });
    }

    @Test
    void createTransactions_whenPreviousStatementIsInReview_previousStatementError() {

        int numberOfTransactions = 1;

        AccountStatement previousStatement = createPeriodOverlapErrorStatementWithTransactions();

        //Statement has initial amount 10
        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .statementId("2")
                .previousStatement(previousStatement)
                .initialAmount(previousStatement.getFinalAmount())
                .initialDirection(previousStatement.getFinalDirection())
                .finalDirection(previousStatement.getFinalDirection())
                .finalAmount(previousStatement.getFinalAmount().add(BigDecimal.valueOf(numberOfTransactions)))
                .initialDate(previousStatement.getFinalDate())
                .finalDate(previousStatement.getFinalDate().plusDays(1L))
                .build();

        //Each transaction has +Credit value 1
        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(numberOfTransactions, accountStatement);

        AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);

        Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
            Optional<AccountStatement> statement = accountStatementRepository.findById(createdAccountStatement.getId());
            assert statement.isPresent();
            assertThat(statement.get().getStatus())
                    .isEqualTo(AccountStatementStatus.REVIEW);
            assertThat(statement.get().getStatusDescription())
                    .isEqualTo(AccountStatementStatus.Description.ERROR_PREVIOUS_STATEMENT);
        });

    }


    @Test
    void createTransactions_whenPreviousStatementIsValidAndRequestInvalidTransactionsPeriodOverlap_PeriodOverlapError() {

        int numberOfTransactions = 10;

        AccountStatement previousStatement = createImportedStatementWithTransactions();

        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .statementId("2")
                .previousStatement(previousStatement)
                .initialAmount(previousStatement.getFinalAmount())
                .initialDirection(previousStatement.getFinalDirection())
                .finalDirection(previousStatement.getFinalDirection())
                .finalAmount(previousStatement.getFinalAmount().add(BigDecimal.valueOf(numberOfTransactions)))
                .initialDate(previousStatement.getFinalDate())
                .finalDate(previousStatement.getFinalDate().plusDays(1L))
                .build();

        //Each transaction has +Credit value 1
        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(numberOfTransactions, accountStatement);

        transactions.set(5, transactions.get(5).toBuilder()
                .transactionDate(accountStatement.getInitialDate().minusDays(1L))
                .build());

        AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);

        Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
            Optional<AccountStatement> statement = accountStatementRepository.findById(createdAccountStatement.getId());
            assert statement.isPresent();


            assertThat(statement.get().getStatus())
                    .isEqualTo(AccountStatementStatus.REVIEW);
            assertThat(statement.get().getStatusDescription())
                    .isEqualTo(AccountStatementStatus.Description.ERROR_PERIOD_OVERLAP);
        });

    }


    @Test
    void createTransactions_whenPreviousStatementIsInReviewAndInvalidPeriodOverlap_previousStatementError() {

        int numberOfTransactions = 1;

        AccountStatement previousStatement = createImportedStatementWithTransactions();

        //Statement has initial amount 10
        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .statementId("2")
                .previousStatement(previousStatement)
                .initialAmount(previousStatement.getFinalAmount())
                .initialDirection(previousStatement.getFinalDirection())
                .finalDirection(previousStatement.getFinalDirection())
                .finalAmount(previousStatement.getFinalAmount().add(BigDecimal.valueOf(numberOfTransactions)))
                .initialDate(previousStatement.getFinalDate().minusDays(1L))
                .finalDate(previousStatement.getFinalDate())
                .build();

        //Each transaction has +Credit value 1
        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(numberOfTransactions, accountStatement);

        AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);

        Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
            Optional<AccountStatement> statement = accountStatementRepository.findById(createdAccountStatement.getId());
            assert statement.isPresent();

            assertThat(!statement.get().getPreviousStatement().getFinalDate().isAfter(statement.get().getInitialDate()))
                    .isEqualTo(false);

            assertThat(statement.get().getStatus())
                    .isEqualTo(AccountStatementStatus.REVIEW);
            assertThat(statement.get().getStatusDescription())
                    .isEqualTo(AccountStatementStatus.Description.ERROR_PERIOD_OVERLAP);
        });

    }

    @Test
    void createAccountStatement_withParseSwiftMessageUseCaseForMTFile() throws IOException, NotFoundException {

        ClassLoader classLoader = getClass().getClassLoader();
        File fileMT = new File(classLoader.getResource("files/filestobeparsed/JUMIA(*************)_01Aug2024.TXT").getFile());
        AbstractMessage message = AbstractMessageParser.getMessage(fileMT);
        ConvertFile convertFile = AbstractMessageParser.getConvertFile(message);
        SwiftMessageFileResponse response = AbstractMessageParser.generateSwiftMessageFileResponse(convertFile, message);

        String expectedAccountId = response.getAccountNumber();
        List<Transaction> transactionList = response.getTransactions();
        AccountStatement expectedAccountStatement = response.getStatement();


        aAccount = createAccount(FakeAccounts.FAKE_ACCOUNT
                .toBuilder()
                .companyID("MTfakeCompanyID")
                .navReference("MTfakeNavReference")
                .accountNumber(expectedAccountId)
                .build());

        AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, expectedAccountStatement, transactionList);

        Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
            Optional<AccountStatement> statement = accountStatementRepository.findById(createdAccountStatement.getId());
            assert statement.isPresent();

            assertThat(statement.get().getStatus())
                    .isEqualTo(AccountStatementStatus.IMPORTED);

        });

    }

    @Test
    void createAccountStatement_withParseSwiftMessageUseCaseForMXFile() throws IOException, NotFoundException {
        Currency createdCurrency = createCurrency(FakeCurrencies.UGX).toEntity();
        Country createdCountry = createCountry(FakeCountries.UGANDA.toBuilder().currency(createdCurrency).build()).toEntity();

        ClassLoader classLoader = getClass().getClassLoader();
        File fileMX = new File(classLoader.getResource("files/filestobeparsed/CAMT053_100626001_2024042402061458").getFile());
        AbstractMessage message = AbstractMessageParser.getMessage(fileMX);
        ConvertFile convertFile = AbstractMessageParser.getConvertFile(message);
        SwiftMessageFileResponse response = AbstractMessageParser.generateSwiftMessageFileResponse(convertFile, message);

        String expectedAccountId = response.getAccountNumber();
        List<Transaction> transactionList = response.getTransactions();
        AccountStatement expectedAccountStatement = response.getStatement();


        aAccount = createAccount(FakeAccounts.FAKE_ACCOUNT_MX
                .toBuilder()
                .accountNumber(expectedAccountId)
                .country(createdCountry)
                .currency(createdCurrency)
                .build());

        AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, expectedAccountStatement, transactionList);

        Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
            Optional<AccountStatement> statement = accountStatementRepository.findById(createdAccountStatement.getId());
            assert statement.isPresent();

            assertThat(statement.get().getStatus())
                    .isEqualTo(AccountStatementStatus.IMPORTED);

        });

    }

    @Test
    void retryStatement_whenStatementDoesNotExist_returnsNotFound() {

        final String response = accountStatementRobot.retryStatement(1L)
                .statusCode(HttpStatus.NOT_FOUND.value())
                .extract()
                .response()
                .body()
                .prettyPrint();

        assertThat(response)
                .contains(String.format("AccountStatement with id %s not found", 1));
    }

    @Test
    void retryStatement_whenStatementHasAlreadyBeenValidated_returnsSuccess() {

        final AccountStatement accountStatement = createImportedStatementWithTransactions();

        final String response = accountStatementRobot.retryStatement(accountStatement.getId())
                .statusCode(HttpStatus.OK.value())
                .extract()
                .response()
                .body()
                .prettyPrint();

        assertThat(response).isEqualTo(String.format("Statement with in status - %s cannot be retried", accountStatement.getStatus()));
    }

    @Test
    void retryStatement_success() {

        int numberOfTransactions = 1;

        AccountStatement invalidParentAccountStatement = createImportedStatementWithTransactions();

        //Statement has initial amount 10
        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .statementId("2")
                .previousStatement(invalidParentAccountStatement)
                .initialAmount(invalidParentAccountStatement.getFinalAmount())
                .initialDirection(invalidParentAccountStatement.getFinalDirection())
                .finalDirection(invalidParentAccountStatement.getFinalDirection())
                .finalAmount(invalidParentAccountStatement.getFinalAmount().add(BigDecimal.valueOf(numberOfTransactions)))
                .initialDate(invalidParentAccountStatement.getFinalDate())
                .finalDate(invalidParentAccountStatement.getFinalDate().plusDays(1L))
                .build();

        //Each transaction has +Credit value 1
        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(numberOfTransactions, accountStatement);

        AccountStatement childAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);

        String response = accountStatementRobot
                .retryStatement(childAccountStatement.getId())
                .extract()
                .body()
                .asPrettyString();

        assertThat(response)
                .contains(String.format("Statement with identifier - %s is being retried", childAccountStatement.getId()));
    }

    @Test
    void discardStatement_success() throws JsonProcessingException {

        int numberOfStatementsImported = 10;
        int numberOfStatementsInError = 5;
        generateNLinkedAccountStatements(numberOfStatementsImported, numberOfStatementsInError);

        AccountStatementFilters filters = AccountStatementFilters.builder()
                .accountID(aAccount.getId())
                .status(List.of(AccountStatementStatus.REVIEW))
                .build();

        List<AccountStatement> orderedAccountStatementList = accountStatementRobot.fetchByAccountIdOrdered(filters).stream().map(
                accountStatementApiResponsePayload -> accountStatementApiResponsePayload.toEntity(aAccount)
        ).toList();

        assertThat(orderedAccountStatementList.size())
                .isEqualTo(numberOfStatementsInError);

        AccountStatement discardedStatement = orderedAccountStatementList.get(2);

        accountStatementRobot.discardStatement(discardedStatement.getId());

        List<AccountStatement> newOrderedAccountStatementList = accountStatementRobot.fetchByAccountIdOrdered(filters).stream().map(
                accountStatementApiResponsePayload -> accountStatementApiResponsePayload.toEntity(aAccount)
        ).toList();

        assertThat(newOrderedAccountStatementList.size())
                .isEqualTo(numberOfStatementsInError - 1);

        assertThat(newOrderedAccountStatementList.stream().noneMatch(
                accountStatement -> accountStatement.getId().equals(discardedStatement.getId())
        )).isTrue();
    }

    @Test
    void discardStatement_anImportedStatement_BadRequest() throws JsonProcessingException {

        int numberOfStatementsImported = 10;
        int numberOfStatementsInError = 5;
        generateNLinkedAccountStatements(numberOfStatementsImported, numberOfStatementsInError);

        AccountStatementFilters filters = AccountStatementFilters.builder()
                .accountID(aAccount.getId())
                .status(List.of(AccountStatementStatus.IMPORTED, AccountStatementStatus.REVIEW))
                .build();

        List<AccountStatement> orderedAccountStatementList = accountStatementRobot.fetchByAccountIdOrdered(filters).stream().map(
                accountStatementApiResponsePayload -> accountStatementApiResponsePayload.toEntity(aAccount)
        ).toList();

        assertThat(orderedAccountStatementList.size())
                .isEqualTo(numberOfStatementsImported + numberOfStatementsInError);

        AccountStatement discardedStatement = orderedAccountStatementList.get(2);

        assertThat(discardedStatement.getStatus())
                .isEqualTo(AccountStatementStatus.IMPORTED);

        accountStatementRobot.discardStatementFails(discardedStatement.getId());

        List<AccountStatement> newOrderedAccountStatementList = accountStatementRobot.fetchByAccountIdOrdered(filters).stream().map(
                accountStatementApiResponsePayload -> accountStatementApiResponsePayload.toEntity(aAccount)
        ).toList();

        assertThat(newOrderedAccountStatementList.size())
                .isEqualTo(numberOfStatementsImported + numberOfStatementsInError);

        assertThat(newOrderedAccountStatementList.stream().anyMatch(
                accountStatement -> accountStatement.getId().equals(discardedStatement.getId())
        )).isTrue();

    }

    @Test
    void insertStatement_betweenStatementsInError_returnSuccess() throws IOException {
        int numberOfStatementsImported = 10;
        int numberOfStatementsInError = 5;
        generateNLinkedAccountStatements(numberOfStatementsImported, numberOfStatementsInError);

        AccountStatementFilters filters = AccountStatementFilters.builder()
                .accountID(aAccount.getId())
                .status(List.of(AccountStatementStatus.REVIEW))
                .build();

        List<AccountStatement> orderedAccountStatementList = accountStatementRobot.fetchByAccountIdOrdered(filters).stream().map(
                accountStatementApiResponsePayload -> accountStatementApiResponsePayload.toEntity(aAccount)
        ).toList();

        assertThat(orderedAccountStatementList.size())
                .isEqualTo(numberOfStatementsInError);

        AccountStatement previousStatement = orderedAccountStatementList.get(2);
        AccountStatement nextStatement = orderedAccountStatementList.get(3);

        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .statementId("aNewStatementID")
                .previousStatement(previousStatement)
                .initialAmount(previousStatement.getFinalAmount())
                .initialDirection(previousStatement.getFinalDirection())
                .finalDirection(previousStatement.getFinalDirection())
                .finalAmount(previousStatement.getFinalAmount().add(BigDecimal.valueOf(2020)))
                .initialDate(previousStatement.getFinalDate())
                .finalDate(previousStatement.getFinalDate().plusDays(1L))
                .build();

        ClassLoader classLoader = getClass().getClassLoader();
        File file = new File(classLoader.getResource("files/transaction-upload.csv").getFile());
        byte[] fileContent = Files.readAllBytes(file.toPath());
        String fileContentBase64 = Base64.getEncoder().encodeToString(fileContent);

        AccountStatementWithFileApiRequestPayload accountStatementWithFileApiRequestPayload = new AccountStatementWithFileApiRequestPayload(
            accountStatement, fileContentBase64, String.valueOf(nextStatement.getId())
        );

        AccountStatement newStatement = accountStatementRobot.createWithFile(accountStatementWithFileApiRequestPayload).toEntity(aAccount);

        Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
            assert accountStatementRepository.findById(newStatement.getId()).isPresent();
            assertThat(accountStatementRepository.findById(newStatement.getId()).get().getStatus())
                    .isEqualTo(AccountStatementStatus.REVIEW);
        });

        List<AccountStatement> newOrderedAccountStatementList = accountStatementRobot.fetchByAccountIdOrdered(filters).stream().map(
                accountStatementApiResponsePayload -> accountStatementApiResponsePayload.toEntity(aAccount)
        ).toList();

        assertThat(newOrderedAccountStatementList.size())
                .isEqualTo(numberOfStatementsInError + 1);

        assertThat(newOrderedAccountStatementList.stream().anyMatch(
                statement -> statement.getId().equals(newStatement.getId())
        )).isTrue();

        AccountStatement previousStatementInList = newOrderedAccountStatementList.stream()
                .filter(statement -> statement.getId().equals(previousStatement.getId()))
                .findFirst().get();

        AccountStatement newStatementInList = newOrderedAccountStatementList.stream()
                .filter(statement -> statement.getId().equals(newStatement.getId()))
                .findFirst().get();

        AccountStatement nextStatementInList = newOrderedAccountStatementList.stream()
                .filter(statement -> statement.getId().equals(nextStatement.getId()))
                .findFirst().get();

        int previousStatementIndex = newOrderedAccountStatementList.indexOf(previousStatementInList);
        int newStatementIndex = newOrderedAccountStatementList.indexOf(newStatementInList);
        int nextStatementIndex = newOrderedAccountStatementList.indexOf(nextStatementInList);

        assertThat(previousStatementIndex < newStatementIndex).isTrue();
        assertThat(newStatementIndex < nextStatementIndex).isTrue();
    }

    @Test
    void insertStatement_betweenImportedStatements_returnBadRequest() throws IOException {
        int numberOfStatementsImported = 10;
        int numberOfStatementsInError = 5;
        generateNLinkedAccountStatements(numberOfStatementsImported, numberOfStatementsInError);

        AccountStatementFilters filters = AccountStatementFilters.builder()
                .accountID(aAccount.getId())
                .status(List.of(AccountStatementStatus.IMPORTED, AccountStatementStatus.REVIEW))
                .build();

        List<AccountStatement> orderedAccountStatementList = accountStatementRobot.fetchByAccountIdOrdered(filters).stream().map(
                accountStatementApiResponsePayload -> accountStatementApiResponsePayload.toEntity(aAccount)
        ).toList();

        assertThat(orderedAccountStatementList.size())
                .isEqualTo(numberOfStatementsImported + numberOfStatementsInError);

        AccountStatement previousStatement = orderedAccountStatementList.get(2);
        AccountStatement nextStatement = orderedAccountStatementList.get(3);

        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .statementId("aNewStatementID")
                .previousStatement(previousStatement)
                .initialAmount(previousStatement.getFinalAmount())
                .initialDirection(previousStatement.getFinalDirection())
                .finalDirection(previousStatement.getFinalDirection())
                .finalAmount(previousStatement.getFinalAmount().add(BigDecimal.valueOf(10)))
                .initialDate(previousStatement.getFinalDate())
                .finalDate(previousStatement.getFinalDate().plusDays(1L))
                .build();

        ClassLoader classLoader = getClass().getClassLoader();
        File file = new File(classLoader.getResource("files/transaction-upload.csv").getFile());
        byte[] fileContent = Files.readAllBytes(file.toPath());
        String fileContentBase64 = Base64.getEncoder().encodeToString(fileContent);

        AccountStatementWithFileApiRequestPayload accountStatementWithFileApiRequestPayload = new AccountStatementWithFileApiRequestPayload(
            accountStatement, fileContentBase64, String.valueOf(nextStatement.getId())
        );

        accountStatementRobot.createWithFileFails(accountStatementWithFileApiRequestPayload);

    }

    @Test
    void createStatement_whenDifferentCurrency_returnBadRequest() {

        AccountStatement previousStatement = createImportedStatementWithTransactions();

        Currency usdCurrency = createCurrency(FakeCurrencies.USD).toEntity();

        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .statementId("2")
                .previousStatement(previousStatement)
                .currency(usdCurrency)
                .initialAmount(previousStatement.getFinalAmount())
                .initialDirection(previousStatement.getFinalDirection())
                .finalDirection(previousStatement.getFinalDirection())
                .finalAmount(previousStatement.getFinalAmount().add(BigDecimal.valueOf(10)))
                .initialDate(previousStatement.getFinalDate())
                .finalDate(previousStatement.getFinalDate().plusDays(1L))
                .build();

        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(10, accountStatement);

        accountStatementRobot.createFails(accountStatement, transactions);
    }

    //validating fxRates
    @Test
    public void createdTransactionContainsCorrectAmounts() throws EntityErrorsException, DatabaseErrorsException, NotFoundException {
        List<Currency> allCurrencies = new ArrayList<>(FakeCurrencies.ALL_CURRENCIES);
        //NGN is created in the Before each
        allCurrencies.removeIf(currency -> currency.getCode().equals("NGN"));
        allCurrencies.forEach(this::createCurrency);
        insertFxRates(FakeFxRates.FAKE_FX_RATE_LIST);

        Account account = createAccount(aAccount.toBuilder()
                .navReference("*********")
                .accountNumber("*********")
                .currency(Currency.builder()
                        .code("EUR")
                        .build())
                .build());

        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, account).get(0).toBuilder()
                .currency(Currency.builder()
                        .code(account.getCurrency().getCode())
                        .build())
                .build();


        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(10, accountStatement);
        AccountStatement createdAccountStatement = createStatementWithTransactions(account, accountStatement, transactions);
        TransactionFilters transactionFilters = TransactionFilters.builder()
                .accountStatementID(createdAccountStatement.getStatementId())
                .build();
        List<Transaction> createdTransactions = transactionRepository.findAllWithReconciliation(transactionFilters, null, null);

        createdTransactions.forEach(transaction -> {
            assertThat(transaction.getCurrency().getCode()).isNotEqualTo(transaction.getAccountStatement().getAccount().getCountry().getCurrency().getCode());
            assertThat(transaction.getAmountLocalCurrency()).isNotNull();
            assertThat(transaction.getAmount()).isNotNull();
            assertThat(transaction.getAmountUsd()).isNotNull();
        });
    }

    private void generateNLinkedAccountStatements(int numberOfStatements, int numberOfErrors) {
        List<AccountStatement> accountStatements = new ArrayList<>();
        for (int i = 0; i < numberOfStatements; i++) {
            AccountStatement previousStatement = i == 0 ? null : accountStatements.get(i - 1);
            AccountStatement accountStatement = FakeAccountStatements
                    .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                    .statementId(String.valueOf(i + 2))
                    .previousStatement(previousStatement)
                    .initialAmount(BigDecimal.TEN)
                    .initialDirection(Direction.CREDIT)
                    .finalDirection(Direction.CREDIT)
                    .finalAmount(BigDecimal.valueOf(10))
                    .initialDate(previousStatement == null ?
                            LocalDate.now() :
                            previousStatement.getFinalDate().plusDays(1))
                    .finalDate(previousStatement == null ?
                            LocalDate.now() :
                            previousStatement.getFinalDate().plusDays(1L))
                    .flow(AccountStatementFlow.AUTOMATIC)
                    .build();

            List<Transaction> transactionsCredit = FakeTransaction.getFakeCreditTransactions(10, accountStatement);
            List<Transaction> transactionDebit = FakeTransaction.getFakeDebitTransactions(10, accountStatement);
            List<Transaction> transactions = new ArrayList<>();
            transactions.addAll(transactionsCredit);
            transactions.addAll(transactionDebit);

            AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);

            Awaitility.await().atMost(20, TimeUnit.SECONDS).untilAsserted(() -> {
                assert accountStatementRepository.findById(createdAccountStatement.getId()).isPresent();
                assertThat(accountStatementRepository.findById(createdAccountStatement.getId()).get().getStatus())
                        .isEqualTo(AccountStatementStatus.IMPORTED);
            });


            accountStatements.add(createdAccountStatement);
        }

        if (numberOfErrors > 0) {
            for (int i = 0; i < numberOfErrors; i++) {
                AccountStatement previousStatement = accountStatements.isEmpty() ? null : accountStatements.get(accountStatements.size() - 1);
                AccountStatement accountStatement = FakeAccountStatements
                        .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                        .statementId(accountStatements.get(accountStatements.size() - 1).getStatementId() + 1)
                        .previousStatement(previousStatement)
                        .initialAmount(BigDecimal.ZERO)
                        .initialDirection(Direction.CREDIT)
                        .finalDirection(Direction.CREDIT)
                        //error is here vvv (11 instead of 10)
                        .finalAmount(BigDecimal.ZERO)
                        .initialDate(previousStatement == null ? LocalDate.now() : previousStatement.getFinalDate())
                        .finalDate(previousStatement == null ? LocalDate.now().plusDays(1L) : previousStatement.getFinalDate().plusDays(1L))
                        .build();
                List<Transaction> transactionsCredit = FakeTransaction.getFakeCreditTransactions(10, accountStatement);
                List<Transaction> transactionDebit = FakeTransaction.getFakeDebitTransactions(10, accountStatement);
                List<Transaction> transactions = new ArrayList<>();
                transactions.addAll(transactionsCredit);
                transactions.addAll(transactionDebit);

                AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);

                Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
                    assert accountStatementRepository.findById(createdAccountStatement.getId()).isPresent();
                });

                accountStatements.add(createdAccountStatement);
            }
        }
    }

    private AccountStatement createImportedStatementWithTransactions() {

        int numberOfTransactions = 10;

        //Statement has initial amount 10
        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(1, aAccount).get(0).toBuilder()
                .initialAmount(BigDecimal.TEN)
                .initialDirection(Direction.CREDIT)
                .finalDirection(Direction.CREDIT)
                .finalAmount(BigDecimal.valueOf(20))
                .build();

        //Each transaction has +Credit value 1
        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(numberOfTransactions, accountStatement);

        AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);

        Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
            assert accountStatementRepository.findById(createdAccountStatement.getId()).isPresent();
            assertThat(accountStatementRepository.findById(createdAccountStatement.getId()).get().getStatus())
                    .isEqualTo(AccountStatementStatus.IMPORTED);
        });

        return accountStatementRepository.findById(createdAccountStatement.getId()).get();
    }

    private AccountStatement createPeriodOverlapErrorStatementWithTransactions() {

       AccountStatement previousStatement = createImportedStatementWithTransactions();

       int numberOfTransactions = 1;

        AccountStatement accountStatement = FakeAccountStatements
                .getFakeAccountStatements(2, aAccount).get(1).toBuilder()
                .statementId("3")
                .previousStatement(previousStatement)
                .initialAmount(previousStatement.getFinalAmount())
                .initialDirection(previousStatement.getFinalDirection())
                .finalDirection(previousStatement.getFinalDirection())
                .finalAmount(previousStatement.getFinalAmount().add(BigDecimal.valueOf(numberOfTransactions)))
                .initialDate(previousStatement.getFinalDate().minusDays(1L))
                .finalDate(previousStatement.getFinalDate())
                .build();

        //Each transaction has +Credit value 1
        List<Transaction> transactions = FakeTransaction.getFakeCreditTransactions(numberOfTransactions, accountStatement);

        AccountStatement createdAccountStatement = createStatementWithTransactions(aAccount, accountStatement, transactions);

        Awaitility.await().atMost(TIMEOUT_SECONDS, TimeUnit.SECONDS).untilAsserted(() -> {
            assert accountStatementRepository.findById(createdAccountStatement.getId()).isPresent();
            assertThat(accountStatementRepository.findById(createdAccountStatement.getId()).get().getStatus())
                    .isEqualTo(AccountStatementStatus.REVIEW);
        });

        return accountStatementRepository.findById(createdAccountStatement.getId()).get();
    }
}
