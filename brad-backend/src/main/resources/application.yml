# Uncomment to run the application with the fake implementations (repository, network, etc.)
#spring.profiles.active=fake

info.build.version: 1.47.0
server.port: 8080

spring:
  application:
    name: brad
  flyway:
    enabled: false
    locations: db/migration
    schemas: audit, public, quartz
  batch:
    job:
      enabled: false  # Prevent auto-execution, jobs are triggered by Quartz
    jdbc:
      initialize-schema: embedded  # Let Spring Batch manage its own schema



endpoints.default.web.enabled: false

#Spring actuator management configs
management.endpoints.web:
  base-path: / # Force base path to be root for instead of default "/actuator"
  exposure.include: health, prometheus   # Expose only health endpoint

#data
data:
  db:
    driver: org.postgresql.Driver
    url: ***********************************************
    username: postgres
    password: postgres
    application-schema: public
    audit-schema: audit
    quartz-schema: quartz
    max-pool-size: 15
    flyway:
      repair: false

  bale:
    username: sa
    password: bale_local_Password_1!
    application-schema: BRAD
    max-pool-size: 15
    flyway:
      repair: false

  fxrates:
    username: sa
    password: fxrates_local_Password_1!
    application-schema: BRAD
    max-pool-size: 15
    flyway:
      repair: false

  finrec-statements:
      driver: com.microsoft.sqlserver.jdbc.SQLServerDriver
      url: **************************************************************
      username: sa
      password: finrec_statements_local_Password_1!
      application-schema: BRAD
      max-pool-size: 15
      flyway:
          repair: false

  events:
    check-connection-timeout: 15s
#API
api:
  swagger-enabled: true
  self-host: http://localhost:8080/
  allowed-domains: http://localhost:9000,http://localhost:8080,http://localhost:3000,http://localhost:4200

#Network
network:
  jokes.url: http://api.icndb.com
  communication-module:
    url: https://communications-api-dev.jumia.services
    username: brad
    password: EubL7PEVJ5ENV20Qj8PkMrHr17pD6A
    logging: false

#ACL
acl:
  skip: false
  url: http://internal-api-acl-staging.jumia.services
  app-name: BRAD
  cache:
    strategy: in-memory
    in-memory:
      expiration-duration: 5m
    redis:
      host: dev-communications.2smgfr.0001.euw1.cache.amazonaws.com
      port: 6379
      expiration-duration: 5m
      timeout: 0s
  migrator-user:
    username: dummy
    password: dummy

#aws
aws:
  bank-statement-files:
    region: eu-west-1
    bucket-name: cs-finance-cashrec-staging-jaccount
  export-files:
    region: eu-west-1
    bucket-name: cs-finance-brad-exports-staging-jaccount
  region: eu-west-1
  bucket-name: cs-finance-brad-staging-jaccount

#thread pool
jvm.thread-executor:
  pool:
    size: 2
    max-size: 20
    queue-capacity: 5000


