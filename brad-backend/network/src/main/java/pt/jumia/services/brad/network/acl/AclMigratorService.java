package pt.jumia.services.brad.network.acl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.acl.lib.payloads.ApplicationResponsePayload;
import pt.jumia.services.acl.lib.payloads.PermissionRequestPayload;
import pt.jumia.services.acl.lib.payloads.PermissionResponsePayload;
import pt.jumia.services.acl.migrator.AclMigrator;
import pt.jumia.services.acl.migrator.callback.MigrationCallback;
import pt.jumia.services.brad.domain.Permissions;
import pt.jumia.services.brad.domain.properties.AclProperties;
import pt.jumia.services.brad.domain.properties.InfoProperties;

import java.net.HttpURLConnection;
import java.util.HashMap;
import java.util.Map;

import static pt.jumia.services.brad.domain.Permissions.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class AclMigratorService {

    private static final String APPLICATION_TARGET_TYPE = "APPLICATION";
    private static final String COUNTRY_TARGET_TYPE = "COUNTRY";

    private final InfoProperties infoProperties;
    private final AclProperties aclProperties;
    private final AclNetworkRequester aclNetworkRequester;

    public void migrate() {
        try {
            AclMigrator aclMigrator = new AclMigrator(aclNetworkRequester.getAclConnectApiClient());
            RequestUser requestUser = aclNetworkRequester.authorize(
                    aclProperties.getMachineUsers().getMigratorUser().getUsername(),
                    aclProperties.getMachineUsers().getMigratorUser().getPassword());

            aclMigrator.run(
                    requestUser,
                    aclProperties.getAppName(),
                    infoProperties.getBuild().getVersion(),
                    createMigrationCallbacks()
            );
        } catch (AclErrorException e) {
            if (e.getCode() == HttpURLConnection.HTTP_FORBIDDEN) {
                log.error("Application {} does not have the necessary permissions to create new permissions. Migrations were not run," +
                        " fix the application profile before trying again", aclProperties.getAppName());
            } else if (e.getCode() == HttpURLConnection.HTTP_CONFLICT) {
                log.error("Application {} has no current version stored. Migrations were not run, set the application version before" +
                        " trying again", aclProperties.getAppName());
            } else {
                log.error("Unable to authenticate user {}. Migrations were not run.", aclProperties.getMachineUsers()
                        .getMigratorUser().getUsername());
            }
        } catch (Exception e) {
            log.error("Something unexpected happened. Migrations were not run. {}", ExceptionUtils.getStackTrace(e));
        }
    }

    private Map<String, MigrationCallback> createMigrationCallbacks() {
        Map<String, MigrationCallback> migrationMap = new HashMap<>();

        migrationMap.put("0.1.0", (app, user) -> {
            log.info("Running migrations for version 0.1.0");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_CAN_ACCESS, "Can access BRAD");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_ACCESS_ACCOUNTS, "Can access accounts' list");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_MANAGE_ACCOUNTS, "Can register and update accounts");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_DOWNLOAD_ACCOUNTS_CSV,
                    "Can download the list of accounts");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_DELETE_ACCOUNTS, "Can delete an account");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_ADMIN_MANAGE_COUNTRIES, "Can manage countries");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_ACCESS_COUNTRIES, "Can access countries");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_ACCESS_STATEMENTS, "Can access statements");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_ACCESS_RECONCILIATION, "Can access the Reconciliation Menu");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_MANAGE_RECONCILIATION,
                    "Can reconcile transactions on the Reconciliation Menu");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_ACCESS_SCHEDULER, "Can access the scheduler list");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_MANAGE_SCHEDULER,
                    "Can manage scheduler (that fetches NAV BA Ledger Entries)");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_ACCESS_API_LOG, "Can access api log");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_ACCESS_CURRENCIES, "Can access currencies");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_ADMIN_MANAGE_CURRENCIES, "Can manage currencies");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_UPLOAD_STATEMENTS, "Can upload statements");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_APPROVE_RECONCILIATIONS, "Can approve reconciliations");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_UNMATCH_RECONCILIATIONS, "Can unmatch reconciliations");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_EXPORT_RECONCILIATIONS,
                    "Can export transactions (reconciled or not)");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_EXPORT_STATEMENTS, "Can export statements");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_DISCARD_STATEMENTS, "Can discard statements");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_ACCESS_TROUBLESHOOTING, "Can access troubleshooting");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_ACCESS_FX_RATES, "Can access the FX Rates Menu");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_ACCESS_EXECUTION_LOG,
                    "Can access Execution Log Menu (NAV BA Ledger Entries)");
            return true;
        });

        migrationMap.put("0.3.0", (app, user) -> {
            log.info("Running migrations for version 0.2.0");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_EXPORT_STATEMENTS, "Can download statements CSV");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_UPLOAD_STATEMENTS, "Can upload statements");
            return true;
        });

        migrationMap.put("1.6.0", (app, user) -> {
            log.info("Running migrations for version 1.6.0");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_MANAGE_THRESHOLDS, "Can manage Thresholds");
            return true;
        });

        migrationMap.put("1.10.0", (app, user) -> {
            log.info("Running migrations for version 1.10.0");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_MANAGE_VIEW_ENTITIES, "Can manage View Entities");
            return true;
        });

        migrationMap.put("1.16.0", (app, user) -> {
            log.info("Running migrations for version 1.16.0");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_STATEMENT_FILES_ACCESS, "Can access Bank Statement Files");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_SCAN_SFTP_FOLDER, "Can can scan Sftp folder");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_DOWNLOAD_STATEMENT_FILE,
                    "Can can scan download statements CSV");
            return true;
        });

        migrationMap.put("1.21.0", (app, user) -> {
            log.info("Running migrations for version 1.21.0");
            createPermissionWithApplicationTargetType(app, user, SETTING_ACCESS, "Allows the user to access settings ");
            createPermissionWithApplicationTargetType(app, user, SETTING_EDIT, "Allows the user to manage settings ");
            return true;
        });

        migrationMap.put("1.22.0", (app, user) -> {
            log.info("Running migrations for version 1.22.0");
            createPermissionWithApplicationTargetType(app, user, BRAD_DOWNLOAD_CONTACTS_CSV, "Can download the list of bank account contacts");
            createPermissionWithApplicationTargetType(app, user, BRAD_DOWNLOAD_USERS_CSV, "Can download the list of bank account users");
            return true;
        });

        migrationMap.put("1.28.0", (app, user) -> {
            log.info("Running migrations for version 1.28.0");
            createPermissionWithApplicationTargetType(app, user, Permissions.BRAD_DISCARD_IMPORTED_STATEMENTS,
                    "Can discard imported statements");
            return true;
        });

        migrationMap.put("1.33.0", (app, user) -> {
            log.info("Running migrations for version 1.33.0");
            createPermissionWithCountryTargetType(app, user, BRAD_ACCESS_EXPORT_LOG,
                    "Can access export logs");
            return true;
        });

        migrationMap.put("1.40.0", (app, user) -> {
            log.info("Running migrations for version 1.40.0");
            createPermissionWithCountryTargetType(app, user, BRAD_CHANGE_STATEMENTS,
                    "Can update statement status and description");
            return true;
        });

        migrationMap.put("1.44.0", (app, user) -> {
            log.info("Running migrations for version 1.44.0");

            createPermissionWithCountryTargetType(app, user, BRAD_RETRY_STATEMENT,
                "Allows the user to retry a statement");
            return true;
        });

        return migrationMap;
    }

    private void createPermissionWithApplicationTargetType(ApplicationResponsePayload application,
                                  RequestUser requestUser,
                                  String permissionCode,
                                  String description){
        createPermission(application, requestUser, permissionCode, description, APPLICATION_TARGET_TYPE);

    }
    private void createPermissionWithCountryTargetType(ApplicationResponsePayload application,
                                                           RequestUser requestUser,
                                                           String permissionCode,
                                                           String description){
        createPermission(application, requestUser, permissionCode, description, COUNTRY_TARGET_TYPE);

    }

    private void createPermission(ApplicationResponsePayload application,
                                  RequestUser requestUser,
                                  String permissionCode,
                                  String description,
                                  String targetType) {

        PermissionRequestPayload permissionRequest = new PermissionRequestPayload();

        permissionRequest.setApplication(application.getId());
        permissionRequest.setCode(permissionCode);
        permissionRequest.setTargetType(targetType);
        permissionRequest.setDescription(description);

        try {
            PermissionResponsePayload permission =
                    aclNetworkRequester.getAclConnectApiClient().management().permissions().create(requestUser, permissionRequest);
            log.info("Created permission: {}", permission.toString());
        } catch (AclErrorException e) {

            if (Integer.valueOf(HttpURLConnection.HTTP_CONFLICT).equals(e.getCode())) {
                log.info("Permission {} already exists, skipping.", permissionRequest.getCode());
            } else {
                log.error("Permission creation failed: {}", e.getMessage());
                throw (e);
            }
        }
    }
}
