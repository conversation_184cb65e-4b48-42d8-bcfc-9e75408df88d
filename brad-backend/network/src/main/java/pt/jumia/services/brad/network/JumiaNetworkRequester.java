package pt.jumia.services.brad.network;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.NetworkRequester;
import pt.jumia.services.brad.domain.Profiles;
import pt.jumia.services.brad.domain.entities.dtos.AccountTroubleshootingDto;
import pt.jumia.services.brad.network.communicationmodule.CommunicationsModuleNetworkRequester;
import pt.jumia.services.brad.network.communicationmodule.payloads.EmailNotificationPayload;

import java.io.IOException;
import java.util.List;

/**
 * {@link NetworkRequester} implementation that will actually fetch the data from the external servers
 */
@Component
@RequiredArgsConstructor
@Profile("!" + Profiles.FAKE_CLIENTS)
public class JumiaNetworkRequester implements NetworkRequester {

    private final CommunicationsModuleNetworkRequester communicationsModuleNetworkRequester;

    @Override
    public void sendEmailForAccountsInTroubleshooting(List<AccountTroubleshootingDto> accounts, String emailAddress) throws IOException {

        communicationsModuleNetworkRequester.sendEmail(
            EmailNotificationPayload.builder().email(emailAddress).accountsInTroubleShooting(accounts).build());

    }
}
