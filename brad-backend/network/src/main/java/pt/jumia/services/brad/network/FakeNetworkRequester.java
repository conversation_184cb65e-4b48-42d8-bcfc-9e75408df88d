package pt.jumia.services.brad.network;

import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.NetworkRequester;
import pt.jumia.services.brad.domain.Profiles;
import pt.jumia.services.brad.domain.entities.dtos.AccountTroubleshootingDto;

import java.io.IOException;
import java.util.List;

/**
 * Fake implementation of the {@link NetworkRequester}, which can be used for tests and dev environment, when you want
 * to mock the network requests and inject data at your will
 */
@Component
@Profile(Profiles.FAKE_CLIENTS)
public class FakeNetworkRequester implements NetworkRequester {

    @Override
    public void sendEmailForAccountsInTroubleshooting(final List<AccountTroubleshootingDto> accounts, final String emailAddress)
        throws IOException {

    }

}
