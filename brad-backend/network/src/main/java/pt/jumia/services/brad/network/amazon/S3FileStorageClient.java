package pt.jumia.services.brad.network.amazon;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.brad.domain.FileStorageClient;
import pt.jumia.services.brad.domain.Profiles;
import pt.jumia.services.brad.domain.RequestContext;
import pt.jumia.services.brad.domain.entities.AccountStatementFile;
import pt.jumia.services.brad.domain.entities.AccountStatementFile.ProcessingStatus;
import pt.jumia.services.brad.domain.entities.BradFileType;
import pt.jumia.services.brad.domain.entities.dtos.AccountStatementFilesScanResponseDto;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.http.HttpStatusCode;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.GetUrlRequest;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;
import software.amazon.awssdk.services.s3.model.S3Object;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

import java.net.URL;
import java.time.Duration;
import java.util.*;
import java.io.File;
import java.io.FileOutputStream;


@Component
@Slf4j
@Profile("!" + Profiles.FAKE_CLIENTS)
@SuppressWarnings("PMD.CloseResource")
public class S3FileStorageClient implements FileStorageClient {

    // only allow alphanumeric and '.' and '@'
    private static final String NOT_ALLOWED_CHARS_FILENAME = "[^a-zA-Z0-9.@]+";
    private static final String S3_LINK_FORMAT = "https://%s.s3.amazonaws.com/%s";
    private final S3Client sftpS3Client;
    private final AwsProperties awsSftpBucketProperties;
    private final S3Presigner sftpS3BucketPresigner;
    private final S3Presigner s3PresignerBankStatementFile;

    private final S3Client exportsS3Client;
    private final AwsProperties awsExportsBucketProperties;
    private final S3Presigner exportsS3BucketPresigner;
    private final S3Presigner s3PresignerExports;

    public S3FileStorageClient(AwsProperties awsProperties) {

        // Bank statements SFTP Folder
        this.awsSftpBucketProperties = new AwsProperties();
        this.awsSftpBucketProperties.setBankStatementFiles(awsProperties.getBankStatementFiles());
        this.awsSftpBucketProperties.setRegion(awsProperties.getRegion());
        this.awsSftpBucketProperties.setBucketName(awsProperties.getBucketName());
        this.awsSftpBucketProperties.setAccessKey(awsProperties.getAccessKey());
        this.awsSftpBucketProperties.setSecretKey(awsProperties.getSecretKey());

        this.sftpS3Client = S3Client.builder()
                .region(awsProperties.getRegion())
                .credentialsProvider(() -> AwsBasicCredentials.create(awsProperties.getAccessKey(), awsProperties.getSecretKey()))
                .build();
        this.sftpS3BucketPresigner = S3Presigner.builder()
                .region(awsProperties.getRegion())
                .credentialsProvider(() -> AwsBasicCredentials.create(awsProperties.getAccessKey(), awsProperties.getSecretKey()))
                .build();
        this.s3PresignerBankStatementFile = Optional.ofNullable(awsProperties.getBankStatementFiles())
                .map(files -> S3Presigner.builder()
                        .region(awsProperties.getBankStatementFiles().getRegion())
                        .credentialsProvider(() -> AwsBasicCredentials.create(awsProperties.getBankStatementFiles().getAccessKey(),
                                awsProperties.getBankStatementFiles().getSecretKey()))
                        .build())
                .orElse(null);

        // Exports Folder
        this.awsExportsBucketProperties = new AwsProperties();
        this.awsExportsBucketProperties.setRegion(awsProperties.getExportFiles().getRegion());
        this.awsExportsBucketProperties.setBucketName(awsProperties.getExportFiles().getBucketName());
        this.awsExportsBucketProperties.setAccessKey(awsProperties.getExportFiles().getAccessKey());
        this.awsExportsBucketProperties.setSecretKey(awsProperties.getExportFiles().getSecretKey());

        this.exportsS3Client = S3Client.builder()
                .region(awsProperties.getExportFiles().getRegion())
                .credentialsProvider(() ->
                        AwsBasicCredentials.create(awsProperties.getExportFiles().getAccessKey(), awsProperties.getExportFiles().getSecretKey()))
                .build();
        this.exportsS3BucketPresigner = S3Presigner.builder()
                .region(awsProperties.getExportFiles().getRegion())
                .credentialsProvider(() ->
                        AwsBasicCredentials.create(awsProperties.getExportFiles().getAccessKey(), awsProperties.getExportFiles().getSecretKey()))
                .build();
        this.s3PresignerExports = Optional.ofNullable(awsProperties.getBankStatementFiles())
                .map(files -> S3Presigner.builder()
                        .region(awsProperties.getExportFiles().getRegion())
                        .credentialsProvider(() -> AwsBasicCredentials.create(awsProperties.getExportFiles().getAccessKey(),
                                awsProperties.getExportFiles().getSecretKey()))
                        .build())
                .orElse(null);
    }

    @Override
    public String uploadCsv(BradFileType fileType, File file, String prefix) {
        String fileName = generateFileName(file.getName(), prefix);

        S3Client client = getAwsClient(fileType);
        AwsProperties properties = getAwsProperties(fileType);

        client.putObject(PutObjectRequest
                        .builder()
                        .bucket(properties.getBucketName())
                        .key(fileName)
                        .build(),
                RequestBody.fromFile(file));

        return String.format(S3_LINK_FORMAT, properties.getBucketName(), fileName);
    }


    @Override
    public Pair<String, String> uploadFileFromBase64(BradFileType fileType, String fileStr, String directory) throws UserForbiddenException {

        String fileKey = directory + "/" + UUID.randomUUID();
        String base64Str = fileStr.split(",")[1];
        byte[] decodedFileBytes = Base64.getDecoder().decode(base64Str);

        String bucketName = awsSftpBucketProperties.getBucketName();
        if (BradFileType.EXPORT.equals(fileType)) {
            bucketName = awsExportsBucketProperties.getBucketName();
        }

        try {
            sftpS3Client.putObject(
                    PutObjectRequest.builder()
                            .bucket(bucketName)
                            .key(fileKey)
                            .build(),
                    RequestBody.fromBytes(decodedFileBytes));
        } catch (S3Exception e) {
            log.error("Error uploading the file {} to storage: {}", fileKey, ExceptionUtils.getStackTrace(e));
            if (e.statusCode() == HttpStatusCode.FORBIDDEN) {
                throw UserForbiddenException.createAwsS3ForbiddenException(RequestContext.getUser().getUsername(),
                        bucketName, fileKey);
            }
            throw e;
        }
        log.info("Successfully uploaded file to the S3 Bucket");

        return Pair.of(String.format(S3_LINK_FORMAT, bucketName, fileKey), fileKey);
    }

    @Override
    public String getTempDownloadUrl(BradFileType fileType, String key, String fileName) {
        String bucket = awsSftpBucketProperties.getBucketName();
        if (BradFileType.EXPORT.equals(fileType)) {
            bucket = awsExportsBucketProperties.getBucketName();
        }

        S3Presigner s3Presigner = exportsS3BucketPresigner;
        if (BradFileType.EXPORT.equals(fileType)) {
            s3Presigner = s3PresignerExports;
        }
        return getTempDownloadUrlProperties(key, fileName, bucket, s3Presigner);
    }

    @Override
    public String getTempDownloadUrlAccountStatementFiles(String key, String fileName) {
        final String bucket = awsSftpBucketProperties.getBankStatementFiles().getBucketName();
        return getTempDownloadUrlProperties(key, fileName, bucket, s3PresignerBankStatementFile);
    }

    @Override
    public AccountStatementFilesScanResponseDto scanBucketPaginated(int pageSize, String continuationToken) throws UserForbiddenException {

        List<AccountStatementFile> accountStatementFiles = new ArrayList<>();

        final String bucket = awsSftpBucketProperties.getBankStatementFiles().getBucketName();

        final S3Client client = S3Client.builder()
                .region(awsSftpBucketProperties.getBankStatementFiles().getRegion())
                .credentialsProvider(() -> AwsBasicCredentials.create(awsSftpBucketProperties.getBankStatementFiles().getAccessKey(),
                        awsSftpBucketProperties.getBankStatementFiles().getSecretKey()))
                .build();

        try {
            log.info("Scanning S3 bucket {} with page size {}", bucket, pageSize);
            ListObjectsV2Request request = buildRequest(pageSize, continuationToken, bucket);

            String nextContinuationToken = doScan(pageSize, client, request, bucket, accountStatementFiles);

            return buildBankStatementFileResponseDto(pageSize, accountStatementFiles, nextContinuationToken);

        } catch (S3Exception exception) {
            log.error("Error scanning s3 bucket {}", ExceptionUtils.getStackTrace(exception));
            if (exception.statusCode() == HttpStatusCode.FORBIDDEN) {
                throw UserForbiddenException.createAwsS3ForbiddenException(RequestContext.getUser().getUsername(), bucket);
            }
            throw exception;
        } finally {
            client.close();
        }
    }

    @Override
    public File getaccountStatementFile(String key) throws Exception {

        final String bucket = awsSftpBucketProperties.getBankStatementFiles().getBucketName();

        final S3Client client = S3Client.builder()
            .region(awsSftpBucketProperties.getBankStatementFiles().getRegion())
            .credentialsProvider(() -> AwsBasicCredentials.create(awsSftpBucketProperties.getBankStatementFiles().getAccessKey(),
                awsSftpBucketProperties.getBankStatementFiles().getSecretKey()))
            .build();

        final File file = new File(key.replace("/", "-"));
        FileOutputStream fos = new FileOutputStream(file);

        try {
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(bucket)
                .key(key)
                .build();

            ResponseBytes<GetObjectResponse> objectBytes = client.getObjectAsBytes(getObjectRequest);
            byte[] data = objectBytes.asByteArray();

            fos.write(data);

            return file;

        } catch (S3Exception exception) {
            log.error("Error Reading contents of file with key{} {}", key, ExceptionUtils.getStackTrace(exception));
            if (exception.statusCode() == HttpStatusCode.FORBIDDEN) {
                throw UserForbiddenException.createAwsS3ForbiddenException(RequestContext.getUser().getUsername(), bucket);
            }
            throw exception;
        } finally {
            client.close();
            fos.close();
        }
    }

    private String doScan(final int pageSize, final S3Client client, final ListObjectsV2Request request, final String bucket,
                          final List<AccountStatementFile> accountStatementFiles) {

        ListObjectsV2Response response;
        String nextContinuationToken;
        do {
            response = client.listObjectsV2(request);

            for (S3Object object : response.contents()) {

                AccountStatementFile file = AccountStatementFile.builder()
                        .name(object.key())
                        .url(getUrl(client, object, bucket).toString())
                        .checksum(calculateChecksum(client, object, bucket))
                        .processingStatus(ProcessingStatus.NEW)
                        .statusDescription(ProcessingStatus.NEW.getDescription())
                        .build();

                accountStatementFiles.add(file);
            }

            request.toBuilder().continuationToken(response.nextContinuationToken());
            nextContinuationToken = response.nextContinuationToken();

        } while (Boolean.TRUE.equals(response.isTruncated()) && accountStatementFiles.size() < pageSize);
        return nextContinuationToken;
    }

    private String calculateChecksum(final S3Client client, final S3Object object, final String bucket) {

        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(bucket)
                .key(object.key())
                .build();

        ResponseBytes<GetObjectResponse> objectBytes = client.getObjectAsBytes(getObjectRequest);
        byte[] data = objectBytes.asByteArray();

        return DigestUtils.sha256Hex(data);
    }

    private URL getUrl(final S3Client client, final S3Object object, String bucket) {

        GetUrlRequest urlRequest = GetUrlRequest.builder()
                .bucket(bucket)
                .key(object.key())
                .build();

        return client.utilities().getUrl(urlRequest);
    }

    private static AccountStatementFilesScanResponseDto buildBankStatementFileResponseDto(final int pageSize,
                                                                                       final List<AccountStatementFile> accountStatementFiles,
                                                                                       final String nextContinuationToken) {

        return AccountStatementFilesScanResponseDto.builder()
                .accountStatementFiles(accountStatementFiles)
                .pageSize(pageSize)
                .nextPageToken(nextContinuationToken)
                .build();
    }

    private static ListObjectsV2Request buildRequest(final int pageSize, final String continuationToken, final String bucket) {

        return ListObjectsV2Request.builder()
                .bucket(bucket)
                .maxKeys(pageSize)
                .continuationToken(continuationToken)
                .build();
    }

    private static String getTempDownloadUrlProperties(String key, String fileName, String bucket, S3Presigner s3Presigner) {

        GetObjectRequest getObjectRequest =
                GetObjectRequest.builder()
                        .bucket(bucket)
                        .key(key)
                        .responseContentDisposition("attachment; filename=" + fileName)
                        .build();

        GetObjectPresignRequest getObjectPresignRequest =
                GetObjectPresignRequest.builder()
                        .signatureDuration(Duration.ofSeconds(15))
                        .getObjectRequest(getObjectRequest)
                        .build();


        try {
            PresignedGetObjectRequest presignedGetObjectRequest = s3Presigner.presignGetObject(getObjectPresignRequest);
            return presignedGetObjectRequest.url().toString();
        } catch (S3Exception e) {
            log.error("Error downloading the file {} from storage: {}", fileName, ExceptionUtils.getStackTrace(e));
            throw e;
        }
    }

    private String generateFileName(String filename, String prefix) {
        String dateString = DateUtil.convertToDateString(new Date());
        String finalFilename;
        if (RequestContext.getUsername() != null) {
            finalFilename = (prefix + RequestContext.getUsername() + "_" + dateString + "_" + filename)
                    .replaceAll(NOT_ALLOWED_CHARS_FILENAME, "_");
        } else {
            finalFilename = (prefix + dateString + "_" + filename)
                    .replaceAll(NOT_ALLOWED_CHARS_FILENAME, "_");
        }
        return finalFilename;
    }

    private S3Client getAwsClient(BradFileType fileType) {
        if (BradFileType.EXPORT.equals(fileType)) {
            return exportsS3Client;
        } else {
            return sftpS3Client;
        }
    }

    private AwsProperties getAwsProperties(BradFileType fileType) {
        if (BradFileType.EXPORT.equals(fileType)) {
            return awsExportsBucketProperties;
        } else {
            return awsSftpBucketProperties;
        }
    }
}
