package pt.jumia.services.brad.network;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.network.communicationmodule.CommunicationsModuleClient;
import pt.jumia.services.brad.network.jokes.JokesClient;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.text.SimpleDateFormat;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * Factory that creates our Retrofit instances, so that we can make HTTP requests
 */
@Component
public class NetworkClientFactory {

    private static final boolean LOGGING = false;

    public JokesClient createJokesClient(String endpoint) {

        return createRetrofitInstance(endpoint, createOkHttpInstance())
            .create(JokesClient.class);
    }

    public CommunicationsModuleClient createCommunicationModuleClient(String endpoint) {

        return createRetrofitInstance(endpoint, createOkHttpInstance())
            .create(CommunicationsModuleClient.class);
    }

    @NotNull
    private OkHttpClient.Builder createOkHttpInstance() {

        HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor();
        interceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        OkHttpClient.Builder builder = new OkHttpClient.Builder()
            .readTimeout(30, TimeUnit.SECONDS);
        if (!LOGGING) {
            return builder;
        } else {
            return builder
                .addInterceptor(interceptor);
        }
    }

    @NotNull
    private Retrofit createRetrofitInstance(String endpoint, OkHttpClient.Builder okHttpBuilder) {

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.ROOT));

        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return new Retrofit.Builder()
            .baseUrl(endpoint)
            .addConverterFactory(JacksonConverterFactory.create(objectMapper))
            .client(okHttpBuilder.build())
            .build();
    }


}
