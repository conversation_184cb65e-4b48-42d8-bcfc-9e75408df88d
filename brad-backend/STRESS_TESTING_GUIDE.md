# Bale Sync Stress Testing Guide

This guide provides comprehensive instructions for stress testing the Bale Sync process to ensure it performs reliably under various load conditions.

## Overview

The Bale Sync stress testing framework validates:

- **Performance**: Throughput, latency, and resource utilization
- **Memory Management**: Memory usage patterns, garbage collection, leak detection
- **Concurrency**: Thread safety, deadlock detection, race conditions
- **Reliability**: Error handling, recovery mechanisms, data consistency
- **Scalability**: Performance under increasing load

## Quick Start

### Running Individual Tests

```bash
# Run the focused stress test
./gradlew :domain:test --tests "BaleStressTest"

# Run comprehensive stress test suite  
./gradlew :domain:test --tests "BaleStressTestSuite"

# Run specific test methods
./gradlew :domain:test --tests "BaleStressTest.testLargeDatasetProcessing"
./gradlew :domain:test --tests "BaleStressTest.testConcurrentProcessing"
```

### Running with Different Configurations

```bash
# Test with high memory pressure
./gradlew :domain:test --tests "BaleStressTest" -Xmx512m

# Test with limited CPU cores
./gradlew :domain:test --tests "BaleStressTest" -DjvmArgs="-XX:ActiveProcessorCount=2"

# Test with streaming enabled
./gradlew :domain:test --tests "BaleStressTest" -Dbrad.bale.sync.use-streaming=true
```

## Test Scenarios

### 1. Large Dataset Processing Test

**Purpose**: Validates performance with large volumes of data

**Configuration**:
- Dataset size: 100,000+ bales
- Multiple view entities
- Both traditional and streaming modes

**Success Criteria**:
- Throughput > 1000 bales/second
- Execution time < 5 minutes
- Memory usage stable

**Command**:
```bash
./gradlew :domain:test --tests "BaleStressTest.testLargeDatasetProcessing"
```

### 2. Memory Constraint Test

**Purpose**: Validates behavior under memory pressure

**Configuration**:
- Limited heap size (512MB)
- Large batch sizes
- Memory threshold monitoring

**Success Criteria**:
- No OutOfMemoryErrors
- Memory usage < 80% of heap
- Graceful degradation

**Command**:
```bash
./gradlew :domain:test --tests "BaleStressTest.testMemoryThresholdHandling" -Xmx512m
```

### 3. Concurrent Processing Test

**Purpose**: Validates thread safety and concurrent performance

**Configuration**:
- 10+ concurrent threads
- Shared resources
- Race condition detection

**Success Criteria**:
- All threads complete successfully
- No deadlocks or race conditions
- Linear performance scaling

**Command**:
```bash
./gradlew :domain:test --tests "BaleStressTest.testConcurrentProcessing"
```

### 4. Batch Optimization Test

**Purpose**: Finds optimal batch configuration

**Configuration**:
- Various batch sizes (100, 500, 1000, 2000, 5000)
- Performance measurement
- Resource utilization tracking

**Success Criteria**:
- Identifies optimal batch size
- Performance improvement visible
- Resource usage reasonable

**Command**:
```bash
./gradlew :domain:test --tests "BaleStressTest.testBatchProcessingOptimization"
```

### 5. Streaming Performance Test

**Purpose**: Validates streaming mode efficiency

**Configuration**:
- Large datasets (50,000+ bales)
- Constant memory usage
- Streaming orchestrator

**Success Criteria**:
- Constant memory usage
- Comparable throughput to traditional mode
- No memory leaks

**Command**:
```bash
./gradlew :domain:test --tests "BaleStressTest.testStreamingProcessingPerformance"
```

## Performance Benchmarks

### Expected Performance Metrics

| Metric | Minimum | Target | Excellent |
|--------|---------|--------|-----------|
| Throughput | 500 bales/sec | 1000 bales/sec | 2000+ bales/sec |
| Memory Usage | < 1GB | < 512MB | < 256MB |
| CPU Usage | < 90% | < 70% | < 50% |
| Error Rate | < 5% | < 1% | < 0.1% |

### Batch Size Recommendations

| Dataset Size | Recommended Batch Size | Memory Impact | Performance |
|--------------|------------------------|---------------|-------------|
| < 10K bales | 500 | Low | Good |
| 10K - 100K bales | 1000 | Medium | Optimal |
| 100K - 1M bales | 2000 | High | Excellent |
| > 1M bales | 5000 + Streaming | Very High | Maximum |

## Monitoring During Tests

### JVM Monitoring

```bash
# Monitor JVM metrics during test execution
jstat -gc -t [PID] 5s

# Monitor memory usage
jmap -histo [PID]

# Monitor thread dumps
jstack [PID]
```

### Application Monitoring

```bash
# Monitor database connections
netstat -an | grep :5432 | wc -l

# Monitor CPU usage
top -p [PID]

# Monitor memory usage
ps -p [PID] -o pid,ppid,cmd,%mem,%cpu
```

### Log Analysis

```bash
# Monitor for memory warnings
grep -i "memory" logs/application.log

# Monitor for performance issues
grep -i "slow\|timeout\|failed" logs/application.log

# Monitor batch processing
grep "Bale sync:" logs/application.log | tail -100
```

## Troubleshooting Common Issues

### OutOfMemoryError

**Symptoms**:
- Tests fail with OOM errors
- Heap dumps generated
- Performance degradation

**Solutions**:
1. Increase heap size: `-Xmx2g`
2. Enable streaming mode
3. Reduce batch sizes
4. Enable G1GC: `-XX:+UseG1GC`

### Poor Performance

**Symptoms**:
- Low throughput
- High CPU usage
- Long execution times

**Solutions**:
1. Optimize batch sizes
2. Check database connection pool
3. Enable parallel processing
4. Review SQL query performance

### Deadlocks

**Symptoms**:
- Tests hang indefinitely
- Thread dumps show blocked threads
- Database lock timeouts

**Solutions**:
1. Review transaction boundaries
2. Implement proper timeout handling
3. Use optimistic locking
4. Reduce concurrent thread count

### Memory Leaks

**Symptoms**:
- Memory usage continuously increases
- GC frequency increases
- Performance degrades over time

**Solutions**:
1. Enable heap dump on OOM: `-XX:+HeapDumpOnOutOfMemoryError`
2. Use memory profiler (VisualVM, JProfiler)
3. Review object retention
4. Check for static collections

## Environment Configuration

### Development Environment

```properties
# application-stress-test.yml
bale:
  batch:
    enabled: true
    size: 1000
    maxBatches: 100
    memoryThreshold: 0.7
  errorHandling:
    maxErrorsInMemory: 500
    errorThreshold: 0.1
```

### CI/CD Environment

```properties
# application-ci.yml
bale:
  batch:
    enabled: true
    size: 500
    maxBatches: 50
    memoryThreshold: 0.6
  errorHandling:
    maxErrorsInMemory: 100
    errorThreshold: 0.05
```

### Production Environment

```properties
# application-prod.yml
bale:
  batch:
    enabled: true
    size: 2000
    maxBatches: 1000
    memoryThreshold: 0.8
  errorHandling:
    maxErrorsInMemory: 1000
    errorThreshold: 0.02
```

## Automated Testing

### Jenkins Pipeline

```groovy
pipeline {
    agent any
    stages {
        stage('Stress Test') {
            steps {
                script {
                    sh '''
                        ./gradlew :domain:test --tests "BaleStressTest" \
                        -Xmx1g -XX:+UseG1GC \
                        --info
                    '''
                }
            }
            post {
                always {
                    publishTestResults testResultsPattern: '**/TEST-*.xml'
                    archiveArtifacts artifacts: '**/hs_err_pid*.log', allowEmptyArchive: true
                }
            }
        }
    }
}
```

### GitHub Actions

```yaml
name: Stress Test
on:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM
  workflow_dispatch:

jobs:
  stress-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
      
      - name: Run Stress Tests
        run: |
          ./gradlew :domain:test --tests "BaleStressTest*" \
          -Xmx2g -XX:+UseG1GC
        
      - name: Upload Test Results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: stress-test-results
          path: |
            **/build/reports/tests/
            **/hs_err_pid*.log
```

## Performance Tuning

### JVM Tuning

```bash
# For large datasets
-Xmx4g -Xms2g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError

# For memory-constrained environments
-Xmx512m -Xms256m
-XX:+UseSerialGC
-XX:MaxMetaspaceSize=128m
```

### Application Tuning

```properties
# Database connection pool
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000

# Threading
spring.task.execution.pool.core-size=8
spring.task.execution.pool.max-size=16
spring.task.execution.pool.queue-capacity=100
```

## Reporting

After running stress tests, review the generated reports:

1. **Test Results**: `build/reports/tests/test/index.html`
2. **Performance Metrics**: Console output and logs
3. **Memory Analysis**: Heap dumps (if generated)
4. **System Metrics**: Monitor dashboards

The stress testing framework provides comprehensive validation of the Bale Sync process under various conditions, ensuring reliable performance in production environments.
